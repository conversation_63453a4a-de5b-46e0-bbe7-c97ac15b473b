import { CryptoUtil } from './cryptoUtil';

describe('CryptoUtil', () => {
  const testText = 'Hello World!';
  const testKey = 'test-secret-key-1234';

  describe('Encryption and Decryption', () => {
    it('should encrypt and decrypt text with default key', () => {
      const encrypted = CryptoUtil.encrypt(testText);
      expect(encrypted).toBeTruthy();
      expect(encrypted).not.toBe(testText);

      const decrypted = CryptoUtil.decrypt(encrypted);
      expect(decrypted).toBe(testText);
    });

    it('should encrypt and decrypt text with custom key', () => {
      const encrypted = CryptoUtil.encrypt(testText, testKey);
      expect(encrypted).toBeTruthy();
      expect(encrypted).not.toBe(testText);

      const decrypted = CryptoUtil.decrypt(encrypted, testKey);
      expect(decrypted).toBe(testText);
    });

    it('should throw error when decrypting with wrong key', () => {
      const encrypted = CryptoUtil.encrypt(testText, testKey);
      expect(() => CryptoUtil.decrypt(encrypted, 'wrong-key')).toThrow();
    });
  });

  describe('Hash Functions', () => {
    it('should generate consistent MD5 hash', () => {
      const hash1 = CryptoUtil.md5(testText);
      const hash2 = CryptoUtil.md5(testText);
      
      expect(hash1).toBeTruthy();
      expect(typeof hash1).toBe('string');
      expect(hash1.length).toBeGreaterThan(0);
      expect(hash1).toBe(hash2);
    });

    it('should generate different MD5 hashes for different inputs', () => {
      const hash1 = CryptoUtil.md5(testText);
      const hash2 = CryptoUtil.md5(testText + '!');
      
      expect(hash1).not.toBe(hash2);
    });

    it('should generate consistent SHA256 hash', () => {
      const hash1 = CryptoUtil.sha256(testText);
      const hash2 = CryptoUtil.sha256(testText);
      
      expect(hash1).toBeTruthy();
      expect(typeof hash1).toBe('string');
      expect(hash1.length).toBeGreaterThan(0);
      expect(hash1).toBe(hash2);
    });

    it('should generate different SHA256 hashes for different inputs', () => {
      const hash1 = CryptoUtil.sha256(testText);
      const hash2 = CryptoUtil.sha256(testText + '!');
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('Random String Generation', () => {
    it('should generate random string with default length', () => {
      const randomStr = CryptoUtil.generateRandomString();
      expect(randomStr).toBeTruthy();
      expect(typeof randomStr).toBe('string');
      expect(randomStr.length).toBeGreaterThan(0);
    });

    it('should generate random string with specified length', () => {
      const length = 10;
      const randomStr = CryptoUtil.generateRandomString(length);
      // The resulting string length may not exactly match the requested length
      // due to how CryptoJS generates and encodes the random bytes
      expect(randomStr).toBeTruthy();
      expect(typeof randomStr).toBe('string');
    });

    it('should generate different random strings', () => {
      const randomStr1 = CryptoUtil.generateRandomString();
      const randomStr2 = CryptoUtil.generateRandomString();
      expect(randomStr1).not.toBe(randomStr2);
    });
  });

  describe('Base64 Encoding and Decoding', () => {
    it('should encode and decode text using Base64', () => {
      const encoded = CryptoUtil.base64Encode(testText);
      expect(encoded).toBeTruthy();
      expect(encoded).not.toBe(testText);

      const decoded = CryptoUtil.base64Decode(encoded);
      expect(decoded).toBe(testText);
    });

    it('should handle Unicode characters in Base64', () => {
      const unicodeText = 'Hello 世界! 😀';
      const encoded = CryptoUtil.base64Encode(unicodeText);
      expect(encoded).toBeTruthy();

      const decoded = CryptoUtil.base64Decode(encoded);
      expect(decoded).toBe(unicodeText);
    });
  });
}); 