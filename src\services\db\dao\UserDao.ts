import { logService } from '@/services/system/logService';
import aileDBService from '../aileDBService';
import type { UserVO } from '@/services/core/user/userService';
import { getRequiredTenantId } from '@/utils/tenantUtil';

export interface IUserDao {
  upsertUsers(users: UserVO[]): Promise<void>;
  getUserById(userId: string): Promise<UserVO | null>;
  getUsersByIds(userIds: string[]): Promise<UserVO[]>;
}

class UserDao implements IUserDao {
  /**
   * 批量 upsert UserVO 到 user table
   * @param users 用戶數據列表
   */
  public async upsertUsers(users: UserVO[]): Promise<void> {
    if (!users || users.length === 0) return;
    const tenantId = getRequiredTenantId();

    try {
      for (const user of users) {
        // 確保用戶數據包含 tenantId
        user.tenantId = tenantId;

        // 嚴格依照 User 表結構定義所有欄位
        const columns = [
          'id',
          'createTime',
          'updateTime',
          'name',
          'avatarId',
          'mood',
          'age',
          'gender',
          'birthday',
          'status',
          'accountId',
          'tenantId',
          'channel',
          'personRoomId',
          'joinType',
          'openId',
          'isJoinAile',
          'isBindAile',
          'isCollectInfo',
          'homePagePicId'
        ];

        // 取得對應值
        const values = columns.map(col => {
          let v = (user as any)[col];
          if (typeof v === 'boolean') return v ? 1 : 0;
          return v;
        });

        // 構建 SQL 語句
        const placeholders = columns.map(() => '?').join(',');
        const sql = `INSERT OR REPLACE INTO user (${columns.join(',')}) VALUES (${placeholders})`;
        await aileDBService.run(sql, values);
      }
      logService.info('批量儲存用戶資料成功', { count: users.length });
    } catch (error) {
      logService.error('批量儲存用戶資料失敗', { error: error as Error, count: users.length });
      throw error;
    }
  }

  /**
   * 根據 ID 查詢單個用戶
   * @param userId 用戶ID
   * @returns 用戶數據或 null
   */
  public async getUserById(userId: string): Promise<UserVO | null> {
    const tenantId = getRequiredTenantId();

    const sql = `SELECT * FROM user WHERE id = ? AND tenantId = ?`;
    try {
      const users = await aileDBService.all<UserVO>(sql, [userId, tenantId]);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      logService.error('根據 ID 查詢用戶失敗', { error, userId });
      throw error;
    }
  }

  /**
   * 根據 ID 列表批量查詢用戶
   * @param userIds 用戶 ID 列表
   * @returns 用戶數據列表
   */
  public async getUsersByIds(userIds: string[]): Promise<UserVO[]> {
    if (!userIds || userIds.length === 0) return [];
    
    const tenantId = getRequiredTenantId();

    // 構建 IN 子句的參數佔位符
    const placeholders = userIds.map(() => '?').join(',');
    const sql = `SELECT * FROM user WHERE id IN (${placeholders}) AND tenantId = ?`;

    // 構建參數列表
    const params = [...userIds, tenantId];
    
    try {
      return await aileDBService.all<UserVO>(sql, params);
    } catch (error) {
      logService.error('批量查詢用戶失敗', { error, userIds });
      throw error;
    }
  }
}

const userDao: IUserDao = new UserDao();
export default userDao; 