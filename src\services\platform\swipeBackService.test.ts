/**
 * 边缘滑动返回服务测试
 */

import { SwipeBackService } from './swipeBackService';
import {
  ROUTE_LOGIN,
  ROUTE_OTP_LOGIN,
  ROUTE_USER_SIGNUP,
  ROUTE_BUSINESS_REGISTER,
  ROUTE_LINE_CALLBACK,
  ROUTE_HOME
} from '../../config/app/routes';

// Mock Capacitor
jest.mock('@capacitor/core', () => ({
  Capacitor: {
    getPlatform: jest.fn(() => 'ios'), // 模拟原生平台
  },
}));

// Mock App plugin
jest.mock('@capacitor/app', () => ({
  App: {
    addListener: jest.fn(),
    removeAllListeners: jest.fn(),
    exitApp: jest.fn(),
  },
}));

// Mock log service
jest.mock('../system/logService', () => ({
  logService: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe('SwipeBackService', () => {
  let service: SwipeBackService;
  let mockNavigate: jest.Mock;

  beforeEach(() => {
    service = new SwipeBackService();
    mockNavigate = jest.fn();
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await service.destroy();
  });

  describe('路径检查方法', () => {
    test('应该正确识别OTP登录页面', () => {
      service.updateConfig({
        currentPath: ROUTE_OTP_LOGIN,
        navigate: mockNavigate,
        isAuthenticated: false,
      });

      // 通过反射访问私有方法进行测试
      const isOtpPage = (service as any).isOtpLoginPage(ROUTE_OTP_LOGIN);
      expect(isOtpPage).toBe(true);
    });

    test('应该正确识别企业注册页面', () => {
      const isBusinessPage = (service as any).isBusinessSignupPage(ROUTE_BUSINESS_REGISTER);
      expect(isBusinessPage).toBe(true);
    });

    test('应该正确识别主页面', () => {
      const isHomePage = (service as any).isHomePage(ROUTE_HOME);
      expect(isHomePage).toBe(true);
    });

    test('应该正确识别聊天室页面', () => {
      const chatRoomPaths = [
        '/chat-room/123',
        '/customer-chat/456',
        '/system-chat-room/789',
        '/team-chat/abc',
      ];

      chatRoomPaths.forEach(path => {
        const isChatRoom = (service as any).isChatRoomPage(path);
        expect(isChatRoom).toBe(true);
      });
    });

    test('应该正确识别登录相关页面', () => {
      const loginPaths = [
        ROUTE_LOGIN,
        ROUTE_OTP_LOGIN,
        ROUTE_USER_SIGNUP,
        ROUTE_BUSINESS_REGISTER,
        ROUTE_LINE_CALLBACK,
      ];

      loginPaths.forEach(path => {
        const isLoginPage = (service as any).isLoginRelatedPage(path);
        expect(isLoginPage).toBe(true);
      });
    });
  });

  describe('配置更新', () => {
    test('应该正确更新配置', () => {
      const config = {
        currentPath: '/test',
        navigate: mockNavigate,
        isAuthenticated: true,
      };

      service.updateConfig(config);
      
      // 验证配置已更新（通过检查内部状态）
      expect((service as any).currentConfig).toEqual(config);
    });
  });

  describe('初始化和销毁', () => {
    test('应该正确初始化服务', async () => {
      const { App } = require('@capacitor/app');
      
      await service.initialize();
      
      expect(App.addListener).toHaveBeenCalledWith('backButton', expect.any(Function));
    });

    test('应该正确销毁服务', async () => {
      const { App } = require('@capacitor/app');
      
      await service.initialize();
      await service.destroy();
      
      expect(App.removeAllListeners).toHaveBeenCalled();
    });

    test('在Web平台应该跳过初始化', async () => {
      const { Capacitor } = require('@capacitor/core');
      Capacitor.getPlatform.mockReturnValue('web');
      
      const { App } = require('@capacitor/app');
      
      await service.initialize();
      
      expect(App.addListener).not.toHaveBeenCalled();
    });
  });
});
