/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "mutation RequestLoginOtp($requestLoginOtpRequest2: OtpInput) {\n  requestLoginOtp(request: $requestLoginOtpRequest2) {\n    data {\n      onceToken\n      validSecond\n    }\n    code\n    status\n    msg\n    success\n    timeCost\n  }\n}\n\nmutation LoginWithOtp($request: LoginWithOtpRequest!) {\n  loginWithOtp(request: $request) {\n    code\n    data {\n      name\n      accountId\n      countryCode\n      mobile\n      tokenId\n      loginType\n      onlineId\n      systemRoomId\n      personRoomId\n      lastTenantId\n      systemAccountId\n      loginStatus\n      isMute\n      accountType\n      isInitial\n      tenantRelations {\n        type\n        id\n        description\n        industry\n        industrySub\n        scale\n        code\n        name\n        shortName\n        avatarId\n        isLastTenant\n        accountId\n        relationId\n        openId\n        unReadCount\n        officialServiceNumberId\n        manageServiceNumberId\n        joinTime\n        manageServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n        officialServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n      }\n      currentTenantInfo {\n        id\n        createTime\n        updateTime\n        name\n        shortName\n        code\n        description\n        type\n        startTime\n        endTime\n        industry\n        industrySub\n        scale\n        city\n        address\n        phone\n        website\n        avatarId\n        accountId\n        upgrade\n        unifiedNumber\n        representativeNumber\n        certificateFileId\n        certificateStatus\n        certificateFailReason\n        switchTime\n        employeeInfo {\n          id\n          createTime\n          updateTime\n          name\n          avatarId\n          mood\n          age\n          gender\n          birthday\n          status\n          accountId\n          tenantId\n          channel\n          personRoomId\n          joinType\n          openId\n          isJoinAile\n          isBindAile\n          isCollectInfo\n          homePagePicId\n        }\n      }\n      currentEmployeeInfo {\n        id\n        createTime\n        updateTime\n        name\n        avatarId\n        mood\n        age\n        gender\n        birthday\n        status\n        accountId\n        tenantId\n        channel\n        personRoomId\n        joinType\n        openId\n        isJoinAile\n        isBindAile\n        isCollectInfo\n        homePagePicId\n      }\n    }\n    msg\n    status\n    success\n    timeCost\n  }\n}": typeof types.RequestLoginOtpDocument,
};
const documents: Documents = {
    "mutation RequestLoginOtp($requestLoginOtpRequest2: OtpInput) {\n  requestLoginOtp(request: $requestLoginOtpRequest2) {\n    data {\n      onceToken\n      validSecond\n    }\n    code\n    status\n    msg\n    success\n    timeCost\n  }\n}\n\nmutation LoginWithOtp($request: LoginWithOtpRequest!) {\n  loginWithOtp(request: $request) {\n    code\n    data {\n      name\n      accountId\n      countryCode\n      mobile\n      tokenId\n      loginType\n      onlineId\n      systemRoomId\n      personRoomId\n      lastTenantId\n      systemAccountId\n      loginStatus\n      isMute\n      accountType\n      isInitial\n      tenantRelations {\n        type\n        id\n        description\n        industry\n        industrySub\n        scale\n        code\n        name\n        shortName\n        avatarId\n        isLastTenant\n        accountId\n        relationId\n        openId\n        unReadCount\n        officialServiceNumberId\n        manageServiceNumberId\n        joinTime\n        manageServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n        officialServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n      }\n      currentTenantInfo {\n        id\n        createTime\n        updateTime\n        name\n        shortName\n        code\n        description\n        type\n        startTime\n        endTime\n        industry\n        industrySub\n        scale\n        city\n        address\n        phone\n        website\n        avatarId\n        accountId\n        upgrade\n        unifiedNumber\n        representativeNumber\n        certificateFileId\n        certificateStatus\n        certificateFailReason\n        switchTime\n        employeeInfo {\n          id\n          createTime\n          updateTime\n          name\n          avatarId\n          mood\n          age\n          gender\n          birthday\n          status\n          accountId\n          tenantId\n          channel\n          personRoomId\n          joinType\n          openId\n          isJoinAile\n          isBindAile\n          isCollectInfo\n          homePagePicId\n        }\n      }\n      currentEmployeeInfo {\n        id\n        createTime\n        updateTime\n        name\n        avatarId\n        mood\n        age\n        gender\n        birthday\n        status\n        accountId\n        tenantId\n        channel\n        personRoomId\n        joinType\n        openId\n        isJoinAile\n        isBindAile\n        isCollectInfo\n        homePagePicId\n      }\n    }\n    msg\n    status\n    success\n    timeCost\n  }\n}": types.RequestLoginOtpDocument,
};

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = gql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function gql(source: string): unknown;

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "mutation RequestLoginOtp($requestLoginOtpRequest2: OtpInput) {\n  requestLoginOtp(request: $requestLoginOtpRequest2) {\n    data {\n      onceToken\n      validSecond\n    }\n    code\n    status\n    msg\n    success\n    timeCost\n  }\n}\n\nmutation LoginWithOtp($request: LoginWithOtpRequest!) {\n  loginWithOtp(request: $request) {\n    code\n    data {\n      name\n      accountId\n      countryCode\n      mobile\n      tokenId\n      loginType\n      onlineId\n      systemRoomId\n      personRoomId\n      lastTenantId\n      systemAccountId\n      loginStatus\n      isMute\n      accountType\n      isInitial\n      tenantRelations {\n        type\n        id\n        description\n        industry\n        industrySub\n        scale\n        code\n        name\n        shortName\n        avatarId\n        isLastTenant\n        accountId\n        relationId\n        openId\n        unReadCount\n        officialServiceNumberId\n        manageServiceNumberId\n        joinTime\n        manageServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n        officialServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n      }\n      currentTenantInfo {\n        id\n        createTime\n        updateTime\n        name\n        shortName\n        code\n        description\n        type\n        startTime\n        endTime\n        industry\n        industrySub\n        scale\n        city\n        address\n        phone\n        website\n        avatarId\n        accountId\n        upgrade\n        unifiedNumber\n        representativeNumber\n        certificateFileId\n        certificateStatus\n        certificateFailReason\n        switchTime\n        employeeInfo {\n          id\n          createTime\n          updateTime\n          name\n          avatarId\n          mood\n          age\n          gender\n          birthday\n          status\n          accountId\n          tenantId\n          channel\n          personRoomId\n          joinType\n          openId\n          isJoinAile\n          isBindAile\n          isCollectInfo\n          homePagePicId\n        }\n      }\n      currentEmployeeInfo {\n        id\n        createTime\n        updateTime\n        name\n        avatarId\n        mood\n        age\n        gender\n        birthday\n        status\n        accountId\n        tenantId\n        channel\n        personRoomId\n        joinType\n        openId\n        isJoinAile\n        isBindAile\n        isCollectInfo\n        homePagePicId\n      }\n    }\n    msg\n    status\n    success\n    timeCost\n  }\n}"): (typeof documents)["mutation RequestLoginOtp($requestLoginOtpRequest2: OtpInput) {\n  requestLoginOtp(request: $requestLoginOtpRequest2) {\n    data {\n      onceToken\n      validSecond\n    }\n    code\n    status\n    msg\n    success\n    timeCost\n  }\n}\n\nmutation LoginWithOtp($request: LoginWithOtpRequest!) {\n  loginWithOtp(request: $request) {\n    code\n    data {\n      name\n      accountId\n      countryCode\n      mobile\n      tokenId\n      loginType\n      onlineId\n      systemRoomId\n      personRoomId\n      lastTenantId\n      systemAccountId\n      loginStatus\n      isMute\n      accountType\n      isInitial\n      tenantRelations {\n        type\n        id\n        description\n        industry\n        industrySub\n        scale\n        code\n        name\n        shortName\n        avatarId\n        isLastTenant\n        accountId\n        relationId\n        openId\n        unReadCount\n        officialServiceNumberId\n        manageServiceNumberId\n        joinTime\n        manageServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n        officialServiceNumber {\n          id\n          name\n          avatarId\n          type\n          ownerId\n          accountId\n        }\n      }\n      currentTenantInfo {\n        id\n        createTime\n        updateTime\n        name\n        shortName\n        code\n        description\n        type\n        startTime\n        endTime\n        industry\n        industrySub\n        scale\n        city\n        address\n        phone\n        website\n        avatarId\n        accountId\n        upgrade\n        unifiedNumber\n        representativeNumber\n        certificateFileId\n        certificateStatus\n        certificateFailReason\n        switchTime\n        employeeInfo {\n          id\n          createTime\n          updateTime\n          name\n          avatarId\n          mood\n          age\n          gender\n          birthday\n          status\n          accountId\n          tenantId\n          channel\n          personRoomId\n          joinType\n          openId\n          isJoinAile\n          isBindAile\n          isCollectInfo\n          homePagePicId\n        }\n      }\n      currentEmployeeInfo {\n        id\n        createTime\n        updateTime\n        name\n        avatarId\n        mood\n        age\n        gender\n        birthday\n        status\n        accountId\n        tenantId\n        channel\n        personRoomId\n        joinType\n        openId\n        isJoinAile\n        isBindAile\n        isCollectInfo\n        homePagePicId\n      }\n    }\n    msg\n    status\n    success\n    timeCost\n  }\n}"];

export function gql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;