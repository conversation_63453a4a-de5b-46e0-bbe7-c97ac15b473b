/**
 * 状态栏服务
 * 统一管理状态栏样式和刘海屏适配
 */

import { StatusBar, Style } from '@capacitor/status-bar';
import { Capacitor } from '@capacitor/core';

export interface StatusBarConfig {
  style?: Style;
  backgroundColor?: string;
  overlaysWebView?: boolean;
}

export class StatusBarService {
  private static instance: StatusBarService;
  private isNative: boolean;
  private currentBackgroundColor: string = '#FFFFFF';
  private observers: Array<(color: string) => void> = [];

  private constructor() {
    this.isNative = Capacitor.isNativePlatform();
  }

  public static getInstance(): StatusBarService {
    if (!StatusBarService.instance) {
      StatusBarService.instance = new StatusBarService();
    }
    return StatusBarService.instance;
  }

  /**
   * 初始化状态栏
   */
  public async initialize(): Promise<void> {
    if (!this.isNative) {
      console.log('StatusBar: Web platform, skipping native configuration');
      return;
    }

    try {
      // 设置状态栏样式
      await this.setStyle(Style.Light);
      
      // 设置状态栏背景色
      await this.setBackgroundColor('#FFFFFF');
      
      // 设置状态栏不覆盖WebView
      await this.setOverlaysWebView(false);
      
      console.log('StatusBar: Initialized successfully');
    } catch (error) {
      console.error('StatusBar: Failed to initialize', error);
    }
  }

  /**
   * 设置状态栏样式
   */
  public async setStyle(style: Style): Promise<void> {
    if (!this.isNative) return;

    try {
      await StatusBar.setStyle({ style });
    } catch (error) {
      console.error('StatusBar: Failed to set style', error);
    }
  }

  /**
   * 设置状态栏背景色
   */
  public async setBackgroundColor(color: string): Promise<void> {
    if (!this.isNative) return;

    try {
      await StatusBar.setBackgroundColor({ color });
      this.currentBackgroundColor = color;
      this.notifyObservers(color);
    } catch (error) {
      console.error('StatusBar: Failed to set background color', error);
    }
  }

  /**
   * 设置状态栏是否覆盖WebView
   */
  public async setOverlaysWebView(overlays: boolean): Promise<void> {
    if (!this.isNative) return;

    try {
      await StatusBar.setOverlaysWebView({ overlay: overlays });
    } catch (error) {
      console.error('StatusBar: Failed to set overlays WebView', error);
    }
  }

  /**
   * 显示状态栏
   */
  public async show(): Promise<void> {
    if (!this.isNative) return;

    try {
      await StatusBar.show();
    } catch (error) {
      console.error('StatusBar: Failed to show', error);
    }
  }

  /**
   * 隐藏状态栏
   */
  public async hide(): Promise<void> {
    if (!this.isNative) return;

    try {
      await StatusBar.hide();
    } catch (error) {
      console.error('StatusBar: Failed to hide', error);
    }
  }

  /**
   * 获取状态栏信息
   */
  public async getInfo(): Promise<any> {
    if (!this.isNative) {
      return { visible: true, style: 'LIGHT', color: '#FFFFFF' };
    }

    try {
      return await StatusBar.getInfo();
    } catch (error) {
      console.error('StatusBar: Failed to get info', error);
      return null;
    }
  }

  /**
   * 配置状态栏主题
   */
  public async configureTheme(isDark: boolean = false): Promise<void> {
    if (!this.isNative) return;

    try {
      const style = isDark ? Style.Dark : Style.Light;
      const backgroundColor = isDark ? '#000000' : '#FFFFFF';
      
      await this.setStyle(style);
      await this.setBackgroundColor(backgroundColor);
      
      console.log(`StatusBar: Theme configured to ${isDark ? 'dark' : 'light'}`);
    } catch (error) {
      console.error('StatusBar: Failed to configure theme', error);
    }
  }

  /**
   * 检查是否支持状态栏
   */
  public isSupported(): boolean {
    return this.isNative;
  }

  /**
   * 动态设置页面状态栏颜色
   */
  public async setPageBackgroundColor(color: string, autoDetectStyle: boolean = true): Promise<void> {
    if (!this.isNative) return;

    try {
      await this.setBackgroundColor(color);

      if (autoDetectStyle) {
        // 自动检测颜色亮度并设置合适的状态栏样式
        const style = this.getStyleForColor(color);
        await this.setStyle(style);
      }

      console.log(`StatusBar: Page background color set to ${color}`);
    } catch (error) {
      console.error('StatusBar: Failed to set page background color', error);
    }
  }

  /**
   * 根据背景色自动判断状态栏样式
   */
  private getStyleForColor(color: string): Style {
    // 移除 # 符号
    const hex = color.replace('#', '');

    // 转换为 RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // 计算亮度 (使用 YIQ 公式)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // 亮度大于 128 使用深色状态栏，否则使用浅色状态栏
    return brightness > 128 ? Style.Dark : Style.Light;
  }

  /**
   * 获取当前背景色
   */
  public getCurrentBackgroundColor(): string {
    return this.currentBackgroundColor;
  }

  /**
   * 添加颜色变化观察者
   */
  public addColorObserver(callback: (color: string) => void): void {
    this.observers.push(callback);
  }

  /**
   * 移除颜色变化观察者
   */
  public removeColorObserver(callback: (color: string) => void): void {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  /**
   * 通知所有观察者颜色变化
   */
  private notifyObservers(color: string): void {
    this.observers.forEach(callback => {
      try {
        callback(color);
      } catch (error) {
        console.error('StatusBar: Observer callback error', error);
      }
    });
  }

  /**
   * 重置为默认颜色
   */
  public async resetToDefault(): Promise<void> {
    await this.setPageBackgroundColor('#FFFFFF');
  }
}

// 导出单例实例
export const statusBarService = StatusBarService.getInstance();
