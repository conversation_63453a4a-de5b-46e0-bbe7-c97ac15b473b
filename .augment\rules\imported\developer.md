---
type: "always_apply"
---

你是一名專業的前端開發工程師，在開發多端應用時必須嚴格遵守以下規範：

---

【開發目標】
構建現代化的多端應用，支持瀏覽器（H5）和 APP（iOS/Android），具備良好的用戶體驗、性能和可維護性。

---

【技術棧強制要求】

✅ **核心技術**
- 框架：React 18 + TypeScript 5.x + Vite 5.x
- 多端能力：Capacitor 6.x
- 狀態管理：Redux Toolkit + RTK Query
- UI庫：Ant Design Mobile 5.x
- 樣式方案：Tailwind CSS 3.x
- 路由：React Router v6
- 數據庫：SQLite（本地存儲）
- 國際化：react-i18next + i18next（支持 zh-TW、zh-CN、en-US）
- 測試：Jest + React Testing Library + Playwright
- 包管理：npm

✅ **版本約束**
- Node.js: >=18.0.0
- npm: >=9.0.0

---

【代碼風格強制要求】

✅ **TypeScript 規範**
- 使用嚴格模式
- 禁止使用 any
- 使用類型推斷
- 使用接口定義數據結構

✅ **命名規範**
- 組件：PascalCase (LoginPage, UserCard)
- 函數/變量：camelCase (handleSubmit, userData)
- 常量：UPPER_SNAKE_CASE (API_BASE_URL, MAX_RETRY_COUNT)
- 類型/接口：PascalCase + I前綴 (IUserProfile, IApiResponse)

✅ **繁體中文強制要求**
- 專案中所有中文文字必須使用繁體中文
- 適用範圍：UI文字、註釋、文檔、錯誤訊息、日誌記錄
- 禁止使用簡體中文

✅ **國際化強制要求**
- 所有用戶可見文字必須使用 t() 函數處理
- 禁止硬編碼文字直接出現在組件中
- 支持語言：zh-TW（預設）、zh-CN、en-US
- 使用 useTranslation hook 進行國際化

✅ **導入規範**
- 使用絕對路徑
- 導入順序：React 相關 → 第三方庫 → 內部模塊 → 類型定義
- 按字母順序排序

---

【常量管理強制要求】

🚨 **所有常量必須在 ConstantUtil 類中統一定義和管理**

✅ **必須常量化的內容**
- 存儲鍵名：localStorage、sessionStorage、Cookie 的 key
- API 接口路徑：所有後端 API 接口的路徑地址
- 配置參數：超時時間、重試次數、分頁大小、文件限制
- UI 常量：顏色值、默認文案、尺寸參數
- 業務常量：支持格式、狀態值、枚舉值

✅ **API 常量命名規範**
- 認證相關：API_AUTH_* (登錄、登出、刷新令牌等)
- 用戶相關：API_USER_* (用戶信息、設置、頭像等)
- 推送相關：API_PUSH_* (推送令牌、通知管理等)
- 文件相關：API_FILE_* (文件上傳、下載、管理等)

❌ **禁止行為**
- 硬編碼字符串、數字、API 路徑
- 在代碼中直接使用字符串而非常量
- 分散常量定義在各個文件中

---

【項目結構強制要求】

✅ **目錄結構**
```
src/
├── app/               # Redux store 配置和類型化 hooks
├── components/        # 可復用組件（common、forms、layout）
├── pages/             # 頁面組件
├── routes/            # 路由配置
├── services/          # API 服務層
├── hooks/             # 自定義 Hooks
├── utils/             # 工具函數
├── types/             # 類型定義
├── locales/           # 國際化資源文件（zh-TW、zh-CN、en-US）
├── assets/            # 靜態資源
└── styles/            # 樣式文件
```

✅ **文件命名規範**
- 組件文件：ComponentName.tsx
- 頁面文件：PageName.tsx
- 頁面樣式文件：PageName.css
- 組件樣式文件：ComponentName.css
- Service文件：serviceName.ts
- 測試文件：fileName.test.ts(x)

---

【狀態管理強制要求】

✅ **Redux Toolkit 規範**
- 使用 createAsyncThunk 處理異步操作
- 使用類型化 Hooks：useAppDispatch 和 useAppSelector
- 在異步操作中記錄錯誤日誌

✅ **Redux Slice 結構**
- 明確定義 State 類型
- 提供合理的默認值
- 使用 extraReducers 處理異步操作生命週期

---

【UI組件強制要求】

✅ **Ant Design Mobile 規範**
- 按需導入組件
- 使用 Form.useForm() 管理表單狀態
- 配置完整的表單驗證規則
- 通過 className 進行樣式擴展

✅ **響應式設計強制要求**
- 禁止固定寬度：絕對不允許為頁面容器設置固定的 maxWidth（如 '375px'）
- 移動端優先：先實現移動端佈局，再通過媒體查詢適配更大螢幕
- 彈性佈局：使用 flexbox 和 CSS Grid 實現自適應佈局
- 流體寬度：容器寬度使用 100% 或 100vw，避免硬編碼像素值

✅ **樣式管理強制要求**
- 頁面級CSS文件：每個頁面必須創建獨立的 PageName.css 文件
- CSS變量優先：所有樣式值必須使用CSS變量，禁止硬編碼顏色、尺寸等值
- BEM命名法：CSS類名必須遵循 page-name__element--modifier 格式
- 禁止行內樣式：禁止在JSX中使用 style 屬性

---

【路由配置強制要求】

✅ **React Router v6 規範**
- 所有頁面組件必須使用 React.lazy() 進行懶加載
- 每個懶加載組件都需要 Suspense
- 使用統一的加載組件作為 fallback
- 為路由組件添加錯誤邊界保護

✅ **路由守衛要求**
- 認證守衛：保護需要登錄的路由
- 權限守衛：根據用戶權限控制訪問
- 合理處理未授權訪問的重定向

---

【測試強制要求】

✅ **單元測試規範**
- 每個核心邏輯需有單元測試覆蓋
- 使用 Jest + React Testing Library
- 測試文件與源文件放在同級目錄
- 測試文件命名：FileName.test.ts(x)
- 每個測試文件獨立管理所需的 mock

✅ **E2E 測試規範**
- 使用 Playwright 進行端到端測試
- 使用 data-testid 屬性標識測試元素
- 覆蓋關鍵的用戶操作流程

✅ **測試覆蓋率要求**
- 單元測試覆蓋率: ≥ 80%
- 核心業務邏輯: ≥ 90%
- 關鍵路徑: 100%

---

【性能優化強制要求】

✅ **組件優化**
- 使用 React.memo 避免不必要的重渲染
- 使用 useCallback 緩存函數引用
- 使用 useMemo 緩存計算結果

✅ **代碼分割**
- 路由級別分割：所有頁面組件必須使用 lazy() 加載
- 為懶加載組件提供合適的 Suspense fallback

---

【錯誤處理強制要求】

✅ **錯誤邊界**
- 使用 React Error Boundary 捕獲組件錯誤
- 為路由組件添加錯誤邊界保護
- 提供錯誤時的降級用戶界面

✅ **異步錯誤處理**
- 所有異步操作必須使用 try-catch
- 所有異常處理必須調用 logService 記錄異常信息
- 使用 Toast 或其他方式提示用戶

---

【日誌記錄強制要求】

✅ **日誌規範**
- 每個核心代碼文件需自動引入 logService
- 使用 debug、info、warn、error 四個級別
- error/fatal 級別錯誤由 logService 自動上報 Sentry
- 日誌內容包含錯誤堆棧、上下文信息、用戶標識

---

【代碼提交強制要求】

✅ **Commit 消息格式**
- 格式：`<type>(<scope>): <subject>`
- Type 類型：feat、fix、docs、style、refactor、perf、test、chore

✅ **代碼提交檢查清單**
- [ ] 代碼符合 ESLint 規則
- [ ] 所有測試通過
- [ ] 添加了必要的測試用例
- [ ] 無硬編碼用戶可見文字
- [ ] 繁體中文使用規範符合要求
- [ ] 常量管理規範符合要求

---

【Figma設計實現強制要求】

✅ **像素級精確實現**
- 嚴格按照Figma設計稿實現UI，要求像素級精確（Pixel Perfect）
- 禁止任何形式的自定義、重構或偏離
- 所有UI元素必須與設計稿100%一致
- 所有圖標、圖片必須直接從Figma中導出使用

❌ **禁止項**
- 禁止添加任何設計稿中未出現的UI元素
- 禁止刪除任何設計稿中存在的UI元素
- 禁止對組件或佈局進行任何形式的重構或邏輯簡化

---

【Code Review 強制檢查清單】

✅ **語言規範檢查**
- [ ] 所有中文文字是否使用繁體中文
- [ ] 用戶界面文字是否符合繁體中文規範
- [ ] 註釋和文檔是否使用繁體中文

✅ **國際化規範檢查**
- [ ] 是否有硬編碼的用戶可見文字
- [ ] 是否正確使用 t() 函數進行翻譯
- [ ] 翻譯 key 命名是否規範

✅ **常量管理檢查**
- [ ] 檢查是否有新的硬編碼值
- [ ] 驗證 localStorage/sessionStorage 鍵名使用常量
- [ ] 確認 API 路徑、超時時間等配置使用常量
- [ ] 檢查所有 fetch、axios 請求是否使用 API 常量

---

⚠️ **重要提醒**：違反以上任何強制要求的代碼將不通過 Code Review，請開發者嚴格遵守！

---

【平台环境判断強制要求】

✅ **平台判斷統一規範**
- 所有平台相關的環境判斷（Web/Native/iOS/Android/Mobile等）必須通過 deviceService 提供的方法獲取。
- 嚴禁在業務代碼中直接使用 window.Capacitor、Capacitor.getPlatform()、navigator.userAgent 等原生API進行平台判斷。
- 需要判斷平台時，統一調用 deviceService 的如下方法：
  - `isNative()`：是否為原生APP（iOS/Android）
  - `isWeb()`：是否為Web環境
  - `getPlatform()`：返回 'web' | 'ios' | 'android'
  - `isIOS()` / `isAndroid()` / `isMobile()`：分別判斷iOS、Android、移動端
- 新增平台判斷需求時，必須先在 deviceService 統一實現，再在業務代碼中調用。

❌ **禁止行為**
- 禁止在任何業務代碼中直接判斷 window.Capacitor、Capacitor.getPlatform()、navigator.userAgent 等。
- 禁止在多處重複實現平台判斷邏輯，必須集中於 deviceService。

✅ **審查要求**
- 代碼審查時必須檢查所有平台判斷是否通過 deviceService 實現。
- 違反上述規範的代碼不得合併。

---

# 多租戶資料隔離規則

## 強制要求
1. 除 tenant 表外，所有本地資料表（如 User、Contact、Room、RoomMembers、Messages、FailedMessages、ServiceNumber、KVStore 等）在新增、更新、查詢、刪除時，必須自動帶入當前租戶 Id（tenantId），查詢必須加上 tenantId 條件，確保不同租戶資料完全隔離。
2. KVStore 表必須以 `${tenantId}::key` 方式進行多租戶隔離，嚴禁跨租戶查詢或寫入。
3. 當前租戶 Id 必須統一通過 tenantService.getCurrentTenantId() 或 ConstantUtil.CURRENT_TENANT_ID_KEY 取得，不得自行判斷。
4. 如有新表設計，若涉及多租戶資料，必須包含 tenantId 欄位並遵循本規則。
5. 本規則適用於所有 DAO 層、Service 層及本地資料存取相關邏輯，違反者不得通過 Code Review。

## 測試要求
- 所有多租戶資料隔離邏輯必須有單元測試與 E2E 測試覆蓋。
- 測試需覆蓋不同租戶資料隔離、資料查詢、資料寫入、資料刪除等場景。

---
本規則為 AileCode 專案多租戶資料安全與隔離的強制要求，後續如有新表或新功能，必須嚴格遵循。
















































