import { baseConfig, BaseConfig } from './base';

/**
 * 生产环境配置
 */
export interface ProductionConfig extends BaseConfig {
  API_BASE_URL: string;
  SOCKET_URL: string;
  SOCKET_PATH: string;
  VITE_LINE_CHANNEL_ID: string;
  VITE_LINE_REDIRECT_URI: string;
  DEBUG: boolean;
}

export const productionConfig: ProductionConfig = {
  ...baseConfig,
  
  // 生产环境特定配置
  API_BASE_URL: 'https://newaile.dev.aile.cloud',
  SOCKET_URL: 'https://newaile.dev.aile.cloud/aile',
  SOCKET_PATH: '/socketio/socket.io',
  VITE_LINE_CHANNEL_ID: '2007765389',
  VITE_LINE_REDIRECT_URI: 'cloud.aile.aile:/callback',
  DEBUG: false,
  
  // 生产环境覆盖配置
  LOG_LEVEL: 'warn',
  API_TIMEOUT: 0,
  CACHE_TTL: 600000, // 生产环境延长缓存时间到10分钟
};
