// 服务统一导出

import authService from './core/auth/authService';
export { authService };

// 统一导出其他服务
export * from './core/auth';
export * from './system';
// 导出 db 相关服务的具体模块而非整个目录
export { default as sqliteService } from './db/sqliteService';
export { default as aileDBService, closeAileDB, initAileDB } from './db/aileDBService';
export * from './core/tenant';
// 显式重新导出 core 模块，避免 Type 名称冲突
export { messageService, Type, SystemMessageEventCode, SessionStatus } from './core/chat';
export {
  fetchRoomItem,
  getRoomsByFilter,
  fetchRoomServiceWeightSync,
  fetchRoomServiceRobotList,
  fetchRoomServiceEndList,
  getBasicRoomsFromDB,
  getRoomInfoOptimized,
  syncChatMemberAndStore,
  getChatMembersFromDB
} from './core/chat/roomService';
export type {
  RoomVO,
  ChatMemberVO,
  RoomType,
  Status as RoomStatus,
  Response as RoomResponse,
  WeightRoomListVORoomVO,
  RobotListResponse,
  BaseListVORoomVO
} from '@/types/room.types';
export { default as contactService } from './core/contact/contactService';
export { default as userService } from './core/user/userService';
export { default as attachmentService } from './core/attachment/attachmentService';
export * from './platform';