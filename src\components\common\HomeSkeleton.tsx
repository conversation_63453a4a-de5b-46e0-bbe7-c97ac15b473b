import React from 'react';
import './HomeSkeleton.css';

interface HomeSkeletonProps {
  showUserInfo?: boolean;
  isAdmin?: boolean;
  loadingStage?: 'initial' | 'authenticated' | 'data-loading';
}

/**
 * Home页面骨架屏组件
 * 模拟HomePage的完整布局结构
 */
const HomeSkeleton: React.FC<HomeSkeletonProps> = ({
  showUserInfo = true,
  isAdmin = false,
  loadingStage = 'initial'
}) => {
  // 根据加载阶段调整显示内容
  const shouldShowUserInfo = loadingStage !== 'initial' && showUserInfo;
  return (
    <div className="home-skeleton" data-testid="home-skeleton">
      {/* 顶部进度条 - 仅在初始加载时显示 */}
      {loadingStage === 'initial' && (
        <div className="skeleton-progress-bar">
          <div className="skeleton-progress-fill"></div>
        </div>
      )}

      {/* Header 骨架 */}
      <div className={`home-skeleton-header ${shouldShowUserInfo ? '' : 'user-info-hidden'}`}>
        {/* User Info 骨架 */}
        {shouldShowUserInfo && (
          <div className="home-skeleton-user-info">
            <div className="skeleton-avatar"></div>
            <div className="skeleton-user-details">
              <div className="skeleton-line skeleton-line-short"></div>
            </div>
            <div className="skeleton-points">
              <div className="skeleton-points-icon"></div>
              <div className="skeleton-line skeleton-line-mini"></div>
              <div className="skeleton-line skeleton-line-mini"></div>
            </div>
          </div>
        )}
        
        {/* Nav Bar 骨架 */}
        <div className="home-skeleton-nav-bar">
          <div className="skeleton-nav-left">
            <div className="skeleton-line skeleton-line-medium"></div>
            <div className="skeleton-dropdown-icon"></div>
          </div>
          <div className="skeleton-nav-right">
            <div className="skeleton-nav-icon"></div>
            <div className="skeleton-nav-icon"></div>
            <div className="skeleton-nav-icon"></div>
          </div>
        </div>
      </div>

      {/* Content 骨架 */}
      <div className="home-skeleton-content">
        {/* Filter Tabs 骨架 */}
        <div className="home-skeleton-filter-tabs">
          <div className="skeleton-filter-tab skeleton-filter-tab-active">
            <div className="skeleton-line skeleton-line-short"></div>
          </div>
          <div className="skeleton-filter-tab">
            <div className="skeleton-line skeleton-line-short"></div>
          </div>
          <div className="skeleton-filter-tab">
            <div className="skeleton-line skeleton-line-short"></div>
          </div>
        </div>

        {/* List Items 骨架 */}
        <div className="home-skeleton-list">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="home-skeleton-list-item">
              <div className="skeleton-item-avatar"></div>
              <div className="skeleton-item-content">
                <div className="skeleton-item-header">
                  <div className="skeleton-line skeleton-line-medium"></div>
                  <div className="skeleton-line skeleton-line-mini"></div>
                </div>
                <div className="skeleton-line skeleton-line-long"></div>
              </div>
              <div className="skeleton-item-badge"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Tab Bar 骨架 */}
      <div className="home-skeleton-tab-bar">
        <div className="skeleton-tab-item">
          <div className="skeleton-tab-icon"></div>
          <div className="skeleton-line skeleton-line-mini"></div>
        </div>
        <div className="skeleton-tab-item">
          <div className="skeleton-tab-icon"></div>
          <div className="skeleton-line skeleton-line-mini"></div>
        </div>
        <div className="skeleton-tab-item">
          <div className="skeleton-tab-icon"></div>
          <div className="skeleton-line skeleton-line-mini"></div>
        </div>
        <div className="skeleton-tab-item">
          <div className="skeleton-tab-icon"></div>
          <div className="skeleton-line skeleton-line-mini"></div>
        </div>
        {isAdmin && (
          <div className="skeleton-tab-item">
            <div className="skeleton-tab-icon"></div>
            <div className="skeleton-line skeleton-line-mini"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HomeSkeleton;
