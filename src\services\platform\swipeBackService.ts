/**
 * 边缘滑动返回服务
 * 处理移动端边缘滑动返回导航逻辑
 */

import { App } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import { logService } from '../system/logService';
import { 
  ROUTE_LOGIN, 
  ROUTE_OTP_LOGIN, 
  ROUTE_HOME, 
  ROUTE_USER_SIGNUP,
  ROUTE_BUSINESS_REGISTER,
  ROUTE_LINE_CALLBACK
} from '../../config/app/routes';

export interface SwipeBackConfig {
  /** 当前路由路径 */
  currentPath: string;
  /** 导航函数 */
  navigate: (path: string, options?: { replace?: boolean }) => void;
  /** 是否已认证 */
  isAuthenticated: boolean;
}

export class SwipeBackService {
  private isInitialized = false;
  private currentConfig: SwipeBackConfig | null = null;

  /**
   * 初始化边缘滑动返回服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logService.debug('SwipeBackService 已经初始化');
      return;
    }

    // 只在原生平台启用
    if (Capacitor.getPlatform() === 'web') {
      logService.debug('Web平台不支持边缘滑动返回');
      return;
    }

    try {
      // 监听硬件返回按钮事件
      await App.addListener('backButton', this.handleBackButton.bind(this));
      
      this.isInitialized = true;
      logService.info('SwipeBackService 初始化成功');
    } catch (error) {
      logService.error('SwipeBackService 初始化失败', error as Error);
    }
  }

  /**
   * 更新当前配置
   */
  public updateConfig(config: SwipeBackConfig): void {
    this.currentConfig = config;
    logService.debug('SwipeBackService 配置已更新', {
      currentPath: config.currentPath,
      isAuthenticated: config.isAuthenticated
    });
  }

  /**
   * 处理返回按钮事件
   */
  private async handleBackButton(): Promise<void> {
    if (!this.currentConfig) {
      logService.warn('SwipeBackService 配置未设置，使用默认返回行为');
      return;
    }

    const { currentPath, navigate, isAuthenticated } = this.currentConfig;
    
    logService.debug('处理边缘滑动返回', { currentPath, isAuthenticated });

    try {
      // 根据当前路径决定返回行为
      if (this.isOtpLoginPage(currentPath)) {
        // OTP登录页面 → 返回到login页面
        logService.info('从OTP登录页面返回到登录页面');
        navigate(ROUTE_LOGIN, { replace: true });
        
      } else if (this.isBusinessSignupPage(currentPath)) {
        // 企业登录页面 → 返回到OTP页面
        logService.info('从企业登录页面返回到OTP页面');
        navigate(ROUTE_OTP_LOGIN, { replace: true });
        
      } else if (this.isHomePage(currentPath)) {
        // 主页面 → 直接退出应用
        logService.info('从主页面退出应用');
        await App.exitApp();
        
      } else if (this.isChatRoomPage(currentPath)) {
        // 聊天室页面 → 返回到主页面
        logService.info('从聊天室页面返回到主页面');
        navigate(ROUTE_HOME, { replace: true });
        
      } else if (this.isLoginRelatedPage(currentPath)) {
        // 其他登录相关页面 → 退出应用
        logService.info('从登录相关页面退出应用');
        await App.exitApp();
        
      } else {
        // 其他页面 → 默认返回行为（返回上一页或主页）
        if (isAuthenticated) {
          logService.info('已认证用户，返回到主页面');
          navigate(ROUTE_HOME, { replace: true });
        } else {
          logService.info('未认证用户，返回到登录页面');
          navigate(ROUTE_LOGIN, { replace: true });
        }
      }
    } catch (error) {
      logService.error('处理边缘滑动返回失败', error as Error);
    }
  }

  /**
   * 检查是否为OTP登录页面
   */
  private isOtpLoginPage(path: string): boolean {
    return path === ROUTE_OTP_LOGIN;
  }

  /**
   * 检查是否为企业注册页面
   */
  private isBusinessSignupPage(path: string): boolean {
    return path === ROUTE_BUSINESS_REGISTER;
  }

  /**
   * 检查是否为主页面
   */
  private isHomePage(path: string): boolean {
    return path === ROUTE_HOME;
  }

  /**
   * 检查是否为聊天室页面
   */
  private isChatRoomPage(path: string): boolean {
    return path.includes('/chat-room/') ||
           path.includes('/customer-chat/') ||
           path.includes('/system-chat-room/') ||
           path.includes('/team-chat/');
  }

  /**
   * 检查是否为登录相关页面
   */
  private isLoginRelatedPage(path: string): boolean {
    const loginPages = [
      ROUTE_LOGIN,
      ROUTE_OTP_LOGIN,
      ROUTE_USER_SIGNUP,
      ROUTE_BUSINESS_REGISTER,
      ROUTE_LINE_CALLBACK,
    ];
    return loginPages.includes(path as any);
  }

  /**
   * 销毁服务
   */
  public async destroy(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      // 移除所有监听器
      await App.removeAllListeners();
      
      this.isInitialized = false;
      this.currentConfig = null;
      
      logService.info('SwipeBackService 已销毁');
    } catch (error) {
      logService.error('SwipeBackService 销毁失败', error as Error);
    }
  }
}

// 创建单例实例
export const swipeBackService = new SwipeBackService();
