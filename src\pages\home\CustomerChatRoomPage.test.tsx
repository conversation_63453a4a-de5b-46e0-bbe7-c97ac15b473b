
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import CustomerChatRoomPage from './CustomerChatRoomPage';

// Mock the navigate function
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({ customerId: '123' }),
}));

describe('CustomerChatRoomPage', () => {
  it('renders correctly', () => {
    render(
      <BrowserRouter>
        <CustomerChatRoomPage />
      </BrowserRouter>
    );
    
    // Check for main elements
    expect(screen.getByText('Abby Wang')).toBeInTheDocument();
    expect(screen.getByText('客戶相關任務 (4)')).toBeInTheDocument();
    
    // Check for date headers
    expect(screen.getByText('04/07')).toBeInTheDocument();
    expect(screen.getByText('今天')).toBeInTheDocument();
    
    // Check for messages
    expect(screen.getByText('訊息訊息')).toBeInTheDocument();
    expect(screen.getByText('訊息')).toBeInTheDocument();
    expect(screen.getByText('訊息訊息訊息訊息')).toBeInTheDocument();
    
    // Check for timestamps
    expect(screen.getAllByText('13:41').length).toBe(2);
    expect(screen.getByText('13:45')).toBeInTheDocument();
    expect(screen.getByText('09:34')).toBeInTheDocument();
    expect(screen.getByText('09:45')).toBeInTheDocument();
    
    // Check for team chat button
    expect(screen.getByText('團隊聊天室')).toBeInTheDocument();
    expect(screen.getByText('8')).toBeInTheDocument();
    
    // Check for input
    expect(screen.getByPlaceholderText('Aa')).toBeInTheDocument();
  });
  
  it('allows typing in the input field', () => {
    render(
      <BrowserRouter>
        <CustomerChatRoomPage />
      </BrowserRouter>
    );
    
    const input = screen.getByPlaceholderText('Aa');
    fireEvent.change(input, { target: { value: 'Test message' } });
    
    // Check if the input value has changed
    expect(input).toHaveValue('Test message');
  });
  
  it('has working back button', () => {
    const navigateMock = jest.fn();
    jest.spyOn(require('react-router-dom'), 'useNavigate').mockImplementation(() => navigateMock);
    
    render(
      <BrowserRouter>
        <CustomerChatRoomPage />
      </BrowserRouter>
    );
    
    // Find and click the back button
    const backButton = screen.getByAltText('Back');
    fireEvent.click(backButton);
    
    // Check if navigate was called with -1 (navigate back)
    expect(navigateMock).toHaveBeenCalledWith(-1);
  });
}); 