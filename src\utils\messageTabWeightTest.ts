import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from '@/types/aile.enum';
import { RoomType, Status, type RoomVO } from '@/types/room.types';
import { SessionStatus } from '@/services/core/chat/roomService';

/**
 * MessageTab权重计算测试工具
 * 用于验证权重计算逻辑是否正确
 */

// 模拟当前用户ID
const CURRENT_USER_ID = 'current_user_id';

// 模拟stateService.loginUser()
const mockLoginUser = () => ({
  id: CURRENT_USER_ID,
  name: 'Test User',
});

/**
 * 根据聊天室状态计算权重（与MessageTab中的逻辑相同）
 */
export const calculateRoomWeight = (room: RoomVO) => {
  try {
    // 安全检查
    if (!room || !room.id) {
      console.warn('房间数据无效，使用默认权重', { room });
      return {
        p1: P1WeightEnum.Default,
        p2: P2WeightEnum.Default,
        p3: [P3WeightEnum.Default],
        timestamp: Date.now(),
      };
    }

    // P1: 根据聊天室会话状态设置最高优先级
    let p1 = P1WeightEnum.Default;
    if (room.type === RoomType.Services) {
      // 客服聊天室根据会话状态设置优先级
      switch (room.sessionStatus) {
        case SessionStatus.DistributeActive:
          p1 = P1WeightEnum.JustReceived; // 转人工，刚进件
          break;
        case SessionStatus.AgentActive:
          // 根据agentId判断是否为当前用户的服务
          try {
            const currentUser = mockLoginUser();
            if (currentUser && room.agentId === currentUser.id) {
              p1 = P1WeightEnum.MyService; // 我的服务中
            } else {
              p1 = P1WeightEnum.InService; // 服务中
            }
          } catch (error) {
            console.warn('获取当前用户失败，使用默认P1权重', { error });
            p1 = P1WeightEnum.InService; // 默认为服务中
          }
          break;
        case SessionStatus.RobotActive:
          p1 = P1WeightEnum.Default; // 机器人服务中，默认优先级
          break;
        default:
          p1 = P1WeightEnum.Default;
      }
    }

    // P2: 根据未读消息设置次要优先级
    const p2 = (room.unreadCount && room.unreadCount > 0) ? P2WeightEnum.Unread : P2WeightEnum.Default;

    // P3: 根据聊天室标签设置优先级
    const p3Tags: P3WeightEnum[] = [];
    if (room.isPin) p3Tags.push(P3WeightEnum.Top); // 置顶
    if (room.isFavorite) p3Tags.push(P3WeightEnum.Favorite); // 收藏
    if (room.isAIDraft) p3Tags.push(P3WeightEnum.Draft); // AI草稿
    if (room.isDraft) p3Tags.push(P3WeightEnum.Draft); // 草稿
    if (room.hasAtMe) p3Tags.push(P3WeightEnum.AtMe); // @我
    if (room.hasSendFailed) p3Tags.push(P3WeightEnum.SendFailed); // 发送失败
    
    // 根据房间状态添加特殊标签
    if (room.status === Status.Danger) p3Tags.push(P3WeightEnum.SendFailed); // 危险状态
    if (room.status === Status.Complaint) p3Tags.push(P3WeightEnum.MarkUnread); // 投诉状态
    
    if (p3Tags.length === 0) p3Tags.push(P3WeightEnum.Default);

    return {
      p1,
      p2,
      p3: p3Tags,
      timestamp: room.updateTime || Date.now(),
    };
  } catch (error) {
    console.error('计算房间权重失败，使用默认权重', { error, roomId: room?.id });
    return {
      p1: P1WeightEnum.Default,
      p2: P2WeightEnum.Default,
      p3: [P3WeightEnum.Default],
      timestamp: Date.now(),
    };
  }
};

/**
 * 测试用例
 */
export const testRoomWeightCalculation = () => {
  console.log('开始测试MessageTab权重计算逻辑...');

  // 测试用例1: 刚进件 + 未读 + 置顶
  const room1: RoomVO = {
    id: 'room1',
    name: '客户A',
    type: RoomType.Services,
    sessionStatus: SessionStatus.DistributeActive,
    unreadCount: 5,
    updateTime: Date.now() - 60000,
    isPin: true,
  };

  // 测试用例2: 我的服务中 + 未读
  const room2: RoomVO = {
    id: 'room2',
    name: '客户B',
    type: RoomType.Services,
    sessionStatus: SessionStatus.AgentActive,
    agentId: CURRENT_USER_ID,
    unreadCount: 2,
    updateTime: Date.now() - 120000,
  };

  // 测试用例3: 服务中 + 危险状态
  const room3: RoomVO = {
    id: 'room3',
    name: '客户C',
    type: RoomType.Services,
    sessionStatus: SessionStatus.AgentActive,
    agentId: 'other_agent',
    unreadCount: 0,
    updateTime: Date.now() - 180000,
    status: Status.Danger,
  };

  // 测试用例4: 机器人服务 + AI草稿
  const room4: RoomVO = {
    id: 'room4',
    name: '客户D',
    type: RoomType.Services,
    sessionStatus: SessionStatus.RobotActive,
    unreadCount: 1,
    updateTime: Date.now() - 240000,
    isAIDraft: true,
  };

  const testRooms = [room1, room2, room3, room4];
  
  testRooms.forEach((room, index) => {
    const weight = calculateRoomWeight(room);
    console.log(`测试用例${index + 1}:`, {
      roomName: room.name,
      sessionStatus: room.sessionStatus,
      unreadCount: room.unreadCount,
      isPin: room.isPin,
      status: room.status,
      isAIDraft: room.isAIDraft,
      calculatedWeight: weight,
    });
  });

  console.log('权重计算测试完成');
  return testRooms.map(room => ({ room, weight: calculateRoomWeight(room) }));
};

/**
 * 验证权重计算是否会导致错误
 */
export const validateWeightCalculation = () => {
  try {
    // 测试空值
    const emptyRoom = null as any;
    const emptyWeight = calculateRoomWeight(emptyRoom);
    console.log('空值测试通过:', emptyWeight);

    // 测试无效数据
    const invalidRoom = { id: undefined } as any;
    const invalidWeight = calculateRoomWeight(invalidRoom);
    console.log('无效数据测试通过:', invalidWeight);

    // 测试正常数据
    const normalRoom: RoomVO = {
      id: 'test_room',
      name: '测试房间',
      type: RoomType.Services,
      sessionStatus: SessionStatus.DistributeActive,
      unreadCount: 1,
      updateTime: Date.now(),
    };
    const normalWeight = calculateRoomWeight(normalRoom);
    console.log('正常数据测试通过:', normalWeight);

    return true;
  } catch (error) {
    console.error('权重计算验证失败:', error);
    return false;
  }
};

// 如果在浏览器环境中，可以在控制台运行测试
if (typeof window !== 'undefined') {
  (window as any).testMessageTabWeight = {
    testRoomWeightCalculation,
    validateWeightCalculation,
    calculateRoomWeight,
  };
}
