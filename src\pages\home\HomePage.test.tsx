import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import HomePage from './HomePage';
import { getRoomsByFilter } from '@/services';
import { getLocalStorage } from '@/utils/storage';
import aileDBService from '@/services/db/aileDBService';
import stateService from '@/services/stateService';

// Mock additional dependencies for skeleton optimization tests
jest.mock('@/services/room/roomService');
jest.mock('@/utils/storage');
jest.mock('@/services/db/aileDBService');
jest.mock('@/services/stateService');
jest.mock('../../hooks/useStatusBarColor', () => ({
  useStatusBarColor: () => ({ setColor: jest.fn() })
}));

const mockGetRoomsByFilter = getRoomsByFilter as jest.MockedFunction<typeof getRoomsByFilter>;
const mockGetLocalStorage = getLocalStorage as jest.MockedFunction<typeof getLocalStorage>;
const mockAileDBService = aileDBService as jest.Mocked<typeof aileDBService>;
const mockStateService = stateService as jest.Mocked<typeof stateService>;

// Mock the translations
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock the react-router-dom hooks
jest.mock('react-router-dom', () => ({
  useNavigate: () => jest.fn(),
  useLocation: () => ({
    pathname: '/',
    hash: '',
    search: '',
    state: null,
  }),
}));

// Mock the image imports
jest.mock('../../assets/icons/message-active.svg', () => 'message-active.svg');
jest.mock('../../assets/icons/task.svg', () => 'task.svg');
jest.mock('../../assets/icons/client.svg', () => 'client.svg');
jest.mock('../../assets/icons/memo.svg', () => 'memo.svg');
jest.mock('../../assets/icons/account.svg', () => 'account.svg');
jest.mock('../../assets/icons/points-icon.svg', () => 'points-icon.svg');
jest.mock('../../assets/icons/down-arrow.svg', () => 'down-arrow.svg');
jest.mock('../../assets/icons/search.svg', () => 'search.svg');
jest.mock('../../assets/icons/scanning.svg', () => 'scanning.svg');
jest.mock('../../assets/icons/more.svg', () => 'more.svg');
jest.mock('../../assets/icons/mute.svg', () => 'mute.svg');
jest.mock('../../assets/icons/edit-icon.svg', () => 'edit-icon.svg');
jest.mock('../../assets/images/avatars/user-avatar.png', () => 'user-avatar.png');
jest.mock('../../assets/images/avatars/team-avatar.png', () => 'team-avatar.png');
jest.mock('../../assets/images/avatars/alice-avatar.png', () => 'alice-avatar.png');
jest.mock('../../assets/images/avatars/grace-avatar.png', () => 'grace-avatar.png');
jest.mock('../../assets/images/avatars/peter-avatar.png', () => 'peter-avatar.png');
jest.mock('../../assets/images/avatars/alex-avatar.png', () => 'alex-avatar.png');
jest.mock('../../assets/images/avatars/jack-avatar.png', () => 'jack-avatar.png');

// Create mock store for skeleton optimization tests
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      room: (state = {
        robotRooms: [],
        activeRooms: [],
        processedRooms: [],
        robotPage: 0,
        activePage: 0,
        processedPage: 0,
        robotHasNextPage: true,
        activeHasNextPage: true,
        processedHasNextPage: true,
        robotRefreshTime: 0,
        processedRefreshTime: 0,
        weight: 0,
      }) => state,
      auth: (state = {
        isAuthenticated: true,
        account: { accountId: 'test-user' },
        authToken: 'test-token'
      }) => state,
      tenant: (state = {
        currentTenantId: 'test-tenant',
        currentTenantInfo: { id: 'test-tenant' }
      }) => state
    },
    preloadedState: initialState
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('HomePage Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mocks for skeleton optimization
    mockAileDBService.isInitialized.mockReturnValue(true);
    mockStateService.loginUser.mockReturnValue({
      id: 'test-user',
      createTime: Date.now(),
      updateTime: Date.now(),
      name: 'Test User',
      avatarId: null,
      mood: null,
      age: null,
      gender: null,
      birthday: null,
      status: 'active',
      accountId: 'test-account',
      tenantId: 'test-tenant',
      channel: 'web',
      personRoomId: 'test-room',
      joinType: 'normal',
      openId: 'test-open-id',
      isJoinAile: true,
      isBindAile: true,
      isCollectInfo: false,
      homePagePicId: null,
      tokenId: 'test-token',
      tenantCode: 'test-code'
    });
    mockStateService.tenantId.mockReturnValue('test-tenant');
    mockGetRoomsByFilter.mockResolvedValue([]);
    mockGetLocalStorage.mockReturnValue(null);
  });
  it('renders the header correctly', () => {
    render(<HomePage />);
    expect(screen.getByText('王小明醫師')).toBeInTheDocument();
    expect(screen.getByText('9375')).toBeInTheDocument();
    expect(screen.getByText('點')).toBeInTheDocument();
    expect(screen.getByText('全部列表')).toBeInTheDocument();
  });

  it('renders the message tab content initially', () => {
    render(<HomePage />);
    expect(screen.getByText('機器人(99)')).toBeInTheDocument();
    expect(screen.getByText('服務中(20)')).toBeInTheDocument();
    expect(screen.getByText('已處理')).toBeInTheDocument();
    expect(screen.getByText('團隊聊天室')).toBeInTheDocument();
  });

  it('switches to the task tab when clicked', () => {
    render(<HomePage />);
    
    // Find the "任務" tab and click on it
    const taskTab = screen.getByText('任務');
    fireEvent.click(taskTab);
    
    // Check that task tab content is displayed
    expect(screen.getByText('全部')).toBeInTheDocument();
    expect(screen.getByText('待處理')).toBeInTheDocument();
    expect(screen.getByText('處理中')).toBeInTheDocument();
    expect(screen.getByText('已完成')).toBeInTheDocument();
    
    // Check for some task items
    expect(screen.getByText('預約諮詢')).toBeInTheDocument();
    expect(screen.getByText('回覆客戶問題')).toBeInTheDocument();
  });

  it('filters tasks correctly in task tab', () => {
    render(<HomePage />);
    
    // Switch to the task tab
    const taskTab = screen.getByText('任務');
    fireEvent.click(taskTab);
    
    // Initially all tasks should be visible
    expect(screen.getByText('預約諮詢')).toBeInTheDocument();
    expect(screen.getByText('回覆客戶問題')).toBeInTheDocument();
    expect(screen.getByText('客戶回訪')).toBeInTheDocument();
    
    // Switch to the "待處理" filter
    const todoTab = screen.getByText('待處理');
    fireEvent.click(todoTab);
    
    // Should see only todo tasks
    expect(screen.getByText('預約諮詢')).toBeInTheDocument();
    expect(screen.getByText('追蹤訂單狀態')).toBeInTheDocument();
    expect(screen.queryByText('回覆客戶問題')).not.toBeInTheDocument(); // Processing task
    expect(screen.queryByText('客戶回訪')).not.toBeInTheDocument(); // Completed task
  });

  it('switches back to messages tab when clicked', () => {
    render(<HomePage />);
    
    // First switch to task tab
    const taskTab = screen.getByText('任務');
    fireEvent.click(taskTab);
    
    // Then switch back to message tab
    const messageTab = screen.getByText('訊息');
    fireEvent.click(messageTab);
    
    // Check message content appears
    expect(screen.getByText('機器人(99)')).toBeInTheDocument();
    expect(screen.getByText('團隊聊天室')).toBeInTheDocument();
    expect(screen.queryByText('全部')).not.toBeInTheDocument(); // Task tab title not present
  });

  describe('Skeleton Optimization', () => {
    it('should show skeleton for 800ms when no cached data', async () => {
      renderWithProviders(<HomePage />);

      // Should show skeleton initially
      expect(screen.getByTestId('home-skeleton')).toBeInTheDocument();

      // Should hide skeleton after timeout
      await waitFor(() => {
        expect(screen.queryByTestId('home-skeleton')).not.toBeInTheDocument();
      }, { timeout: 1000 });
    });

    it('should skip skeleton when Redux has cached data', async () => {
      const stateWithCache = {
        room: {
          robotRooms: [{ id: '1', name: 'Test Room' }],
          activeRooms: [],
          processedRooms: [],
          robotPage: 0,
          activePage: 0,
          processedPage: 0,
          robotHasNextPage: true,
          activeHasNextPage: true,
          processedHasNextPage: true,
          robotRefreshTime: 0,
          processedRefreshTime: 0,
          weight: 0,
        }
      };

      renderWithProviders(<HomePage />, stateWithCache);

      // Should not show skeleton when cached data exists
      await waitFor(() => {
        expect(screen.queryByTestId('home-skeleton')).not.toBeInTheDocument();
      }, { timeout: 100 });
    });

    it('should skip skeleton when database has cached data', async () => {
      mockGetRoomsByFilter.mockResolvedValueOnce([{ id: '1', name: 'DB Room' }]);

      renderWithProviders(<HomePage />);

      // Should quickly hide skeleton when DB has data
      await waitFor(() => {
        expect(screen.queryByTestId('home-skeleton')).not.toBeInTheDocument();
      }, { timeout: 200 });
    });

    it('should show shorter skeleton (400ms) when has persisted auth but no data', async () => {
      mockGetLocalStorage.mockReturnValue({
        auth: {
          isAuthenticated: true,
          authToken: 'persisted-token'
        }
      });

      renderWithProviders(<HomePage />);

      // Should show skeleton initially
      expect(screen.getByTestId('home-skeleton')).toBeInTheDocument();

      // Should hide skeleton after shorter timeout
      await waitFor(() => {
        expect(screen.queryByTestId('home-skeleton')).not.toBeInTheDocument();
      }, { timeout: 500 });
    });
  });
});