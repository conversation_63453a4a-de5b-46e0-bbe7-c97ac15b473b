module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.svg$': '<rootDir>/__mocks__/svgMock.js',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
    }],
  },
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  transformIgnorePatterns: [
    'node_modules/(?!(react-router|react-router-dom)/)'
  ],
  globals: {
    'import.meta': {
      env: {
        MODE: 'test',
        DEV: false,
        PROD: false,
        VITE_API_BASE_URL: 'http://localhost:3000',
        VITE_SOCKET_URL: 'http://localhost:3000',
        VITE_SOCKET_PATH: '/socket.io',
        VITE_LINE_CHANNEL_ID: 'test-channel-id',
        VITE_LINE_REDIRECT_URI: 'http://localhost:3000/callback',
        VITE_DEBUG: 'false'
      }
    }
  },
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  testEnvironmentOptions: {
    customExportConditions: ['node', 'node-addons'],
  },
}; 