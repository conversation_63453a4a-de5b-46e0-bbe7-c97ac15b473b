import { logService } from '../../system/logService';
import httpService from '../../system/httpService';
import { ConstantUtil } from '../../../utils/constantUtil';
import { CryptoUtil } from '../../../utils/cryptoUtil';
import { firstValueFrom } from 'rxjs';
import { Messages } from '../../db/initSql';
import { messageDao } from '../../db/dao';
import { SessionStatus } from './roomService';
/**
 * 聊天消息發送請求參數
 */
export interface SendMessageRequest {
  roomId: string;  /** 聊天室ID*/
  content: object | string;  /**消息內容*/
  type?: Type;  /**消息類型*/
  metadata?: Record<string, any>;  /**消息元數據*/
  replyToId?: string;  /**引用消息ID（如果是回覆消息）*/
}
/**
 * 聊天消息發送響應
 */
export interface SendMessageResponse {
  success: boolean;
  data?: {
    id: string;
    sequence: number;
    senderName: string;
    senderId: string;
    accountId: string;
    sendTime: number;
    roomId: string;
    tenantId: string;
    channel: string;
    content: string;
    sourceType: string;
  };
  msg?: string;
  code?: string;
}

/**
 * 獲取聊天消息請求參數
 */
export interface SyncMessageRequest {
  content?: { [key: string]: any };
  /**
   * 排序方向，排序方向
   */
  direction?: string;
  /**
   * 關鍵字，關鍵字
   */
  key?: string;
  keyword?: string;
  last_sequence?: number;
  messageId?: string;
  messageIds?: string[];
  /**
   * 排序欄位，排序欄位
   */
  orderBy?: string;
  /**
   * 頁數，頁數
   */
  pageIndex?: number;
  /**
   * 每頁筆數，每頁筆數
   */
  pageSize?: number;
  previous_sequence?: number;
  /**
   * 刷新時間，刷新時間
   */
  refreshTime?: number;
  roomId?: string;
  sequence?: number;
  sort?: string;
  tag?: { [key: string]: any };
  type?: Type;
  [property: string]: any;
}

export enum Type {
  Action = "Action",
  At = "At",
  Audio = "Audio",
  Event = "Event",
  File = "File",
  Image = "Image",
  Json = "Json",
  Location = "Location",
  Sticker = "Sticker",
  Template = "Template",
  Text = "Text",
  Video = "Video",
  Voice = "Voice",
}

export enum AppointChannel {
  Aiwow = "Aiwow",
  Webchat = "Webchat",
  Line = "Line",
  Facebook = "Facebook",
  Instagram = "Instagram",
}

export enum SystemMessageEventCode {
  SessionStart = 'SessionStart',
  AgentStart = 'AgentStart',
  AgentActive = 'AgentActive',
  RobotActive = 'RobotActive',
  AgentStop = 'AgentStop',
  DistributeActive = 'DistributeActive',
  Timeout = 'Timeout',
  RoomMemberAdd = 'RoomMemberAdd',
  SessionAutoTransfer = 'SessionAutoTransfer'
}
/**
 * 聊天消息數據結構
 */
export interface MessageData {
  /**
   * 消息ID
   */
  id: string;
  
  /**
   * 聊天室ID
   */
  roomId: string;
  
  /**
   * 發送者ID
   */
  senderId: string;
  
  /**
   * 發送者名稱
   */
  senderName: string;
  
  /**
   * 發送者頭像
   */
  senderAvatar?: string;
  
  /**
   * 消息內容
   */
  content: string;
  
  /**
   * 消息類型
   */
  type: 'text' | 'image' | 'file' | 'system' | 'location';
  
  /**
   * 發送時間
   */
  sendTime: string;
  
  /**
   * 消息狀態
   */
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  
  /**
   * 消息元數據
   */
  metadata?: Record<string, any>;
  
  /**
   * 引用消息ID
   */
  replyToId?: string;
  
  /**
   * 引用消息內容
   */
  replyContent?: string;

  /**
   * 更新時間戳
   */
  updateTime?: number;

  sequence: number;
}

/**
 * 獲取聊天消息響應
 */
export interface SyncMessageResponse {
  success: boolean;
  data?: {
    /**
     * 消息列表
     */
    items: MessageData[];
    
    /**
     * 總消息數量
     */
    total: number;
    
    /**
     * 是否有更多消息
     */
    hasMore: boolean;
    
    /**
     * 最後一條消息ID
     */
    lastMessageId?: string;

    /**
     * 是否有下一頁
     */
    hasNextPage?: boolean;

    /**
     * 刷新時間
     */
    refreshTime?: number;
  };
  msg?: string;
  code?: string;
}

/**
 * 批量獲取消息結果
 */
export interface BatchFetchResult {
  /**
   * 所有消息列表
   */
  allMessages: MessageData[];
  
  /**
   * 最新刷新時間
   */
  latestRefreshTime: number;
  
  /**
   * 是否成功
   */
  success: boolean;
  
  /**
   * 錯誤信息
   */
  errorMsg?: string;

  /**
   * 保存到數據庫的消息數量
   */
  savedCount?: number;
}

/**客戶最後進線資料 */
export interface FromAppointRequest{
  roomId: string;  /** 聊天室ID*/
}

/**
 * 客戶最後進線資料響應
 */
export interface FromAppointResponse {
  success: boolean;
  data?: {
    lastFrom: string,
    sessionStatus: SessionStatus,
    otherFroms: any,
    lastMessageTimeOut: boolean,
    lastMessageTime: number,
    isOwnerStop: boolean
  };
  msg?: string;
  code?: string;
}

/**
 * 客戶最後進線資料響應
 */
export interface AgentServicedResponse {
  success: boolean;
  data?: {
    warned: boolean,
    result: boolean,
    transferFlag: boolean,
    serviceNumberAgentId: string,
    startTime: number,
    sessionStatus: SessionStatus,
    isOwnerStop: boolean
  };
  msg?: string;
  code?: string;
}

/**
 * 開始服務請求參數
 */
export interface StartServiceRequest {
  roomId: string;  /** 聊天室ID*/
}

/**
 * 開始服務響應
 */
export interface StartServiceResponse {
  success: boolean;
  data?: {
    id: string;
    createTime: number;
    updateTime: number;
    tenantId: string;
    serviceNumberId: string;
    serviceNumberType: string;
    roomId: string;
    accountId: string;
    customerId: string;
    customerName: string;
    agentId: string;
    channel: string;
    code: string;
    scopeId: string;
    startTime: number;
    distributeTime: number;
    activeTime: number;
    serviceTime: number;
    customerLastMessageChannel: string;
    customerLastMessageTime: number;
    agentLastMessageTime: number | null;
    agentFirstMessageTime: number | null;
    robotEndTime: number | null;
    endTime: number | null;
    status: string;
    gwSessionId: string;
    idleTime: number;
    timeoutTime: number;
    robotTimeoutTime: number | null;
    ownerAutoStopTime: number | null;
    ownerAutoTransferTime: number | null;
    warned: boolean | null;
    warningType: string | null;
    isOwnerStop: boolean | null;
    ownerStopTime: number | null;
    ownerId: string | null;
    appointChannel: string | null;
  };
  msg?: string;
  code?: string;
  timeCost?: number;
}

/**
 * 聊天消息服務類
 * 提供消息相關操作的服務
 */
class MessageService {
  /**
   * 發送聊天消息
   * @param request 發送消息請求參數
   * @returns 發送消息響應
   */
  public async fetchChatMessageSend(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      logService.info('發送聊天消息', { 
        roomId: request.roomId,
        type: request.type || 'text',
        contentLength: typeof request.content === 'string' ? request.content.length : JSON.stringify(request.content).length
      });
      
      const requestData = {
        roomId: request.roomId,
        content: request.content,
        type: request.type || 'text',
        metadata: request.metadata || {},
      };
      
      const encryptedResponse = await firstValueFrom(
        httpService.post<string>(
          ConstantUtil.API_CHAT_MESSAGE_SEND,
          requestData,
          {},
          true // 加密請求
        )
      );
      
      logService.debug('收到的消息發送響應數據', { 
        encryptedResponse, 
        type: typeof encryptedResponse 
      });
      
      // 解密 API 響應數據
      let response: SendMessageResponse;
      
      try {
        // 嘗試解密響應數據
        response = CryptoUtil.decryptApiResponse<SendMessageResponse>(encryptedResponse);
        logService.debug('消息發送響應解密成功', response);
      } catch (decryptError) {
        logService.warn('消息發送響應解密失敗，嘗試直接使用響應數據', { decryptError });
        // 如果解密失敗，直接使用原始響應
        response = encryptedResponse as any;
      }
      
      if (response?.success) {
        logService.info('消息發送成功', {
          id: response.data?.id,
          sequence: response.data?.sequence,
          senderName: response.data?.senderName,
          senderId: response.data?.senderId,
          accountId: response.data?.accountId,
          sendTime: response.data?.sendTime,
          roomId: response.data?.roomId,
          tenantId: response.data?.tenantId,
          channel: response.data?.channel
        });
        return response;
      } else {
        throw new Error(response?.msg || '發送消息失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('發送消息異常', { error: errorMsg, stack: error?.stack });
      return {
        success: false,
        msg: errorMsg,
        code: error?.response?.data?.code || '-1'
      };
    }
  }

  /**
   * 獲取聊天消息內容
   * @param request 獲取消息請求參數
   * @returns 獲取消息響應
   */
  public async fetchBaseSyncMessage(request: SyncMessageRequest): Promise<SyncMessageResponse> {
    try {
      logService.info('獲取聊天消息內容', { 
        roomId: request.roomId,
        pageIndex: request.pageIndex || 0,
        pageSize: request.pageSize || 20,
        refreshTime: request.refreshTime
      });
      
      
      const encryptedResponse = await firstValueFrom(
        httpService.post<string>(
          ConstantUtil.API_BASE_SYNC_MESSAGE,
          request,
          {},
          true // 加密請求
        )
      );
      
      logService.debug('收到的消息內容響應數據', { 
        encryptedResponse, 
        type: typeof encryptedResponse 
      });
      
      // 解密 API 響應數據
      let response: SyncMessageResponse;
      
      try {
        // 嘗試解密響應數據
        response = CryptoUtil.decryptApiResponse<SyncMessageResponse>(encryptedResponse);
        logService.debug('消息內容響應解密成功', {
          success: response.success,
          messageCount: response.data?.items?.length || 0,
          total: response.data?.total || 0,
          hasNextPage: response.data?.hasNextPage,
          refreshTime: response.data?.refreshTime
        });
      } catch (decryptError) {
        logService.warn('消息內容響應解密失敗，嘗試直接使用響應數據', { decryptError });
        // 如果解密失敗，直接使用原始響應
        response = encryptedResponse as any;
      }
      
      if (response?.success) {
        logService.info('獲取消息內容成功', {
          messageCount: response.data?.items?.length || 0,
          total: response.data?.total || 0,
          hasMore: response.data?.hasMore,
          hasNextPage: response.data?.hasNextPage,
          refreshTime: response.data?.refreshTime
        });
        return response;
      } else {
        throw new Error(response?.msg || '獲取消息內容失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('獲取消息內容異常', { error: errorMsg, stack: error?.stack });
      return {
        success: false,
        msg: errorMsg,
        code: error?.response?.data?.code || '-1'
      };
    }
  }

  /**
   * 批量獲取所有聊天消息（自動處理分頁和刷新時間）
   * @param roomId 聊天室ID
   * @param initialRefreshTime 初始刷新時間戳
   * @param pageSize 每頁大小
   * @param maxPages 最大頁數限制，防止無限循環
   * @param saveToDb 是否保存到數據庫
   * @returns 批量獲取結果
   */
  public async fetchAllMessages(
    roomId: string,
    initialRefreshTime: number = 0,
    pageSize: number = 50,
    maxPages: number = 10,
    saveToDb: boolean = false
  ): Promise<BatchFetchResult> {
    const allMessages: MessageData[] = [];
    let currentRefreshTime = initialRefreshTime;
    let currentPage = 0;
    let hasNextPage = true;
    let success = true;
    let errorMsg: string | undefined;
    let savedCount = 0;

    logService.info('開始批量獲取聊天消息', { 
      roomId,
      initialRefreshTime,
      pageSize,
      maxPages,
      saveToDb
    });

    try {
      // 循環獲取所有頁面的數據
      while (hasNextPage && currentPage < maxPages) {
        const request: SyncMessageRequest = {
          roomId,
          pageIndex: currentPage,
          pageSize,
          refreshTime: currentRefreshTime,
          direction: 'desc' // 默認降序，最新消息在前
        };

        const response = await this.fetchBaseSyncMessage(request);

        if (!response.success) {
          success = false;
          errorMsg = response.msg;
          logService.error('批量獲取消息失敗', { 
            page: currentPage,
            error: errorMsg
          });
          break;
        }

        // 添加當前頁的消息到結果列表
        if (response.data?.items && response.data.items.length > 0) {
          allMessages.push(...response.data.items);
          
          // 如果需要保存到數據庫
          if (saveToDb) {
            const savedResult = await this.saveMessagesToDb(response.data.items);
            savedCount += savedResult.successCount;
            
            logService.debug('已保存消息到數據庫', {
              pageIndex: currentPage,
              savedCount: savedResult.successCount,
              failedCount: savedResult.failedCount
            });
          }
          
          // 更新刷新時間（如果API返回了刷新時間）
          if (response.data.refreshTime) {
            currentRefreshTime = response.data.refreshTime;
          } else {
            // 如果API沒有返回刷新時間，則使用消息中的最大更新時間
            const maxUpdateTime = Math.max(
              ...response.data.items
                .filter(msg => msg.updateTime !== undefined)
                .map(msg => msg.updateTime as number),
              currentRefreshTime
            );
            currentRefreshTime = maxUpdateTime;
          }
          
          logService.debug('更新刷新時間', { 
            currentRefreshTime,
            messagesCount: response.data.items.length
          });
        }

        // 檢查是否有下一頁
        hasNextPage = response.data?.hasNextPage === true || response.data?.hasMore === true;
        
        // 如果沒有更多數據，退出循環
        if (!hasNextPage || (response.data?.items && response.data.items.length === 0)) {
          logService.info('沒有更多消息，結束批量獲取', { 
            totalFetched: allMessages.length,
            currentPage
          });
          break;
        }

        currentPage++;
        logService.debug('獲取下一頁消息', { nextPage: currentPage });
      }

      // 如果達到最大頁數限制但仍有更多數據
      if (currentPage >= maxPages && hasNextPage) {
        logService.warn('達到最大頁數限制，停止獲取更多消息', { 
          maxPages,
          totalFetched: allMessages.length
        });
      }

      logService.info('批量獲取消息完成', { 
        totalMessages: allMessages.length,
        latestRefreshTime: currentRefreshTime,
        pagesProcessed: currentPage + 1,
        savedToDb: saveToDb,
        savedCount
      });

      return {
        allMessages,
        latestRefreshTime: currentRefreshTime,
        success,
        errorMsg,
        savedCount: saveToDb ? savedCount : undefined
      };
    } catch (error: any) {
      const errorMessage = error?.message || '批量獲取消息時發生未知錯誤';
      logService.error('批量獲取消息異常', { 
        error: errorMessage,
        stack: error?.stack
      });
      
      return {
        allMessages,
        latestRefreshTime: currentRefreshTime,
        success: false,
        errorMsg: errorMessage,
        savedCount: saveToDb ? savedCount : undefined
      };
    }
  }

  /**
   * 將消息批量保存到數據庫
   * @param messages 消息列表
   * @returns 保存結果
   */
  public async saveMessagesToDb(messages: MessageData[]): Promise<{
    successCount: number;
    failedCount: number;
    errors: Error[];
  }> {
    try {
      // 調用 MessageDao 的實現
      return await messageDao.saveMessagesToDb(messages);
    } catch (error: any) {
      logService.error('調用 MessageDao 保存消息失敗', { error: error.message || error });
      // 返回錯誤結果
      return {
        successCount: 0,
        failedCount: messages.length,
        errors: [error]
      };
    }
  }

  /**
   * 將所有消息保存到數據庫
   * @param roomId 聊天室ID
   * @param refreshTime 刷新時間
   * @param pageSize 每頁大小
   * @param maxPages 最大頁數
   * @returns 保存結果
   */
  public async syncMessagesToDb(
    roomId: string,
    refreshTime: number = 0,
    pageSize: number = 50,
    maxPages: number = 10
  ): Promise<{
    success: boolean;
    savedCount: number;
    totalMessages: number;
    latestRefreshTime: number;
    errorMsg?: string;
  }> {
    try {
      logService.info('開始同步消息到數據庫', {
        roomId,
        refreshTime,
        pageSize,
        maxPages
      });

      // 使用 fetchAllMessages 獲取所有消息並保存到數據庫
      const result = await this.fetchAllMessages(
        roomId,
        refreshTime,
        pageSize,
        maxPages,
        true // 啟用保存到數據庫
      );

      return {
        success: result.success,
        savedCount: result.savedCount || 0,
        totalMessages: result.allMessages.length,
        latestRefreshTime: result.latestRefreshTime,
        errorMsg: result.errorMsg
      };
    } catch (error: any) {
      const errorMessage = error?.message || '同步消息到數據庫時發生未知錯誤';
      logService.error('同步消息到數據庫異常', {
        error: errorMessage,
        stack: error?.stack
      });
      
      return {
        success: false,
        savedCount: 0,
        totalMessages: 0,
        latestRefreshTime: refreshTime,
        errorMsg: errorMessage
      };
    }
  }

  /**
   * 獲取客戶最後進線資料
   * @param request 請求參數 
   * @returns 消息響應
   */
  public async fromAppoint(request: FromAppointRequest): Promise<FromAppointResponse> {
    try {
      logService.info('獲取客戶最後進線資料', { roomId: request.roomId });
      
      const requestData = {
        roomId: request.roomId
      };
      
      const encryptedResponse = await firstValueFrom(
        httpService.post<string>(
          ConstantUtil.API_FROM_APPOINT,
          requestData,
          {},
          true // 加密請求
        )
      );
      
      logService.debug('收到客戶最後進線資料響應數據', { 
        encryptedResponse, 
        type: typeof encryptedResponse 
      });
      
      // 解密 API 響應數據
      let response: FromAppointResponse;
      
      try {
        // 嘗試解密響應數據
        response = CryptoUtil.decryptApiResponse<FromAppointResponse>(encryptedResponse);
        logService.debug('客戶最後進線資料響應解密成功', {
          success: response.success,
          data: response.data
        });
      } catch (decryptError) {
        logService.warn('客戶最後進線資料響應解密失敗，嘗試直接使用響應數據', { decryptError });
        // 如果解密失敗，直接使用原始響應
        response = encryptedResponse as any;
      }
      
      if (response?.success) {
        return response;
      } else {
        throw new Error(response?.msg || '獲取客戶最後進線資料失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('獲取客戶最後進線資料異常', { error: errorMsg, stack: error?.stack });
      return {
        success: false,
        msg: errorMsg,
        code: error?.response?.data?.code || '-1'
      };
    }
  }

  /**
   * 開始客戶服務
   * @param request 請求參數
   * @returns 服務響應
   */
  public async startService(request: StartServiceRequest): Promise<StartServiceResponse> {
    try {
      logService.info('開始客戶服務', { roomId: request.roomId });
      
      const requestData = {
        roomId: request.roomId
      };
      
      const encryptedResponse = await firstValueFrom(
        httpService.post<string>(
          ConstantUtil.API_START_SERVICE,
          requestData,
          {},
          true // 加密請求
        )
      );
      
      logService.debug('收到開始服務響應數據', { 
        encryptedResponse, 
        type: typeof encryptedResponse 
      });
      
      // 解密 API 響應數據
      let response: StartServiceResponse;
      
      try {
        // 嘗試解密響應數據
        response = CryptoUtil.decryptApiResponse<StartServiceResponse>(encryptedResponse);
        logService.debug('開始服務響應解密成功', {
          success: response.success,
          data: response.data?.id
        });
      } catch (decryptError) {
        logService.warn('開始服務響應解密失敗，嘗試直接使用響應數據', { decryptError });
        // 如果解密失敗，直接使用原始響應
        response = encryptedResponse as any;
      }
      
      if (response?.success) {
        return response;
      } else {
        throw new Error(response?.msg || '開始客戶服務失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('開始客戶服務異常', { error: errorMsg, stack: error?.stack });
      return {
        success: false,
        msg: errorMsg,
        code: error?.response?.data?.code || '-1'
      };
    }
  }

/**
 * 檢查狀態並開始服務
 * 只有當聊天室狀態為 DistributeActive 時才會開始服務
 * 
 * @param roomId 聊天室ID
 * @returns 包含狀態檢查和服務啟動結果
 */
public async checkAndStartService(roomId: string): Promise<{
  success: boolean;
  sessionStatus?: SessionStatus;
  serviceResponse?: StartServiceResponse;
  msg?: string;
  code?: string;
}> {
  try {
    // 先檢查聊天室狀態
    const appointResponse = await this.fromAppoint({ roomId });
    
    if (!appointResponse.success || !appointResponse.data) {
      return {
        success: false,
        msg: appointResponse.msg || '獲取聊天室狀態失敗',
        code: appointResponse.code
      };
    }
    
    // 檢查狀態是否為 DistributeActive
    const status = appointResponse.data.sessionStatus;
    
    if (status !== SessionStatus.DistributeActive) {
      return {
        success: false,
        sessionStatus: status,
        msg: `聊天室狀態非待分配狀態，當前為: ${status}`,
        code: 'INVALID_STATUS'
      };
    }
    
    // 調用開始服務
    const serviceResponse = await this.startService({ roomId });
    
    return {
      success: serviceResponse.success,
      sessionStatus: status,
      serviceResponse: serviceResponse,
      msg: serviceResponse.msg,
      code: serviceResponse.code
    };
    
  } catch (error: any) {
    const errorMsg = error?.message || '檢查狀態並開始服務失敗';
    logService.error('檢查狀態並開始服務失敗', { error: errorMsg, roomId, stack: error?.stack });
    
    return {
      success: false,
      msg: errorMsg,
      code: 'INTERNAL_ERROR'
    };
  }
}

/**
 * 直接從數據庫獲取消息
 * @param roomId 聊天室ID
 * @param page 頁碼
 * @param pageSize 每頁大小
 * @param sequence 序列號（用於分頁）
 * @param sort 排序方向
 * @returns 消息列表
 */
public async getMessagesFromDB(
  roomId: string,
  page: number = 0,
  pageSize: number = ConstantUtil.DEFAULT_PAGE_SIZE,
  sequence?: number,
  sort: 'asc' | 'desc' = 'desc'
): Promise<Messages[]> {
  try {
    logService.info('從數據庫獲取消息', {
      roomId,
      page,
      pageSize,
      sequence,
      sort
    });
    
    return await messageDao.fetchMessagesFromDB(roomId, page, pageSize, sequence, sort);
  } catch (error) {
    logService.error('從數據庫獲取消息失敗', {
      error,
      roomId,
      page,
      pageSize
    });
    return [];
  }
}

  /**
   * 設置消息已讀
   * @param request 請求參數 { roomId }
   * @returns 響應 { success, msg, code }
   */
  public async messageRead(request: { roomId: string }): Promise<{ success: boolean; msg?: string; code?: string }> {
    try {
      logService.info('設置消息已讀', { roomId: request.roomId });
      const requestData = { roomId: request.roomId };
      const encryptedResponse = await firstValueFrom(
        httpService.post<string>(
          ConstantUtil.API_CHAT_MESSAGE_READ,
          requestData,
          {},
          true // 加密請求
        )
      );
      logService.debug('收到設置消息已讀響應數據', { encryptedResponse, type: typeof encryptedResponse });
      let response: { success: boolean; msg?: string; code?: string };
      try {
        response = CryptoUtil.decryptApiResponse(encryptedResponse);
        logService.debug('設置消息已讀響應解密成功', response);
      } catch (decryptError) {
        logService.warn('設置消息已讀響應解密失敗，嘗試直接使用響應數據', { decryptError });
        response = encryptedResponse as any;
      }
      if (response?.success) {
        return response;
      } else {
        throw new Error(response?.msg || '設置消息已讀失敗');
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
      logService.error('設置消息已讀異常', { error: errorMsg, stack: error?.stack });
      return {
        success: false,
        msg: errorMsg,
        code: error?.response?.data?.code || '-1'
      };
    }
  }

    /**
   * 獲取agent服務
   * @param request 請求參數 { roomId }
   * @returns 響應 { success, msg, code }
   */
    public async agentServiced(request: { roomId: string }): Promise<AgentServicedResponse> {
      try {
        logService.info('獲取agent服務', { roomId: request.roomId });
        const requestData = { roomId: request.roomId };
        const encryptedResponse = await firstValueFrom(
          httpService.post<string>(
            ConstantUtil.API_AGENT_SERVICED,
            requestData,
            {},
            true // 加密請求
          )
        );
        logService.debug('收到agent服務響應數據', { encryptedResponse, type: typeof encryptedResponse });
        let response: AgentServicedResponse;
        try {
          response = CryptoUtil.decryptApiResponse(encryptedResponse);
          logService.debug('agent服務響應解密成功', response);
        } catch (decryptError) {
          logService.warn('agent服務響應解密失敗，嘗試直接使用響應數據', { decryptError });
          response = encryptedResponse as any;
        }
        if (response?.success) {
          return response;
        } else {
          throw new Error(response?.msg || '獲取agent服務失敗');
        }
      } catch (error: any) {
        const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
        logService.error('獲取agent服務異常', { error: errorMsg, stack: error?.stack });
        return {
          success: false,
          msg: errorMsg,
          code: error?.response?.data?.code || '-1'
        };
      }
    }

    /**
     * 結束服務
     * @param request 請求參數 { roomId }
     * @returns 響應 { success, data, msg, code, timeCost }
     */
    public async agentStop(request: { roomId: string }): Promise<{
      success: boolean;
      data?: boolean;
      msg?: string;
      code?: string;
      timeCost?: number;
      status?: number;
    }> {
      try {
        logService.info('結束服務', { roomId: request.roomId });
        const requestData = { roomId: request.roomId };
        const encryptedResponse = await firstValueFrom(
          httpService.post<string>(
            ConstantUtil.API_AGENT_STOP,
            requestData,
            {},
            true // 加密請求
          )
        );
        logService.debug('收到結束服務響應數據', { encryptedResponse, type: typeof encryptedResponse });
        let response: { success: boolean; data?: boolean; msg?: string; code?: string; timeCost?: number; status?: number; };
        try {
          response = CryptoUtil.decryptApiResponse(encryptedResponse);
          logService.debug('結束服務響應解密成功', response);
        } catch (decryptError) {
          logService.warn('結束服務響應解密失敗，嘗試直接使用響應數據', { decryptError });
          response = encryptedResponse as any;
        }
        if (response?.success) {
          return response;
        } else {
          throw new Error(response?.msg || '結束服務失敗');
        }
      } catch (error: any) {
        const errorMsg = error?.response?.data?.msg || error?.message || '網絡請求失敗';
        logService.error('結束服務異常', { error: errorMsg, stack: error?.stack });
        return {
          success: false,
          msg: errorMsg,
          code: error?.response?.data?.code || '-1'
        };
      }
    }

  /**
   * 优化的聊天室消息获取方法
   * 支持序列号范围查询和智能缓存
   * @param roomId 房間ID
   * @param options 查询选项
   * @returns 消息列表和元数据
   */
  public async getMessagesOptimized(
    roomId: string,
    options: {
      type: 'latest' | 'range' | 'history';
      limit?: number;
      startSequence?: number;
      endSequence?: number;
      page?: number;
      pageSize?: number;
    }
  ): Promise<{
    messages: MessageData[];
    hasMore: boolean;
    sequenceRange: { min: number; max: number } | null;
    source: 'database';
  }> {
    try {
      let messages: Messages[] = [];
      let hasMore = false;
      let sequenceRange: { min: number; max: number } | null = null;

      switch (options.type) {
        case 'latest':
          // 获取最新消息（用于初始化）
          messages = await messageDao.fetchLatestMessages(roomId, options.limit || 20, options.startSequence);
          hasMore = messages.length >= (options.limit || 20);
          break;

        case 'range':
          // 序列号范围查询（用于精确获取）
          if (options.startSequence !== undefined && options.endSequence !== undefined) {
            messages = await messageDao.fetchMessagesBySequenceRange(
              roomId,
              options.startSequence,
              options.endSequence,
              options.limit || 50
            );
            hasMore = messages.length >= (options.limit || 50);
          }
          break;

        case 'history':
          // 历史消息查询（用于加载更多）- 使用 beforeSequence 参数
          messages = await messageDao.fetchMessagesFromDB(
            roomId,
            options.page || 0,
            options.pageSize || ConstantUtil.DEFAULT_PAGE_SIZE,
            options.startSequence, // 这里作为 beforeSequence 使用
            'desc'
          );
          hasMore = messages.length >= (options.pageSize || ConstantUtil.DEFAULT_PAGE_SIZE);
          break;
      }

      // 计算序列号范围
      if (messages.length > 0) {
        const sequences = messages
          .map(msg => msg.sequence || 0)
          .filter(seq => seq > 0)
          .sort((a, b) => a - b);

        if (sequences.length > 0) {
          sequenceRange = {
            min: sequences[0],
            max: sequences[sequences.length - 1]
          };
        }
      }

      // 转换为标准格式
      const formattedMessages: MessageData[] = messages.map(msg => ({
        id: msg.id,
        type: (msg.type as 'text' | 'image' | 'file' | 'system' | 'location') || 'text',
        senderName: msg.senderName || '',
        senderId: msg.senderId || '',
        content: msg.content || '',
        roomId: msg.roomId || roomId,
        sendTime: (msg.sendTime || Date.now()).toString(),
        sequence: msg.sequence || 0,
        status: 'sent' as const, // 数据库中的消息默认为已发送状态
        accountId: msg.accountId,
        sourceType: msg.sourceType,
        osType: msg.osType,
        channel: msg.channel,
        appointChannel: msg.appointChannel,
        tag: msg.tag,
        themeId: msg.themeId,
        nearMessageId: msg.nearMessageId,
        sessionId: msg.sessionId,
        channelMessageId: msg.channelMessageId,
        flag: msg.flag
      }));

      logService.info('✅ 优化消息查询完成', {
        roomId,
        type: options.type,
        resultCount: formattedMessages.length,
        hasMore,
        sequenceRange,
        options
      });

      return {
        messages: formattedMessages,
        hasMore,
        sequenceRange,
        source: 'database'
      };

    } catch (error) {
      logService.error('优化消息查询失败', {
        error: error as Error,
        roomId,
        options
      });
      return {
        messages: [],
        hasMore: false,
        sequenceRange: null,
        source: 'database'
      };
    }
  }

  /**
   * 检测消息序列号缺失情况
   * @param roomId 聊天室ID
   * @param expectedLastSequence 期望的最后序列号
   * @returns 缺失分析结果
   */
  public async detectMissingSequences(
    roomId: string,
    expectedLastSequence: number
  ): Promise<{
    isComplete: boolean;
    missingSequences: number[];
    missingRanges: Array<{start: number, end: number}>;
    dbSequenceRange: {min: number, max: number} | null;
    totalMissing: number;
  }> {
    try {
      logService.info('🔍 开始检测消息序列号缺失', { roomId, expectedLastSequence });

      // 获取数据库中的所有消息序列号
      const dbMessages = await this.getMessagesFromDB(roomId, 0, 1000, undefined, 'asc');
      const sequences = dbMessages.map(msg => msg.sequence || 0).filter(seq => seq > 0).sort((a, b) => a - b);

      if (sequences.length === 0) {
        return {
          isComplete: false,
          missingSequences: Array.from({length: expectedLastSequence}, (_, i) => i + 1),
          missingRanges: [{start: 1, end: expectedLastSequence}],
          dbSequenceRange: null,
          totalMissing: expectedLastSequence
        };
      }

      const dbMinSequence = sequences[0];
      const dbMaxSequence = sequences[sequences.length - 1];
      const dbSequenceRange = { min: dbMinSequence, max: dbMaxSequence };

      // 检测缺失的序列号
      const missingSequences: number[] = [];

      // 1. 检查开头缺失的序列号 (1 到 dbMinSequence-1)
      for (let i = 1; i < dbMinSequence; i++) {
        missingSequences.push(i);
      }

      // 2. 检查中间缺失的序列号
      for (let i = dbMinSequence; i <= dbMaxSequence; i++) {
        if (!sequences.includes(i)) {
          missingSequences.push(i);
        }
      }

      // 3. 检查末尾缺失的序列号 (dbMaxSequence+1 到 expectedLastSequence)
      for (let i = dbMaxSequence + 1; i <= expectedLastSequence; i++) {
        missingSequences.push(i);
      }

      // 将缺失的序列号分组为连续范围
      const missingRanges = this.groupSequencesToRanges(missingSequences);

      const isComplete = missingSequences.length === 0;

      logService.info('📊 序列号缺失检测完成', {
        roomId,
        expectedLastSequence,
        dbSequenceRange: `${dbMinSequence}-${dbMaxSequence}`,
        dbMessagesCount: dbMessages.length,
        totalMissing: missingSequences.length,
        missingRanges: missingRanges.map(range => `${range.start}-${range.end}`).join(', '),
        isComplete
      });

      return {
        isComplete,
        missingSequences,
        missingRanges,
        dbSequenceRange,
        totalMissing: missingSequences.length
      };

    } catch (error) {
      logService.error('❌ 检测消息序列号缺失失败', { error, roomId, expectedLastSequence });
      return {
        isComplete: false,
        missingSequences: [],
        missingRanges: [],
        dbSequenceRange: null,
        totalMissing: 0
      };
    }
  }

  /**
   * 将序列号数组分组为连续范围
   * @param sequences 序列号数组
   * @returns 连续范围数组
   */
  private groupSequencesToRanges(sequences: number[]): Array<{start: number, end: number}> {
    if (sequences.length === 0) return [];

    const ranges: Array<{start: number, end: number}> = [];
    let start = sequences[0];
    let end = sequences[0];

    for (let i = 1; i < sequences.length; i++) {
      if (sequences[i] === end + 1) {
        // 连续序列号，扩展当前范围
        end = sequences[i];
      } else {
        // 非连续，保存当前范围并开始新范围
        ranges.push({ start, end });
        start = sequences[i];
        end = sequences[i];
      }
    }

    // 添加最后一个范围
    ranges.push({ start, end });

    return ranges;
  }
}

// 導出消息服務實例
export const messageService = new MessageService();
export default messageService;