require_relative '../../node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '15.5'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

# 增加网络超时时间和重试次数
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

def capacitor_pods
  pod 'Capacitor', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorCommunityMedia', :path => '../../node_modules/@capacitor-community/media'
  pod 'CapacitorCommunitySafeArea', :path => '../../node_modules/@capacitor-community/safe-area'
  pod 'CapacitorCommunitySqlite', :path => '../../node_modules/@capacitor-community/sqlite'
  pod 'CapacitorMlkitBarcodeScanning', :path => '../../node_modules/@capacitor-mlkit/barcode-scanning'
  pod 'CapacitorApp', :path => '../../node_modules/@capacitor/app'
  pod 'CapacitorCamera', :path => '../../node_modules/@capacitor/camera'
  pod 'CapacitorDevice', :path => '../../node_modules/@capacitor/device'
  pod 'CapacitorFilesystem', :path => '../../node_modules/@capacitor/filesystem'
  pod 'CapacitorPushNotifications', :path => '../../node_modules/@capacitor/push-notifications'
  pod 'CapacitorShare', :path => '../../node_modules/@capacitor/share'
  pod 'CapacitorStatusBar', :path => '../../node_modules/@capacitor/status-bar'
  pod 'AileCapacitorLineLogin', :path => '../../node_modules/aile-capacitor-line-login'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
end

# post_install do |installer|
#   assertDeploymentTarget(installer)
# end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # 為了相容性，強制所有 Pods 使用 Swift 5
      config.build_settings['SWIFT_VERSION'] = '5.0'
      # 清除任何可能導致 Swift 6 嚴格模式的條件
      config.build_settings['SWIFT_ACTIVE_COMPILATION_CONDITIONS'] = ''
      
      # 強制設置iOS部署目標為15.5，避免版本不一致的警告
      if target.platform_name == :ios
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.5'
      end
    end
  end
end