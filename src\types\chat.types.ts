import { Type } from '@/services';
import type { TransTenantVO, RelationTenantVO } from '@/services/core/tenant/tenantTypes';

/**
 * 聊天室类型枚举
 */
export enum ChatRoomType {
  CUSTOMER = 'customer',
  GROUP = 'group',
  SYSTEM = 'system',
  SUPPORT = 'support',
  MY = 'my',
  TEAM = 'team'
}

/**
 * 消息发送者接口
 */
export interface MessageSender {
  id: string;
  name: string;
  avatar?: string;
}

/**
 * 聊天消息接口
 */
export interface ChatMessage {
  id: string;
  content: string;
  time: string;
  timestamp: string;
  isUser: boolean;
  avatarSrc?: string;
  avatarText?: string;
  sender: MessageSender;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  type?: Type;
  metadata?: Record<string, any>;
  channel?: string;
  sequence?: number; // 添加消息序列号，用于已读已到状态管理
  templateId?: string; // 添加模板ID，用于跟踪当前用户发送的消息
}

/**
 * 为了兼容现有代码，提供IChatMessage接口
 */
export interface IChatMessage extends ChatMessage {}

/**
 * 日期消息组接口
 */
export interface DateMessageGroup {
  date: string;
  messages: IChatMessage[];
}

/**
 * 聊天室数据接口
 */
export interface ChatRoomData {
  title: string;
  dateGroups: DateMessageGroup[];
}

/**
 * 聊天室组件属性接口
 */
export interface IChatRoomProps {
  type: ChatRoomType;
  title: string;
  dateGroups: DateMessageGroup[];
  roomId: string; // 新增 roomId 屬性
  roomInfo?: {  // 添加 roomInfo 可選屬性
    id?: string;
    name?: string;
    avatarId?: string;
    last_sequence?: number;
    lastSequence?: number;
    ownerId?: string;
    [key: string]: any;
  };
  showTasksBar?: boolean;
  taskCount?: number;
  showTeamSelector?: boolean;
  teamUnreadCount?: number;
  teamAvatarSrc?: string;
  onBackClick?: () => void;
  memberCount?: number;
  onTeamChatClick?: () => void;
  isLoadingRoomInfo?: boolean; // 添加 roomInfo 加载状态
}

export interface LogInAccount {
  name: string;
  accountId: string;
  countryCode: string;
  mobile: string;
  tokenId: string;
  loginType: string;
  onlineId: string;
  transTenant: TransTenantVO | null;
  tenantInfo: RelationTenantVO[];
  systemRoomId: string;
  personRoomId: string;
  systemAccountId: string;
  status: string;
  isInitial: boolean;
  [key: string]: any;
}

export interface LoginTenant {
  id: string;
  createTime: number;
  updateTime: number;
  name: string;
  shortName: string;
  code: string;
  description: string | null;
  type: string;
  startTime: number | null;
  endTime: number | null;
  industry: string | null;
  industrySub: string | null;
  scale: string | null;
  city: string | null;
  address: string | null;
  phone: string | null;
  website: string | null;
  avatarId: string | null;
  accountId: string;
  upgrade: boolean | null;
  unifiedNumber: string | null;
  representativeNumber: string | null;
  certificateFileId: string | null;
  certificateStatus: string | null;
  certificateFailReason: string | null;
  switchTime: number | null;
  [key: string]: any;
} 