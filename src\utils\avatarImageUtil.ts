import { attachmentService } from '@/services/core/attachment/attachmentService';
import { ConstantUtil } from '@/utils/constantUtil';
import { getUserForage } from '@/utils/userForage';
import { logService } from '@/services/system/logService';
import { firstValueFrom } from 'rxjs';
import stateService from '@/services/stateService';

/**
 * 將 Blob 轉換為 base64 字串
 * @param blob Blob 對象
 * @returns base64 字串
 */
export function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * 下載並快取頭像圖片，回傳 base64 字串
 * @param avatarId 頭像ID
 * @param size 頭像顯示尺寸
 * @param apiSize API 調用時的尺寸參數，默認為 's'
 * @returns base64 字串
 */
export async function downloadAndCacheAvatarImage(
  avatarId: string, 
  size: string,
  apiSize: string = 's'
): Promise<string | null> {
  const loginAccount = stateService.loginAccount();
  if (!loginAccount?.accountId) {
    logService.error('下載頭像失敗：未獲取到當前用戶 accountId');
    return null;
  }
  
  const accountId = loginAccount.accountId;
  const key = ConstantUtil.AVATAR_IMAGE_CACHE_KEY(avatarId, size);
  const forage = getUserForage(accountId);
  
  // 添加超時保護，避免無限等待
  const timeoutPromise = new Promise<null>((_, reject) => {
    setTimeout(() => reject(new Error('頭像下載超時')), 30000); // 30秒超時
  });
  
  try {
    // 先查快取
    const cached = await forage.getItem<string>(key);
    if (cached) {
      //logService.info('頭像圖片快取命中', { key });
      return cached;
    }

    // 使用 base64 API 獲取頭像
    logService.info('使用 base64 API 獲取頭像', { avatarId, size, apiSize });
    
    // 添加超時保護
    const base64Result = await Promise.race([
      firstValueFrom(attachmentService.fetchAvatarBase64({ avatarId, size: apiSize })),
      timeoutPromise
    ]) as string | null;
    
    if (base64Result) {
      // 檢查 base64 格式是否正確
      if (typeof base64Result === 'string' && (
          base64Result.startsWith('data:image') || 
          base64Result.startsWith('/9j/') || // JPEG 的 base64 開頭
          base64Result.startsWith('iVBOR') // PNG 的 base64 開頭
        )) {
        
        // 確保 base64 格式正確（添加前綴如果需要）
        let finalBase64 = base64Result;
        if (!base64Result.startsWith('data:image')) {
          finalBase64 = `data:image/jpeg;base64,${base64Result}`;
        }
        
        // 保存到快取
        await forage.setItem(key, finalBase64);
        logService.info('頭像圖片 base64 API 獲取並快取成功', { key });
        return finalBase64;
      } else {
        logService.warn('頭像 base64 API 返回格式不正確', { 
          resultType: typeof base64Result,
          resultPreview: typeof base64Result === 'string' ? base64Result.substring(0, 20) + '...' : 'non-string'
        });
        throw new Error('頭像 base64 API 返回格式不正確');
      }
    } else {
      throw new Error('頭像 base64 API 返回空數據');
    }
  } catch (err) {
    // 處理所有錯誤，包括超時
    const errorMessage = err instanceof Error ? err.message : '未知錯誤';
    logService.error('頭像圖片下載/快取失敗', { key, error: errorMessage });
    
    // 返回 null 表示獲取失敗
    return null;
  }
} 