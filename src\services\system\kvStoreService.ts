import { logService } from './logService';
import kvStoreDao from '../db/dao/KVStoreDao';
import type { KVStore } from '../db/initSql';

/**
 * KVStore 服務類
 * 提供鍵值對存儲相關的業務邏輯
 */
class KVStoreService {
  /**
   * 獲取值
   * @param key 鍵名
   * @returns 值或 null
   */
  public async getValue(key: string): Promise<string | null> {
    try {
      if (!key) {
        logService.warn('獲取 KV 值時 key 為空');
        return null;
      }
      
      return await kvStoreDao.getValue(key);
    } catch (error) {
      logService.error('獲取 KV 值失敗', { error: error as Error, key });
      return null;
    }
  }

  /**
   * 保存值
   * @param key 鍵名
   * @param value 值
   * @returns 是否保存成功
   */
  public async setValue(key: string, value: string): Promise<boolean> {
    try {
      if (!key) {
        logService.warn('保存 KV 值時 key 為空');
        return false;
      }
      
      return await kvStoreDao.saveValue(key, value);
    } catch (error) {
      logService.error('保存 KV 值失敗', { error: error as Error, key });
      return false;
    }
  }

  /**
   * 刪除值
   * @param key 鍵名
   * @returns 是否刪除成功
   */
  public async deleteValue(key: string): Promise<boolean> {
    try {
      if (!key) {
        logService.warn('刪除 KV 值時 key 為空');
        return false;
      }
      
      return await kvStoreDao.deleteValue(key);
    } catch (error) {
      logService.error('刪除 KV 值失敗', { error: error as Error, key });
      return false;
    }
  }

  /**
   * 獲取數值類型的值
   * @param key 鍵名
   * @param defaultValue 默認值
   * @returns 數值或默認值
   */
  public async getNumber(key: string, defaultValue: number = 0): Promise<number> {
    try {
      const value = await this.getValue(key);
      if (value === null) {
        return defaultValue;
      }
      
      const num = Number(value);
      return isNaN(num) ? defaultValue : num;
    } catch (error) {
      logService.error('獲取數值類型的 KV 值失敗', { error: error as Error, key });
      return defaultValue;
    }
  }

  /**
   * 獲取布林類型的值
   * @param key 鍵名
   * @param defaultValue 默認值
   * @returns 布林值或默認值
   */
  public async getBoolean(key: string, defaultValue: boolean = false): Promise<boolean> {
    try {
      const value = await this.getValue(key);
      if (value === null) {
        return defaultValue;
      }
      
      return value === 'true';
    } catch (error) {
      logService.error('獲取布林類型的 KV 值失敗', { error: error as Error, key });
      return defaultValue;
    }
  }

  /**
   * 獲取 JSON 類型的值
   * @param key 鍵名
   * @param defaultValue 默認值
   * @returns 解析後的對象或默認值
   */
  public async getJSON<T>(key: string, defaultValue: T): Promise<T> {
    try {
      const value = await this.getValue(key);
      if (value === null) {
        return defaultValue;
      }
      
      return JSON.parse(value) as T;
    } catch (error) {
      logService.error('獲取 JSON 類型的 KV 值失敗', { error: error as Error, key });
      return defaultValue;
    }
  }

  /**
   * 保存 JSON 類型的值
   * @param key 鍵名
   * @param value 值
   * @returns 是否保存成功
   */
  public async setJSON<T>(key: string, value: T): Promise<boolean> {
    try {
      return await this.setValue(key, JSON.stringify(value));
    } catch (error) {
      logService.error('保存 JSON 類型的 KV 值失敗', { error: error as Error, key });
      return false;
    }
  }

  /**
   * 獲取根據前綴匹配的所有 KV 值
   * @param prefix 前綴
   * @returns 匹配的 KV 值列表
   */
  public async getValuesByPrefix(prefix: string): Promise<KVStore[]> {
    try {
      if (!prefix) {
        logService.warn('獲取前綴匹配的 KV 值時 prefix 為空');
        return [];
      }
      
      return await kvStoreDao.getValuesByPrefix(prefix);
    } catch (error) {
      logService.error('獲取前綴匹配的 KV 值失敗', { error: error as Error, prefix });
      return [];
    }
  }

  /**
   * 獲取所有 KV 值
   * @returns 所有 KV 值列表
   */
  public async getAllValues(): Promise<KVStore[]> {
    try {
      return await kvStoreDao.getAllValues();
    } catch (error) {
      logService.error('獲取所有 KV 值失敗', { error: error as Error });
      return [];
    }
  }

  /**
   * 儲存刷新時間（用於增量同步）
   * @param key 鍵名前綴，通常是資源類型
   * @param id 資源ID，通常是表名或服務ID
   * @param refreshTime 刷新時間戳
   * @returns 是否保存成功
   */
  public async saveRefreshTime(key: string, id: string, refreshTime: number): Promise<boolean> {
    try {
      const fullKey = `${key}_refresh_time_${id}`;
      return await this.setValue(fullKey, refreshTime.toString());
    } catch (error) {
      logService.error('保存刷新時間失敗', { error: error as Error, key, id, refreshTime });
      return false;
    }
  }

  /**
   * 獲取刷新時間（用於增量同步）
   * @param key 鍵名前綴，通常是資源類型
   * @param id 資源ID，通常是表名或服務ID
   * @param defaultTime 默認時間，如果未找到值
   * @returns 刷新時間或默認時間
   */
  public async getRefreshTime(key: string, id: string, defaultTime: number = 0): Promise<number> {
    try {
      const fullKey = `${key}_refresh_time_${id}`;
      return await this.getNumber(fullKey, defaultTime);
    } catch (error) {
      logService.error('獲取刷新時間失敗', { error: error as Error, key, id });
      return defaultTime;
    }
  }
}

// 導出單例實例
export default new KVStoreService(); 