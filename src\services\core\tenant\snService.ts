import { ConstantUtil } from '@/utils/constantUtil';
import { getLocalStorage, setLocalStorage } from '@/utils/storage';
import { logService } from '../../system/logService';
import httpService from '../../system/httpService';
import CryptoUtil from '@/utils/cryptoUtil';
import { firstValueFrom } from 'rxjs';
import serviceNumberDao from '../../db/dao/ServiceNumberDao';

import stateService from '../../stateService';

export interface IServiceNumberInfo {
  id: string;
  name: string;
  type: string;
  [key: string]: any;
}

class SnService {
  /**
   * 獲取指定租戶的商務號信息
   * @param tenantId 租戶ID
   * @returns 商務號資料（如失敗返回 null）
   */
  public async getBossServiceNumberInfo(tenantId: string): Promise<IServiceNumberInfo | null> {
    if (!tenantId) {
      logService.warn('獲取商務號ID失敗：租戶ID為空');
      return null;
    }
    try {
      logService.info('開始獲取個人租戶的商務號', { tenantId });
      const response = await firstValueFrom(
        httpService.post(
          ConstantUtil.API_TENANT_SN_PERSON_BOSS.replace('{tenantId}', tenantId),
          {},
          {},
          true
        )
      );
      const decryptedResponse = CryptoUtil.decryptApiResponse(response);
      if (decryptedResponse.success && decryptedResponse.data && decryptedResponse.data.id) {
        logService.info('成功獲取個人租戶的商務號', { tenantId });

        // 只有當前租戶時才更新localStorage和stateService
        const currentTenantId = getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
        if (tenantId === currentTenantId) {
          setLocalStorage(ConstantUtil.BOSS_SERVICENUMBERID, decryptedResponse.data.id);
          serviceNumberDao.upsertServiceNumber(decryptedResponse.data);
        }

        return decryptedResponse.data as IServiceNumberInfo;
      } else {
        logService.warn('獲取個人租戶的商務號失敗', {
          tenantId,
          status: decryptedResponse.status,
          message: decryptedResponse.msg
        });
        return null;
      }
    } catch (error) {
      logService.error('獲取個人租戶的商務號發生異常', { error: error as Error, tenantId });
      return null;
    }
  }

  /**
   * 獲取當前租戶的商務號信息（無參數版本）
   * @returns 商務號資料（如失敗返回 null）
   */
  public async getCurrentBossServiceNumber(): Promise<IServiceNumberInfo | null> {
    const currentTenantId = getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
    if (!currentTenantId) {
      logService.warn('獲取當前商務號失敗：當前租戶ID為空');
      return null;
    }

    return this.getBossServiceNumberInfo(currentTenantId);
  }

   /**
   * 獲取個人租戶的商務號
   * @returns 返回商務號ID，如果不存在則返回null
   */
   public async getBossServiceNumberId(): Promise<string | null> {
    const bossId = stateService.bossServiceNumberId();
    
    // 如果 stateService 中沒有值，嘗試從 localStorage 獲取
    if (!bossId) {
      const localStorageBossId = getLocalStorage<string | null>(ConstantUtil.BOSS_SERVICENUMBERID, null);
      logService.debug('從 localStorage 獲取的 bossServiceNumberId', { localStorageBossId });
      
      if (localStorageBossId) {
        // 如果 localStorage 中有值，更新 stateService
        stateService.setBossServiceNumberId(localStorageBossId);
        return localStorageBossId;
      }
      
      // 如果 localStorage 也沒有值，嘗試重新獲取商務號信息
      const bossInfo = await this.getCurrentBossServiceNumber();
      if (bossInfo && bossInfo.id) {
        logService.debug('重新獲取的商務號ID', { bossServiceNumberId: bossInfo.id });
        return bossInfo.id;
      }
    }
    
    return bossId;
  }

  /**
   * 根據ID獲取服務號信息
   * @param serviceNumberId 服務號ID
   * @returns 服務號信息，如果不存在則返回 null
   */
  public async getServiceNumberById(serviceNumberId: string): Promise<IServiceNumberInfo | null> {
    if (!serviceNumberId) {
      logService.warn('獲取服務號信息失敗：服務號ID為空');
      return null;
    }

    try {
      // 先從本地數據庫查詢
      const serviceNumber = await serviceNumberDao.getServiceNumberById(serviceNumberId);
      if (serviceNumber) {
        return serviceNumber;
      }

      // 本地沒有數據，可以考慮從API獲取（這裡暫不實現API查詢，需要時可以添加）
      logService.warn('未找到指定ID的服務號信息', { serviceNumberId });
      return null;
    } catch (error) {
      logService.error('獲取服務號信息時發生錯誤', { error: error as Error, serviceNumberId });
      return null;
    }
  }
}

const snService = new SnService();
export default snService;