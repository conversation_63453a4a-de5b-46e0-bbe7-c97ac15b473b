// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-media')
    implementation project(':capacitor-community-safe-area')
    implementation project(':capacitor-community-sqlite')
    implementation project(':capacitor-mlkit-barcode-scanning')
    implementation project(':capacitor-app')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-share')
    implementation project(':capacitor-status-bar')
    implementation project(':aile-capacitor-line-login')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
