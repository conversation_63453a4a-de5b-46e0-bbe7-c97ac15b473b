---
type: "agent_requested"
---

# Rule Name: aileApiNamingRule

## API 方法命名規則（最新版）

1. **接口路徑轉方法名**
   - 將 API 路徑中的 `/` 轉為單詞分隔，去除如 openapi 等前綴，直接反映業務語意。
   - 例：`/openapi/tenant/relation/list` → `fetchTenantRelationList`

2. **動詞前綴**
   - 從 API 伺服器獲取資料：`fetchXxx`（如 fetchTenantRelationList）
   - 本地查詢（DB/記憶體）：`getXxx`（如 getTenantById）
   - 新增：`createXxx`
   - 更新：`updateXxx`
   - 刪除：`deleteXxx`
   - 本地同步：`syncXxxToDb`（如 syncTenantsToDb）

3. **語意明確**
   - 方法名需直觀反映業務含義，副作用需體現於命名。

4. **參數設計**
   - 僅傳遞接口實際需要的參數，不多傳、不硬編碼。
   - 若接口不需 accountId，方法簽名不帶該參數。

5. **常量管理**
   - API 路徑常量統一在 ConstantUtil 中定義，命名為 `API_` + 路徑語意大寫。

6. **異步與副作用**
   - 所有 API 方法均為 async，返回 Promise。
   - 有本地資料同步時，方法名需體現（如 syncTenantsToDb）。

7. **可複用性**
   - API 方法應可被多處調用（如登入、初始化、切換租戶等場景）。

8. **測試覆蓋**
   - 每個 API 方法都需有對應單元測試，覆蓋正常與異常分支。

## 命名示例

| 類型         | 方法名                    | 常量名                        | 說明                       |
|--------------|--------------------------|-------------------------------|----------------------------|
| API查詢      | fetchTenantRelationList  | API_TENANT_RELATION_LIST      | 從API獲取租戶關聯列表      |
| 本地查詢     | getTenantById            |                               | 從本地DB查詢租戶           |
| 新增         | createPersonTenant       | API_TENANT_PERSON_CREATE      | 新增個人租戶               |
| 更新         | updateUserProfile        | API_USER_PROFILE_UPDATE       | 更新用戶資料               |
| 刪除         | deleteTenant             |                               | 刪除租戶                   |
| 本地同步     | syncTenantsToDb          | API_TENANT_RELATION_LIST      | 同步租戶到本地DB           |

---

**本規則適用於所有 API 服務層方法，違反將不通過 Code Review。**
