/**
 * 认证上下文
 * 提供全局的认证状态管理
 */

import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

// 用户类型 - 移自 types/services
interface User {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  tenantId?: string;
}

// 认证状态类型
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

// 认证操作类型
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'AUTH_CLEAR_ERROR' };

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
};

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return { ...state, loading: true, error: null };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.payload,
      };
    case 'AUTH_LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null,
      };
    case 'AUTH_CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
}

// 上下文类型
interface AuthContextType extends AuthState {
  login: (credentials: any) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
}

// 创建上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider组件Props
interface AuthProviderProps {
  children: ReactNode;
}

// Provider组件
export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // 登录方法
  const login = async (credentials: any) => {
    dispatch({ type: 'AUTH_START' });
    try {
      // TODO: 实现实际的登录逻辑
      // const result = await authService.login(credentials);
      // dispatch({ type: 'AUTH_SUCCESS', payload: result });
      console.log('Login with:', credentials);
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE', payload: (error as Error).message });
    }
  };

  // 登出方法
  const logout = async () => {
    try {
      // TODO: 实现实际的登出逻辑
      // await authService.logout();
      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // 清除错误
  const clearError = () => {
    dispatch({ type: 'AUTH_CLEAR_ERROR' });
  };

  // 初始化时检查认证状态
  useEffect(() => {
    // TODO: 检查本地存储的token并验证
  }, []);

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
