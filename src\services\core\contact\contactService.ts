import { logService } from '../../system/logService';
import type { Contact } from '../../db/initSql';
import { ConstantUtil } from '../../../utils/constantUtil';
import httpService from '../../system/httpService';
import { firstValueFrom } from 'rxjs';
import CryptoUtil from '../../../utils/cryptoUtil';
import { contactDao } from '../../db/dao';
import kvStoreService from '../../system/kvStoreService';
import aileDBService from '../../db/aileDBService';
import stateService from '../../stateService';
// import { addContacts } from '../../app/slices/contactSlice';
// import { safeDispatch } from '../../app/storeUtils';

/**
 * Contact API 請求參數
 */
interface ContactListParams {
  pageIndex?: number;
  pageSize?: number;
  direction?: string;
  orderBy?: string;
  key?: string;
  refreshTime?: number;
  serviceNumberId?: string;
}

/**
 * Contact API 響應數據
 */
interface ContactApiResponse {
  status: number;
  success: boolean;
  data: {
    items: Contact[];
    count: number;
    hasNextPage: boolean;
    pageIndex: number;
    refreshTime: number;
  };
  msg: string;
  code: string;
}

/**
 * 客戶服務類
 * 提供客戶相關操作的服務
 */
class ContactService {
  /**
   * 從API獲取客戶列表
   * @param params 查詢參數
   * @returns 客戶列表API響應
   */
  public async fetchTenantContactList(params: ContactListParams = {}): Promise<ContactApiResponse> {
    const {
      pageIndex = 0,
      pageSize = 20,
      direction = 'asc',
      orderBy = 'id',
      key,
      refreshTime = 0,
      serviceNumberId
    } = params;

    try {
      const startTime = Date.now();
      
      const requestData: ContactListParams = {
        pageIndex,
        pageSize,
        direction,
        orderBy,
        refreshTime
      };

      // 添加可選參數
      if (key) {
        requestData.key = key;
      }
      
      if (serviceNumberId) {
        requestData.serviceNumberId = serviceNumberId;
      }


      const responseRaw = await firstValueFrom(
        httpService.post<any>(
          ConstantUtil.API_TENANT_CONTACT_LIST,
          requestData,
          {},
          true
        )
      );

      // 解密 API 響應數據
      let response;
      
      // 判斷響應是否需要解密
      try {
        response = CryptoUtil.decryptApiResponse<ContactApiResponse>(responseRaw);
      } catch {
        response = responseRaw;
      }

      const timeCost = Date.now() - startTime;
      
      logService.info('從API獲取客戶列表成功', {
        count: response.data?.items?.length || 0,
        timeCost,
        params: requestData
      });

      return response;
    } catch (error) {
      logService.error('從API獲取客戶列表失敗', { error: error as Error, params });
      
      // 返回錯誤響應格式
      return {
        status: -1,
        success: false,
        data: {
          items: [],
          count: 0,
          hasNextPage: false,
          pageIndex: 0,
          refreshTime: 0
        },
        msg: (error as Error).message || '獲取客戶列表失敗',
        code: '-1'
      };
    }
  }

  /**
   * 根據ID獲取客戶詳情
   * @param id 客戶ID
   * @returns 客戶詳情
   */
  public async getContactById(id: string): Promise<Contact | null> {
    try {
      // 首先嘗試從本地DB獲取客戶詳情
      const localContact = await contactDao.getContactById(id);
      
      // 如果在本地DB中找到，直接返回
      if (localContact) {
        logService.debug('從本地DB獲取客戶詳情成功', { id });
        return localContact;
      }
      
      // 本地DB沒有數據，嘗試從API獲取
      logService.info('本地DB未找到客戶詳情，嘗試從API獲取', { id });
      
      // 呼叫API獲取客戶資料
      const response = await firstValueFrom(
        httpService.post<any>(
          ConstantUtil.API_TENANT_CONTACT_ITEM.replace('{id}', id),
          {},
          {},
          true
        )
      );
      
      // 解密 API 響應數據
      let apiResponse;
      try {
        apiResponse = CryptoUtil.decryptApiResponse<any>(response);
      } catch {
        apiResponse = response;
      }
      
      // 檢查API響應是否成功且有數據
      if (!apiResponse.success || !apiResponse.data) {
        logService.warn('從API獲取客戶詳情失敗或無數據', { 
          id, 
          success: apiResponse.success,
          msg: apiResponse.msg 
        });
        return null;
      }
      
      // 將API返回的客戶資料轉換為Contact格式
      const contact: Contact = apiResponse.data;
      
      // 將從API獲取的客戶資料保存到本地DB
      await this.saveContact(contact);
      
      logService.info('從API獲取客戶詳情成功並已保存到本地DB', { id });
      return contact;
    } catch (error) {
      logService.error('獲取客戶詳情失敗', { error: error as Error, id });
      return null;
    }
  }

  /**
   * 保存客戶信息到數據庫
   * @param contact 客戶信息
   * @returns 是否保存成功
   */
  public async saveContact(contact: Contact): Promise<boolean> {
    try {
      // 使用 contactDao 保存客戶信息
      return await contactDao.saveContact(contact);
    } catch (error) {
      logService.error('保存客戶信息失敗', { error: error as Error, contact });
      return false;
    }
  }

  /**
   * 批量保存客戶信息到數據庫
   * @param contacts 客戶信息列表
   * @returns 成功保存的客戶數量
   */
  public async saveContactsToDb(contacts: Contact[]): Promise<number> {
    if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
      logService.warn('沒有客戶信息需要保存');
      return 0;
    }

    try {
      logService.info('開始將客戶信息保存到數據庫', { count: contacts.length });
      
      // 使用 contactDao 批量保存客戶信息
      const successCount = await contactDao.saveContacts(contacts);

      // 批量寫入後保存到持久存儲
      if (successCount > 0) {
        try {
          await this.saveToStoreWithRetry();
          logService.debug('客戶信息批量寫入後已保存到持久存儲', { successCount });
        } catch (saveError) {
          logService.warn('保存到持久存儲失敗，但客戶信息已寫入數據庫', { 
            error: saveError as Error, 
            successCount 
          });
          // 不拋出異常，因為資料已成功寫入內存數據庫
        }
      }

      logService.info('客戶信息保存完成', { totalCount: contacts.length, successCount });
      return successCount;
    } catch (error) {
      logService.error('批量保存客戶信息失敗', { error: error as Error });
      return 0;
    }
  }
  
  /**
   * 帶重試邏輯的持久化數據保存
   * @private
   */
  private async saveToStoreWithRetry(retries = 2): Promise<void> {
    let lastError;
    
    for (let i = 0; i <= retries; i++) {
      try {
        if (i > 0) {
          logService.info(`嘗試第${i}次重新保存客戶數據到持久存儲`);
          // 重試前短暫延遲
          await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        await aileDBService.saveToStore();
        return; // 成功保存，退出函數
      } catch (error) {
        lastError = error;
        logService.warn(`持久化客戶數據失敗，嘗試重試 (${i}/${retries})`, { error });
      }
    }
    
    // 所有重試都失敗
    throw lastError;
  }

  /**
   * 異步獲取客戶資料並存儲到DB
   * @param serviceNumberId 服務號ID
   * @returns 保存成功的客戶數量
   */
  public async syncContactsToDb(serviceNumberId?: string): Promise<number> {
    try {
      // 添加調試日誌
      logService.debug('contactService.syncContactsToDb 被調用', { serviceNumberId });
      
      // 獲取上次刷新時間，用於增量同步
      const lastRefreshTime = await kvStoreService.getRefreshTime('contact', serviceNumberId || 'default');
      
      logService.info('開始同步客戶資料', { serviceNumberId, lastRefreshTime });
      
      let pageIndex = 0;
      let hasNextPage = true;
      let totalSavedCount = 0;
      let latestRefreshTime = 0;
      
      // 循環獲取所有頁面的數據
      while (hasNextPage) {
        // 從 API 獲取客戶列表
        const response = await this.fetchTenantContactList({
          serviceNumberId,
          refreshTime: lastRefreshTime,
          pageSize: 100, // 增大每頁數量以提高效率
          pageIndex
        });
        
        if (!response.success || !response.data || !Array.isArray(response.data.items)) {
          logService.warn('API 未返回有效的客戶資料', { response, pageIndex });
          break;
        }
        
        const { items, refreshTime, hasNextPage: morePages } = response.data;
        
        // 更新最新的刷新時間
        if (refreshTime && refreshTime > latestRefreshTime) {
          latestRefreshTime = refreshTime;
        }
        
        if (items.length === 0) {
          logService.info('當前頁面沒有新的客戶資料需要同步', { pageIndex });
          break;
        }
        
        // 保存客戶資料到數據庫
        const savedCount = await this.saveContactsToDb(items);
        totalSavedCount += savedCount;
        
        logService.info('客戶資料頁面同步完成', { 
          pageIndex, 
          total: items.length, 
          saved: savedCount
        });
        
        // 更新分頁相關變數
        hasNextPage = morePages;
        if (hasNextPage) {
          pageIndex += 1;
          logService.debug('還有更多客戶資料，繼續獲取下一頁', { nextPage: pageIndex });
        }
      }
      
      // 保存本次刷新時間
      if (latestRefreshTime) {
        await kvStoreService.saveRefreshTime('contact', serviceNumberId || 'default', latestRefreshTime);
      }
      
      logService.info('客戶資料同步全部完成', { 
        totalPages: pageIndex + 1,
        totalSaved: totalSavedCount,
        refreshTime: latestRefreshTime
      });
      
      // 最後再次通知所有聯繫人數據已更新（確保所有監聽器都能收到通知）
      stateService.notifyContactListChanged();
      
      return totalSavedCount;
    } catch (error) {
      logService.error('同步客戶資料失敗', { error: error as Error, serviceNumberId });
      return 0;
    }
  }

  /**
   * 獲取客戶列表
   * @param params 查詢參數
   * @returns 客戶列表
   */
  public async getContactList(params: {
    tenantId?: string;
    serviceNumberId?: string;
    key?: string;
    pageIndex?: number;
    pageSize?: number;
  } = {}): Promise<Contact[]> {
    try {
      // 使用 contactDao 獲取聯絡人列表
      return await contactDao.getContactList(params);
    } catch (error) {
      logService.error('獲取客戶列表失敗', { error: error as Error, params });
      return [];
    }
  }

  /**
   * 刪除客戶
   * @param id 客戶ID
   * @returns 是否刪除成功
   */
  public async deleteContact(id: string): Promise<boolean> {
    try {
      // 使用 contactDao 刪除聯絡人
      return await contactDao.deleteContact(id);
    } catch (error) {
      logService.error('刪除客戶失敗', { error: error as Error, id });
      return false;
    }
  }

  /**
   * 更新聯繫人頭像信息
   * @param contactId 聯繫人ID
   * @param avatarId 新的頭像ID
   */
  public async updateContactAvatar(contactId: string, avatarId: string): Promise<boolean> {
    try {
      // 1. 更新本地數據庫
      const contact = await contactDao.getContactById(contactId);
      if (!contact) {
        logService.warn('更新頭像失敗：找不到聯繫人', { contactId });
        return false;
      }
      
      contact.avatarId = avatarId;
      contact.updateTime = Date.now();
      
      await contactDao.saveContact(contact);
      
      // 2. 更新 Redux store
      // safeDispatch(addContacts([contact]));
      
      // 3. 清除頭像緩存
      // 使用 AvatarImage 組件自帶的緩存機制，這裏只需要通知更新
      
      // 4. 通知聯繫人頭像已更新
      stateService.notifyContactListChanged();
      
      logService.info('聯繫人頭像更新成功', { contactId, avatarId });
      return true;
    } catch (error) {
      logService.error('更新聯繫人頭像失敗', { error, contactId });
      return false;
    }
  }
  
  /**
   * 刷新指定聯繫人的信息（從API獲取最新數據）
   * @param contactId 聯繫人ID
   */
  public async refreshContactInfo(contactId: string): Promise<boolean> {
    try {
      // 強制忽略本地緩存，從API獲取聯繫人詳情
      // 這裡我們直接使用 API 接口調用，而不是 getContactById (因為它會先查詢本地)
      const response = await firstValueFrom(
        httpService.post<any>(
          ConstantUtil.API_TENANT_CONTACT_ITEM.replace('{id}', contactId),
          {},
          {},
          true
        )
      );
      
      // 解密 API 響應數據
      let apiResponse;
      try {
        apiResponse = CryptoUtil.decryptApiResponse<any>(response);
      } catch {
        apiResponse = response;
      }
      
      // 檢查API響應是否成功且有數據
      if (!apiResponse.success || !apiResponse.data) {
        logService.warn('刷新聯繫人信息失敗：API返回錯誤', { 
          contactId, 
          success: apiResponse.success,
          msg: apiResponse.msg 
        });
        return false;
      }
      
      const contactInfo = apiResponse.data;
      
      // 確保有ID字段
      if (!contactInfo.id) {
        contactInfo.id = contactId;
      }
      
      // 保存到數據庫
      await contactDao.saveContact(contactInfo);
      
      // 更新 Redux store
      // safeDispatch(addContacts([contactInfo]));
      
      // 通知聯繫人數據已更新
      stateService.notifyContactListChanged();
      
      logService.info('聯繫人信息刷新成功', { contactId });
      return true;
    } catch (error) {
      logService.error('刷新聯繫人信息失敗', { error, contactId });
      return false;
    }
  }
}

// 導出單例實例
export default new ContactService(); 