import React from 'react';
import './MessageTabSkeleton.css';

/**
 * MessageTab骨架屏组件
 * 模拟MessageTab的布局结构
 */
const MessageTabSkeleton: React.FC = () => {
  return (
    <div className="message-tab-skeleton">
      {/* Filter Tabs 骨架 */}
      <div className="message-skeleton-filter-tabs">
        <div className="message-skeleton-filter-tab message-skeleton-filter-tab-active">
          <div className="skeleton-line skeleton-line-short"></div>
        </div>
        <div className="message-skeleton-filter-tab">
          <div className="skeleton-line skeleton-line-short"></div>
        </div>
        <div className="message-skeleton-filter-tab">
          <div className="skeleton-line skeleton-line-short"></div>
        </div>
      </div>

      {/* List Items 骨架 */}
      <div className="message-skeleton-list">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="message-skeleton-list-item">
            <div className="skeleton-item-avatar"></div>
            <div className="skeleton-item-content">
              <div className="skeleton-item-header">
                <div className="skeleton-line skeleton-line-medium"></div>
                <div className="skeleton-line skeleton-line-mini"></div>
              </div>
              <div className="skeleton-line skeleton-line-long"></div>
            </div>
            <div className="skeleton-item-badge"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MessageTabSkeleton;
