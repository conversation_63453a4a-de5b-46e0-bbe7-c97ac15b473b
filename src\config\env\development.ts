import { baseConfig, BaseConfig } from './base';

/**
 * 开发环境配置
 */
export interface DevelopmentConfig extends BaseConfig {
  API_BASE_URL: string;
  VITE_BFF_GRAPHQL_URL: string;
  SOCKET_URL: string;
  SOCKET_PATH: string;
  VITE_LINE_CHANNEL_ID: string;
  VITE_LINE_REDIRECT_URI: string;
  DEBUG: boolean;
}

export const developmentConfig: DevelopmentConfig = {
  ...baseConfig,
  
  // 开发环境特定配置
  API_BASE_URL: 'https://newaile.dev.aile.cloud',
  SOCKET_URL: 'https://newaile.dev.aile.cloud/aile',
  VITE_BFF_GRAPHQL_URL : 'https://newaile.dev.aile.cloud/graphql',
  // API_BASE_URL: 'http://35.236.166.137:8000/',
  // SOCKET_URL: 'http://35.236.166.137:8000/aile',
  SOCKET_PATH: '/socketio/socket.io',
  VITE_LINE_CHANNEL_ID: '2007765389',
  VITE_LINE_REDIRECT_URI: 'cloud.aile.aile:/callback',
  DEBUG: false,
  
  // 开发环境覆盖配置
  LOG_LEVEL: 'debug',
  API_TIMEOUT: 0, // 开发环境延长超时时间
};
