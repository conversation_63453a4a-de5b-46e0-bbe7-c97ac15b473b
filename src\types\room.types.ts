import { P1WeightEnum, P2WeightEnum, P3WeightEnum } from './aile.enum';
import { SessionStatus } from '@/services/core/chat/roomService';

// 房間權重同步請求參數
export interface IWeightRoomListRequest {
  direction?: string;
  key?: string;
  orderBy?: string;
  pageIndex?: number;
  pageSize?: number;
  refreshTime?: number;
  serviceNumberId?: string;
  weight?: number;
  [property: string]: any;
}

// 服務房間列表請求參數
export interface IServiceRoomListRequest {
  direction?: string;
  key?: string;
  orderBy?: string;
  pageIndex?: number;
  pageSize?: number;
  refreshTime?: number;
  serviceNumberId?: string;
  [property: string]: any;
}

/**
 * 聊天室权重DTO
 */
export interface RoomWeightDto {
  p1?: P1WeightEnum;
  p2?: P2WeightEnum;
  p3?: P3WeightEnum[];
  timestamp?: number;
}

/**
 * 权重计算结果
 */
export interface WeightCalculationResult {
  score: number;
  p1: number;
  p2: number;
  p3: number;
  p4: number;
}

/**
 * Score解析结果
 */
export interface ScoreParseResult {
  p1: number;
  p2: number;
  p3: number;
  p4: number;
}

// ===== 以下内容从 roomService.types.ts 合并而来 =====

export interface Response {
    code?: string;
    data?: WeightRoomListVORoomVO;
    msg?: string;
    status?: number;
    success?: boolean;
    timeCost?: number;
    [property: string]: any;
}

export interface WeightRoomListVORoomVO {
    count?: number;
    hasNextPage?: boolean;
    items?: RoomVO[];
    totalCount?: number;
    weight?: number;
    [property: string]: any;
}

export interface RoomVO {
    accountId?: string;
    agentId?: string;
    avatarId?: string;
    businessDescription?: string;
    businessEndTime?: number;
    businessId?: string;
    businessName?: string;
    businessStatus?: string;
    createTime?: number;
    deleted?: boolean;
    dfrTime?: number;
    homePagePicId?: string;
    id?: string;
    isCustomName?: boolean;
    isExternal?: boolean;
    isMute?: boolean;
    isOwnerStop?: boolean;
    isTop?: boolean;
    isTransfer?: boolean;
    lastMessage?: string;
    lastSequence?: number;
    mainRoomId?: string;
    member_deleted?: boolean;
    memberCount?: number;
    memberIds?: string[];
    name?: string;
    ownerId?: string;
    provisionalIds?: string[];
    serviceNumberId?: string;
    sessionId?: string;
    sessionStatus?: SessionStatus ;
    status?: Status;
    tenantId?: string;
    topTime?: number;
    transferReason?: string;
    type?: string;
    unreadCount?: number;
    updateTime?: number;
    warned?: boolean;
    weight?: number;
    /**
     * UI 置頂（Pin）狀態，僅前端用，與 DB isTop 區分
     */
    isPin?: boolean;
    [property: string]: any;
}

export enum Status {
    Block = "Block",
    Complaint = "Complaint",
    Danger = "Danger",
    Delete = "Delete",
    Disable = "Disable",
    Enable = "Enable",
    Forbid = "Forbid",
}

export interface RobotListResponse {
    code?: string;
    data?: BaseListVORoomVO;
    msg?: string;
    status?: number;
    success?: boolean;
    timeCost?: number;
    [property: string]: any;
}

export interface BaseListVORoomVO {
    count?: number;
    hasNextPage?: boolean;
    items?: RoomVO[];
    pageIndex?: number;
    refreshTime?: number;
    totalCount?: number;
    [property: string]: any;
}

/**
 * 聊天室類型枚舉
 */
export enum RoomType {
  Person = 'person',
  AccountPerson = 'accountPerson',
  Friend = 'friend',
  Discuss = 'discuss',
  Group = 'group',
  Services = 'services',
  ServiceMember = 'serviceMember',
  AileSystem = 'aileSystem',
  AiwowSystem = 'aiwowSystem',
  Business = 'business',
}

/**
 * 同步聊天室成員請求參數
 */
export interface SyncChatMemberRequest {
  roomId: string;
}

/**
 * 聊天室成員資料
 */
export interface ChatMemberVO {
  id?: string;
  createTime?: number;
  updateTime?: number;
  memberId?: string;
  type?: string;
  roomId?: string;
  roomType?: string;
  accountId?: string;
  tenantId?: string;
  status?: string;
  privilege?: string;
  lastReceivedSequence?: number;
  lastReadSequence?: number;
  firstSequence?: number;
  dfrTime?: number | null;
  mute?: boolean | null;
  top?: boolean | null;
  topTime?: number | null;
  mainRoomId?: string | null;
  serviceNumberId?: string | null;
  lastMessage?: {
    id?: string;
    type?: string;
    sourceType?: string;
    senderName?: string;
    senderId?: string;
    accountId?: string;
    sendTime?: number;
    roomId?: string;
    sequence?: number;
    tenantId?: string;
    osType?: string | null;
    channel?: string;
    appointChannel?: string | null;
    content?: string;
    tag?: string | null;
    themeId?: string | null;
    nearMessageId?: string | null;
    sessionId?: string | null;
    channelMessageId?: string | null;
    excludeMemberIds?: string[] | null;
    recipientId?: string | null;
    recipientAccountId?: string | null;
    flag?: string | null;
  };
  [property: string]: any;
}

/**
 * 同步聊天室成員響應數據
 */
export interface SyncChatMemberData {
  totalCount?: number;
  count?: number;
  hasNextPage?: boolean;
  pageIndex?: number;
  refreshTime?: number;
  items?: ChatMemberVO[];
  [property: string]: any;
}

/**
 * 同步聊天室成員響應
 */
export interface SyncChatMemberResponse {
  status?: number;
  success?: boolean;
  data?: SyncChatMemberData;
  msg?: string;
  code?: string;
  timeCost?: number;
  [property: string]: any;
}