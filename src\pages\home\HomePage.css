.home-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #E4F4FD;
}

/* Header styles */
.header {
  background-color: #E4F4FD;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header.visible {
  transform: translateY(0);
}

.header.hidden {
  transform: translateY(-100%);
}



.user-info {
  display: flex;
  align-items: center;
  padding: 4px 16px;
  padding-top: 4px; 
  gap: 12px;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  max-height: 40px;
  opacity: 1;
  overflow: hidden;
}

.user-info.hidden {
  opacity: 0;
  padding: 0 16px;
}

.user-avatar {
  width: 32px !important;
  height: 32px !important;
  border-radius: 4px !important;
  font-size: 16px !important;
}

.user-name {
  flex: 1;
}

.name {
  font-family: 'SF Pro', sans-serif;
  font-weight: 590;
  font-size: 18px;
  color: #333333;
}

.points {
  display: flex;
  align-items: center;
  gap: 2px;
}

.points-icon {
  width: 14px;
  height: 14px;
}

.points-value, .points-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #1677FF;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 12px;
  transition: padding-top 0.3s ease;
}

/* 當用戶信息隱藏時，nav-bar需要額外的頂部間距 */
.header.user-info-hidden .nav-bar {
  padding-top: 16px;
}

/* 桌面端（网页版）的nav-bar间距调整 */
@media (min-width: 769px) or (pointer: fine) {
  .header.user-info-hidden .nav-bar {
    padding-top: 16px;
  }
}

.nav-left {
  display: flex;
  align-items: center;
}

.dropdown {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dropdown-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 590;
  font-size: 18px;
  color: #333333;
}

.dropdown-icon {
  width: 14px;
  height: 14px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}
.nav-icon{
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.nav-icon-search{
  width: 16.18px;
  height: 16.18px;
}
.nav-icon-scan{
  width: 16.67px;
  height: 16.67px;
}
.nav-icon-more{
  width: 12.5px;
  height: 2.5px;
}

/* Filter tabs */
.filter-tabs {
  display: flex;
  padding-top: 12px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
}

.filter-tab {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 8px 0 10px;
  cursor: pointer;
}

.filter-tab.active .filter-text {
  color: #1677FF;
}

.filter-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}

.dot-badge {
  width: 10px;
  height: 10px;
  background-color: #FF3141;
  border-radius: 50%;
  position: relative;
  display: inline-block;
  margin-left: 4px;
}

.active-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1677FF;
}

/* Chat list */
.chat-list {
  flex: 1;
  overflow-y: auto;
  background-color: #FFFFFF;
  height: calc(100vh - 180px);
  -webkit-overflow-scrolling: touch; /* 为iOS添加惯性滚动效果 */
  touch-action: pan-y; /* 允许垂直方向上的触摸滚动 */
  position: relative; /* 确保滚动区域定位正确 */
}

/* 移动端滚动优化 */
.chat-list::-webkit-scrollbar {
  display: none; /* 隐藏滚动条以优化移动端体验 */
}

/* 确保在移动端触摸响应 */
.chat-list {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.tab-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'SF Pro', sans-serif;
  font-size: 18px;
  color: #666666;
  background-color: #FFFFFF;
  height: 100%;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.chat-item:first-child {
  padding-top: 24px;
}

.chat-avatar {
  width: 44px !important;
  height: 44px !important;
  border-radius: 22px !important;
}

.chat-avatar-letter {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  border-radius: 22px;
  color: #FFFFFF;
}

.chat-avatar-letter .letter {
  font-family: 'SF Pro', sans-serif;
  font-size: 17px;
}

.chat-avatar.pinned {
  border: 1px solid #F5F5F5;
}

.chat-avatar-with-status {
  position: relative;
  width: 44px;
  height: 44px;
}

.status-tag {
  position: absolute;
  bottom: 0px;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 17px;
  border-width: 1px;
  padding: 2px 4px;
  border-radius: 40px;
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 8px;
  color: #FFFFFF;
  gap: 10px;
}

.timeout-tag {
  background-color: #FF3141;
}

.waiting-tag {
  background-color: #FF5B05;
}

.chat-avatar-with-status.timeout .chat-avatar {
  border: 2px solid #FF3141;
}

.chat-avatar-with-status.waiting .chat-avatar {
  border: 2px solid #FF5B05;
}

.chat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-title-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.chat-title {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 15px;
  color: #333333;
}

.team-count {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 15px;
  color: #333333;
}

.mute-icon {
  width: 14px;
  height: 14px;
}

.chat-message {
  display: flex;
  align-items: center;
  gap: 2px;
  width: 100%;
}

.ai-tag {
  font-family: 'SF Pro', sans-serif;
  font-weight: 510;
  font-size: 14px;
  color: #999999;
}

.edit-icon {
  width: 14px;
  height: 14px;
}

.message-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 13px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 220px;
}

.chat-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  height: 100%;
}

.chat-time {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
}

.badge {
  margin-top: 4px;
  height: 20px;
  border-radius: 10px !important;
  background-color: #FF3141 !important;
  color: #FFFFFF !important;
  font-size: 12px !important;
  padding: 0 4px !important;
  min-width: unset !important;
  width: auto !important;
}

.badge:has(span:not([data-content="99+"])) {
  width: 20px !important;
  height: 20px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.badge:has(span[data-content="99+"]) {
  width: 31px !important;
}

/* Task tab styles */
.task-tabs {
  --adm-color-primary: #1677FF;
  --adm-color-text: #333333;
  background-color: #FFFFFF;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 1;
}

.task-tab-title {
  position: relative;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 14px;
}

.task-tab-badge {
  position: absolute;
  right: -12px;
  top: -2px;
  --adm-badge-color: #FF3141;
  --adm-badge-size: 8px;
}

.task-list {
  flex: 1;
  background-color: #FFFFFF;
  overflow-y: auto;
  height: calc(100vh - 180px);
  --adm-color-background: #FFFFFF;
  --adm-color-border: #F5F5F5;
}

:global(.adm-list-item-content) {
  padding: 16px 16px;
}

.task-priority {
  width: 4px;
  height: 36px;
  border-radius: 2px;
  margin-right: 12px;
}

.task-priority.high {
  background-color: #FF3141;
}

.task-priority.medium {
  background-color: #FF9500;
}

.task-priority.low {
  background-color: #1677FF;
}

.task-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 4px;
}


.task-time {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
}

.task-description {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 13px;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 280px;
}

.task-item {
  padding: 0;
}

:global(.adm-list-item) {
  padding: 0;
}

/* Tab bar */
.tab-bar-wrapper {
  background: #FFFFFF;
  box-shadow: inset 0px 1px 0px 0px rgba(247, 247, 247, 1);
}

:global(.adm-tab-bar) {
  height: 80px;
  padding: 8px 0;
}

:global(.adm-tab-bar-item) {
  padding: 8px 24px;
}

.tab-icon {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

:global(.adm-tab-bar-item-title) {
  margin-top: 2px;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
}

:global(.adm-tab-bar-item-active .adm-tab-bar-item-title) {
  color: #1677FF;
}

/* 未選中的圖標樣式 */
.tab-icon img {
  opacity: 0.7;
  filter: grayscale(100%) brightness(0.5);
}

/* 選中的圖標樣式 */
.tab-icon img.active {
  opacity: 1;
  filter: brightness(0) saturate(100%) invert(39%) sepia(94%) saturate(1857%) hue-rotate(201deg) brightness(99%) contrast(105%);
}

:global(.adm-badge-wrap) {
  position: absolute;
  right: 0;
  top: -5px;
}

:global(.adm-tab-bar-item:last-child .adm-tab-bar-item-title) {
  font-size: 10px;
}

/* Tabs component override styles */
:global(.adm-tabs-tab) {
  padding: 12px 0;
  flex: 1;
}

:global(.adm-tabs-tab-line) {
  height: 2px !important;
  bottom: 0 !important;
  background-color: #1677FF !important;
}

:global(.adm-tabs-header) {
  border-bottom: 1px solid #EEEEEE;
} 

.chat-list .adm-badge-content{
  font-size: 12px !important;
  /* padding: 4px 2px !important; */
}

.task-list-wrapper {
  height: calc(100vh - 180px);
  overflow-y: auto;
}
.tab-bar-wrapper .adm-tab-bar-item{
  padding: 8px 8px 24px !important;
}