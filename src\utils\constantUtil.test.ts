import { ConstantUtil, Environment, SupportedImageFormat } from './constantUtil';

describe('ConstantUtil', () => {
  describe('Basic Constants', () => {
    it('should have correct APP_NAME', () => {
      expect(ConstantUtil.APP_NAME).toBeDefined();
    });


  });

  describe('UUID Generation', () => {
    it('should generate UUID', () => {
      const uuid = ConstantUtil.uuid();
      expect(uuid).toBeTruthy();
      expect(typeof uuid).toBe('string');
      expect(uuid.length).toBeGreaterThan(10);
    });

    it('should generate different UUIDs', () => {
      const uuid1 = ConstantUtil.uuid();
      const uuid2 = ConstantUtil.uuid();
      expect(uuid1).not.toBe(uuid2);
    });
  });

  describe('getNowFormatDate', () => {
    it('should return formatted date string', () => {
      const dateStr = ConstantUtil.getNowFormatDate();
      expect(dateStr).toMatch(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/);
    });
  });

  describe('Random Integer Generation', () => {
    it('should generate random int in range', () => {
      const min = 10;
      const max = 20;
      for (let i = 0; i < 100; i++) {
        const num = ConstantUtil.randomInt(min, max);
        expect(num).toBeGreaterThanOrEqual(min);
        expect(num).toBeLessThanOrEqual(max);
        expect(Number.isInteger(num)).toBe(true);
      }
    });
  });

  describe('Null Checking', () => {
    it('should check for null values', () => {
      expect(ConstantUtil.isNull(null)).toBe(true);
      expect(ConstantUtil.isNull(undefined)).toBe(true);
      expect(ConstantUtil.isNull('')).toBe(false);
      expect(ConstantUtil.isNull(0)).toBe(false);
      expect(ConstantUtil.isNull(false)).toBe(false);
    });

    it('should check for null numbers', () => {
      expect(ConstantUtil.isNumberNull(null)).toBe(true);
      expect(ConstantUtil.isNumberNull(undefined)).toBe(true);
      expect(ConstantUtil.isNumberNull(NaN)).toBe(true);
      expect(ConstantUtil.isNumberNull(0)).toBe(false);
      expect(ConstantUtil.isNumberNull(42)).toBe(false);
    });
  });

  describe('JSON Validation', () => {
    it('should validate JSON strings', () => {
      expect(ConstantUtil.isJson('{"name":"John","age":30}')).toBe(true);
      expect(ConstantUtil.isJson('{"invalid":}')).toBe(false);
      expect(ConstantUtil.isJson('not json')).toBe(false);
      expect(ConstantUtil.isJson({ name: 'John' })).toBe(true);
      expect(ConstantUtil.isJson(null)).toBe(false);
    });
  });

  describe('Time Formatting', () => {
    it('should format seconds to time string', () => {
      expect(ConstantUtil.second2time(65)).toBe('01:05');
      expect(ConstantUtil.second2time(3665)).toBe('01:01:05');
      expect(ConstantUtil.second2time(0)).toBe('00:00');
    });

    it('should format duration', () => {
      expect(ConstantUtil.durationFormat(65)).toBe('1:05');
      expect(ConstantUtil.durationFormat(125)).toBe('2:05');
    });
  });

  describe('File Type Detection', () => {
    it('should detect video files', () => {
      expect(ConstantUtil.isVideo('mp4')).toBe(true);
      expect(ConstantUtil.isVideo('.mp4')).toBe(true);
      expect(ConstantUtil.isVideo('MP4')).toBe(true);
      expect(ConstantUtil.isVideo('webm')).toBe(true);
      expect(ConstantUtil.isVideo('mov')).toBe(true);
      expect(ConstantUtil.isVideo('pdf')).toBe(false);
      expect(ConstantUtil.isVideo('jpg')).toBe(false);
    });

    it('should categorize file types', () => {
      expect(ConstantUtil.getFileTypeCategory('jpg')).toBe('image');
      expect(ConstantUtil.getFileTypeCategory('mp4')).toBe('video');
      expect(ConstantUtil.getFileTypeCategory('mp3')).toBe('audio');
      expect(ConstantUtil.getFileTypeCategory('pdf')).toBe('unknown');
    });
  });

  describe('Environment Validation', () => {
    it('should validate environments', () => {
      expect(ConstantUtil.isValidEnv('development')).toBe(true);
      expect(ConstantUtil.isValidEnv('staging')).toBe(true);
      expect(ConstantUtil.isValidEnv('production')).toBe(true);
      expect(ConstantUtil.isValidEnv('testing')).toBe(false);
    });
  });

  describe('Link Replacement', () => {
    it('should replace links with anchor tags', () => {
      const text = 'Visit http://example.com for more info';
      const replaced = ConstantUtil.replaceLink(text);
      expect(replaced).toBe('Visit <a href="http://example.com" target="_blank" rel="noopener noreferrer">http://example.com</a> for more info');
    });
  });

  describe('Query Parameter Extraction', () => {
    it('should extract query parameters from URL', () => {
      const url = 'https://example.com/page?id=123&name=John';
      expect(ConstantUtil.getQueryVariable(url, 'id')).toBe('123');
      expect(ConstantUtil.getQueryVariable(url, 'name')).toBe('John');
      expect(ConstantUtil.getQueryVariable(url, 'notexist')).toBeNull();
    });
  });

  // Test type exports
  describe('Type Exports', () => {
    it('should export correct type definitions', () => {
      // TypeScript compilation test
      const validEnv: Environment = 'development';
      const validImage: SupportedImageFormat = 'jpg';
      
      // This is just to avoid unused variable lint errors
      expect(typeof validEnv).toBe('string');
      expect(typeof validImage).toBe('string');
    });
  });
}); 