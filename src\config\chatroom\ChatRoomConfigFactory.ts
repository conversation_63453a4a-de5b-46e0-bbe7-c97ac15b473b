import { ChatRoomType, IChatRoomConfig } from '@/types/chat.types';
import { ConstantUtil } from '@/utils/constantUtil';

/**
 * 聊天室配置工厂类
 * 根据聊天室类型创建对应的配置
 */
export class ChatRoomConfigFactory {
  private static configs: Map<ChatRoomType, IChatRoomConfig> = new Map();

  /**
   * 初始化所有聊天室类型的配置
   */
  static initialize() {
    // 客户聊天室配置
    this.configs.set(ChatRoomType.CUSTOMER, {
      type: ChatRoomType.CUSTOMER,
      title: '客戶聊天室',
      showTasksBar: true,
      showTeamSelector: true,
      showMemberCount: false,
      allowFileUpload: true,
      allowImageUpload: true,
      allowVoiceMessage: true,
      headerStyle: 'customer',
      inputPlaceholder: 'Aa',
      backgroundColor: '#E4F4FD',
      features: {
        messageStatus: true,
        messageRetry: true,
        messageDelete: true,
        messageForward: false,
        messageQuote: false,
      },
    });

    // 系統聊天室配置
    this.configs.set(ChatRoomType.SYSTEM, {
      type: ChatRoomType.SYSTEM,
      title: '系統聊天室',
      showTasksBar: false,
      showTeamSelector: false,
      showMemberCount: false,
      allowFileUpload: false,
      allowImageUpload: false,
      allowVoiceMessage: false,
      headerStyle: 'system',
      inputPlaceholder: '輸入系統指令...',
      backgroundColor: '#F5F5F5',
      features: {
        messageStatus: false,
        messageRetry: false,
        messageDelete: false,
        messageForward: false,
        messageQuote: false,
      },
    });

    // 團隊聊天室配置
    this.configs.set(ChatRoomType.TEAM, {
      type: ChatRoomType.TEAM,
      title: '團隊聊天室',
      showTasksBar: false,
      showTeamSelector: false,
      showMemberCount: true,
      allowFileUpload: true,
      allowImageUpload: true,
      allowVoiceMessage: true,
      headerStyle: 'team',
      inputPlaceholder: '與團隊成員聊天...',
      backgroundColor: '#E4F4FD',
      features: {
        messageStatus: true,
        messageRetry: true,
        messageDelete: true,
        messageForward: true,
        messageQuote: true,
      },
    });

    // 我的聊天室配置
    this.configs.set(ChatRoomType.MY, {
      type: ChatRoomType.MY,
      title: '我的聊天室',
      showTasksBar: false,
      showTeamSelector: false,
      showMemberCount: false,
      allowFileUpload: true,
      allowImageUpload: true,
      allowVoiceMessage: true,
      headerStyle: 'default',
      inputPlaceholder: '記錄想法...',
      backgroundColor: '#E4F4FD',
      features: {
        messageStatus: false,
        messageRetry: true,
        messageDelete: true,
        messageForward: false,
        messageQuote: false,
      },
    });

    // 群組聊天室配置
    this.configs.set(ChatRoomType.GROUP, {
      type: ChatRoomType.GROUP,
      title: '群組聊天室',
      showTasksBar: false,
      showTeamSelector: false,
      showMemberCount: true,
      allowFileUpload: true,
      allowImageUpload: true,
      allowVoiceMessage: true,
      headerStyle: 'default',
      inputPlaceholder: '與群組成員聊天...',
      backgroundColor: '#E4F4FD',
      features: {
        messageStatus: true,
        messageRetry: true,
        messageDelete: true,
        messageForward: true,
        messageQuote: true,
      },
    });

    // 支援聊天室配置
    this.configs.set(ChatRoomType.SUPPORT, {
      type: ChatRoomType.SUPPORT,
      title: '技術支援',
      showTasksBar: false,
      showTeamSelector: false,
      showMemberCount: false,
      allowFileUpload: true,
      allowImageUpload: true,
      allowVoiceMessage: false,
      headerStyle: 'default',
      inputPlaceholder: '描述您的問題...',
      backgroundColor: '#E4F4FD',
      features: {
        messageStatus: true,
        messageRetry: true,
        messageDelete: false,
        messageForward: false,
        messageQuote: true,
      },
    });
  }

  /**
   * 根據類型獲取聊天室配置
   */
  static getConfig(type: ChatRoomType): IChatRoomConfig {
    if (this.configs.size === 0) {
      this.initialize();
    }

    const config = this.configs.get(type);
    if (!config) {
      throw new Error(`未找到聊天室類型 ${type} 的配置`);
    }

    return { ...config }; // 返回副本，避免外部修改
  }

  /**
   * 獲取所有支援的聊天室類型
   */
  static getSupportedTypes(): ChatRoomType[] {
    if (this.configs.size === 0) {
      this.initialize();
    }
    return Array.from(this.configs.keys());
  }

  /**
   * 檢查是否支援指定的聊天室類型
   */
  static isTypeSupported(type: string): type is ChatRoomType {
    return Object.values(ChatRoomType).includes(type as ChatRoomType);
  }

  /**
   * 根據路由參數解析聊天室類型
   */
  static parseTypeFromRoute(typeParam: string): ChatRoomType {
    if (!this.isTypeSupported(typeParam)) {
      throw new Error(`不支援的聊天室類型: ${typeParam}`);
    }
    return typeParam as ChatRoomType;
  }

  /**
   * 動態更新配置（用於主題切換等場景）
   */
  static updateConfig(type: ChatRoomType, updates: Partial<IChatRoomConfig>) {
    const currentConfig = this.getConfig(type);
    const updatedConfig = { ...currentConfig, ...updates };
    this.configs.set(type, updatedConfig);
  }
}

// 初始化配置
ChatRoomConfigFactory.initialize();

export default ChatRoomConfigFactory;
