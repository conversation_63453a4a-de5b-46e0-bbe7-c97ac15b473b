import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from './app/hooks';
import { logout } from './app/slices/authSlice';
import { clearTenantState } from './app/slices/tenantSlice';
import AppRoutes from './routes';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { HomeSkeleton, ChatRoomSkeleton } from './components/common';
import syncInitService from './services/syncInitService';
import { logService } from './services/system/logService';
import { initLogConfig } from './utils/logConfig';
import initAppService from './services/system/initAppService';
import { useNavigate, useLocation } from 'react-router-dom';
import { loadPersistedState } from './app/store';
import socketService from './services/socketService';
import { statusBarService, swipeBackService } from './services/platform';
import { ROUTES } from './config/app/routes';
import { useSidebarPreloader } from './hooks/useSidebarPreloader';
import authService from './services/core/auth/authService';
// import pushService from './services/pushService';
import stateService from './services/stateService';
import { setLocalStorage } from './utils/storage';
import { ConstantUtil } from './utils/constantUtil';

// 在开发环境中导入调试工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/debugUtils');
}

function App(): React.ReactElement {
  const dispatch = useAppDispatch();
  const [initialized, setInitialized] = React.useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const authState = useAppSelector((state) => state.auth);
  const initialPathRef = React.useRef<string>(location.pathname);

  // 使用侧边栏预加载 Hook
  useSidebarPreloader();

  // 应用恢复处理函数
  const handleAppResume = async () => {
    try {
      // 检查用户是否已登录
      if (!authState.isAuthenticated || !authState.authToken) {
        logService.info('用户未登录，跳过恢复处理');
        return;
      }

      // 验证token有效性
      const isTokenValid = await authService.checkAccountToken();
      if (!isTokenValid) {
        logService.warn('Token已失效，执行强制登出');
        dispatch(logout());
        dispatch(clearTenantState());
        navigate(ROUTES.AUTH.LOGIN, { replace: true });
        return;
      }

      // Token有效，恢复连接和同步
      logService.info('Token有效，恢复应用状态');

      // // 恢复推送服务连接
      // if (authState.account) {
      //   pushService.initServices();
      // }

      // 恢复Socket连接
      socketService.initServices(null, null, null);

      logService.info('应用状态恢复完成');
    } catch (error) {
      logService.error('应用恢复处理失败', error as Error);
    }
  };

  // 应用暂停处理函数
  const handleAppPause = () => {
    try {
      logService.info('应用进入后台，保存状态');

      // 保存Redux状态到本地存储
      const currentState = {
        auth: {
          isAuthenticated: authState.isAuthenticated,
          authToken: authState.authToken,
          user: authState.account
        }
      };

      setLocalStorage(ConstantUtil.REDUX_STATE_KEY, currentState);
      logService.info('已保存应用状态到本地存储');

    } catch (error) {
      logService.error('应用暂停处理失败', error as Error);
    }
  };

  // 1. 全域初始化（只做一次）
  useEffect(() => {
    initLogConfig();
    socketService.initServices(null,null,null);
    // 初始化状态栏
    statusBarService.initialize();
    // 初始化边缘滑动返回服务
    swipeBackService.initialize();

    // 监听应用状态变化（前台/后台切换）
    const handleAppStateChange = (state: { isActive: boolean }) => {
      const appState = state.isActive ? 'active' : 'background';
      logService.info('应用状态变化', { state: appState });
      stateService.notifyAppStateChanged(appState);

      if (state.isActive) {
        // 应用回到前台时的处理
        logService.info('应用回到前台，检查token有效性');
        handleAppResume();
      } else {
        // 应用进入后台时的处理
        logService.info('应用进入后台，保存状态');
        handleAppPause();
      }
    };

    // 使用Capacitor的App插件监听状态变化
    import('@capacitor/app').then(({ App }) => {
      App.addListener('appStateChange', handleAppStateChange);

      // 清理函数
      return () => {
        App.removeAllListeners();
      };
    }).catch(() => {
      // Web环境下使用页面可见性API
      const handleVisibilityChange = () => {
        const isActive = !document.hidden;
        handleAppStateChange({ isActive });
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      // 清理函数
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    });
  }, []);

  // 2. 應用核心初始化
  useEffect(() => {
    const init = async () => {
      try {
        // 執行應用初始化核心
        const initResult = await initAppService.initializeCore({
          dispatch,
          navigate,
          loadPersistedState
        });

        logService.info('應用初始化結果', {
          success: initResult.success,
          hasAccountData: !!initResult.accountData,
          hasUserData: !!initResult.userData,
          hasTenantId: !!initResult.tenantId,
          hasTenantInfo: !!initResult.tenantInfo,
          error: initResult.error?.message,
          initialPath: initialPathRef.current,
          currentPath: location.pathname
        });

        // 無論成功失敗，都完成初始化階段
        setInitialized(true);

        // 如果初始化失敗，處理錯誤情況
        if (!initResult.success) {
          logService.error('應用初始化失敗', initResult.error as Error);
          navigate(ROUTES.AUTH.LOGIN, { replace: true });
          return;
        }

        // 初始化成功 - 執行數據同步
        if (initResult.success && authState.isAuthenticated && authState.authToken) {
          logService.info('初始化成功且已認證，執行數據同步');
          
          // 必須執行的同步初始化
          await syncInitService.init();

          // 优化：移除应用启动时的预加载，改为由 useSidebarPreloader 处理
          // 这样可以避免阻塞应用启动，提升整体性能
          logService.info('應用初始化完成，團隊數據將由側邊欄預加載器處理');

          // 判斷路由導航邏輯
          const isLoginRelatedPath = [
            ROUTES.AUTH.LOGIN,
            ROUTES.AUTH.OTP_LOGIN,
            ROUTES.AUTH.USER_SIGNUP,
            ROUTES.AUTH.BUSINESS_SIGNUP,
            ROUTES.AUTH.LINE_CALLBACK,
          ].includes(location.pathname as any);

          // 如果已登入但在登入相關頁面或根路徑，則導航到首頁
          if (location.pathname === '/' || isLoginRelatedPath) {
            logService.info('從登入頁或根路徑導航到首頁', {
              currentPath: location.pathname
            });
            navigate(ROUTES.HOME.DASHBOARD, { replace: true });
          }
        }
      } catch (error) {
        logService.error('應用初始化失敗', error as Error);
        setInitialized(true);
      }
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 3. 註冊強制登出回調
  useEffect(() => {
    syncInitService.registerForceLogoutCallback(() => {
      dispatch(logout());
      dispatch(clearTenantState());
      navigate(ROUTES.AUTH.LOGIN, { replace: true });
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 4. 更新边缘滑动返回服务配置
  useEffect(() => {
    if (initialized) {
      swipeBackService.updateConfig({
        currentPath: location.pathname,
        navigate,
        isAuthenticated: authState.isAuthenticated && !!authState.authToken
      });
    }
  }, [initialized, location.pathname, navigate, authState.isAuthenticated, authState.authToken]);

  if (!initialized) {
    // 根据当前路由和认证状态选择合适的骨架屏
    const currentPath = location.pathname;
    const isAuthenticated = authState.isAuthenticated;

    // 检测是否为聊天室路由
    const isChatRoom = currentPath.includes('/chat') ||
                      currentPath.includes('/customer-chat') ||
                      currentPath.includes('/system-chat') ||
                      currentPath.includes('/team-chat');

    if (isChatRoom && isAuthenticated) {
      // 根据路由确定聊天室类型
      let chatType: 'customer' | 'system' | 'team' | 'my' = 'customer';
      let showTasksBar = false;

      if (currentPath.includes('/customer-chat')) {
        chatType = 'customer';
        showTasksBar = true;
      } else if (currentPath.includes('/system-chat')) {
        chatType = 'system';
      } else if (currentPath.includes('/team-chat')) {
        chatType = 'team';
      } else if (currentPath.includes('/chat-room')) {
        chatType = 'my';
      }

      return (
        <ChatRoomSkeleton
          chatType={chatType}
          showTasksBar={showTasksBar}
        />
      );
    }

    // 默认显示Home骨架屏
    const showUserInfo = isAuthenticated;
    const isAdmin = authState.account?.type === 'owner' || false;
    const loadingStage = isAuthenticated ? 'authenticated' : 'initial';

    return (
      <HomeSkeleton
        showUserInfo={showUserInfo}
        isAdmin={isAdmin}
        loadingStage={loadingStage}
      />
    );
  }
  
  return (
    <ErrorBoundary>
      <div className="app">
        <AppRoutes />
      </div>
    </ErrorBoundary>
  );
}

export default App; 