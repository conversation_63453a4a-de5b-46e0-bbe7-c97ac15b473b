import { logService } from '../../system/logService';
import aileDBService from '../aileDBService';
import type { Contact } from '../initSql';
import { getRequiredTenantId } from '@/utils/tenantUtil';

/**
 * 聯絡人數據訪問對象
 * 提供客戶/聯絡人表相關的數據庫操作
 */
class ContactDao {
  /**
   * 根據ID獲取聯絡人詳情
   * @param id 聯絡人ID
   * @returns 聯絡人詳情
   */
  public async getContactById(id: string): Promise<Contact | null> {
    try {
      const tenantId = getRequiredTenantId();
      
      const contact = await aileDBService.get<Contact>(
        `SELECT * FROM Contact WHERE id = ? AND tenantId = ?`,
        [id, tenantId]
      );
      return contact;
    } catch (error) {
      logService.error('獲取聯絡人詳情失敗', { error: error as Error, id });
      return null;
    }
  }

  /**
   * 保存聯絡人信息到數據庫
   * @param contact 聯絡人信息
   * @returns 是否保存成功
   */
  public async saveContact(contact: Contact): Promise<boolean> {
    try {
      const tenantId = getRequiredTenantId();
      
      contact.tenantId = tenantId;
      const keys = Object.keys(contact);
      const placeholders = keys.map(() => '?').join(', ');
      const values = Object.values(contact).map(value => {
        // 處理陣列類型（languages）
        if (Array.isArray(value)) {
          return JSON.stringify(value);
        }
        // 處理布林值
        if (typeof value === 'boolean') {
          return value ? 1 : 0;
        }
        return value;
      });
      const sql = `INSERT OR REPLACE INTO Contact (${keys.join(', ')}) VALUES (${placeholders})`;
      await aileDBService.run(sql, values);
      return true;
    } catch (error) {
      logService.error('保存聯絡人信息失敗', { error: error as Error, contact });
      return false;
    }
  }

  /**
   * 批量保存聯絡人信息到數據庫
   * @param contacts 聯絡人信息列表
   * @returns 成功保存的聯絡人數量
   */
  public async saveContacts(contacts: Contact[]): Promise<number> {
    if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
      logService.warn('沒有聯絡人信息需要保存');
      return 0;
    }

    try {
      const tenantId = getRequiredTenantId();
      
      let successCount = 0;
      // 批量組合 SQL
      for (const contact of contacts) {
        if (!contact.id) {
          logService.warn('聯絡人ID為空，跳過保存', { contact });
          continue;
        }
        // 保證每筆資料都帶有正確的 tenantId
        contact.tenantId = tenantId;
        const keys = Object.keys(contact);
        const placeholders = keys.map(() => '?').join(', ');
        const values = Object.values(contact).map(value => {
          if (Array.isArray(value)) return JSON.stringify(value);
          if (typeof value === 'boolean') return value ? 1 : 0;
          return value;
        });
        const sql = `INSERT OR REPLACE INTO Contact (${keys.join(', ')}) VALUES (${placeholders})`;
        await aileDBService.run(sql, values);
        successCount++;
      }
      return successCount;
    } catch (error) {
      logService.error('批量保存聯絡人信息失敗', { error: error as Error });
      return 0;
    }
  }

  /**
   * 獲取聯絡人列表
   * @param params 查詢參數
   * @returns 聯絡人列表
   */
  public async getContactList(params: {
    tenantId?: string;
    serviceNumberId?: string;
    key?: string;
    pageIndex?: number;
    pageSize?: number;
  } = {}): Promise<Contact[]> {
    const {
      serviceNumberId,
      key,
      pageIndex = 0,
      pageSize = 20
    } = params;

    try {
      const tenantId = getRequiredTenantId();
      
      // 構建 SQL 查詢
      let sql = `SELECT * FROM Contact WHERE tenantId = ?`;
      const sqlParams: any[] = [tenantId];

      // 處理查詢條件
      if (serviceNumberId) {
        sql += ` AND serviceNumberId = ?`;
        sqlParams.push(serviceNumberId);
      }

      if (key) {
        sql += ` AND (name LIKE ? OR phone LIKE ? OR email LIKE ? OR company LIKE ?)`;
        const likeKey = `%${key}%`;
        sqlParams.push(likeKey, likeKey, likeKey, likeKey);
      }

      // 排序與分頁
      sql += ` ORDER BY updateTime DESC LIMIT ? OFFSET ?`;
      sqlParams.push(pageSize, pageIndex * pageSize);

      // 執行查詢
      const contacts = await aileDBService.all<Contact>(sql, sqlParams);
      return contacts;
    } catch (error) {
      logService.error('查詢聯絡人列表失敗', { error: error as Error, params });
      return [];
    }
  }

  /**
   * 刪除聯絡人
   * @param id 聯絡人ID
   * @returns 是否刪除成功
   */
  public async deleteContact(id: string): Promise<boolean> {
    try {
      await aileDBService.run(`DELETE FROM Contact WHERE id = ?`, [id]);
      return true;
    } catch (error) {
      logService.error('刪除聯絡人失敗', { error: error as Error, id });
      return false;
    }
  }
}

// 導出單例實例
export default new ContactDao(); 