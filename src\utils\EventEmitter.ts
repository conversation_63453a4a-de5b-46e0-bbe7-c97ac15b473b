import { Subject } from 'rxjs';

/**
 * EventEmitter implementation using RxJS Subject
 * Maintains the same API as the original EventEmitter for backwards compatibility
 * while leveraging RxJS Observable patterns internally
 */
export class EventEmitter {
  private subjects: { [eventName: string]: Subject<any> } = {};

  /**
   * Get or create a Subject for an event
   * @param event Event name
   * @returns Subject instance
   */
  private getSubject(event: string): Subject<any> {
    if (!this.subjects[event]) {
      this.subjects[event] = new Subject();
    }
    return this.subjects[event];
  }

  /**
   * Add event listener using RxJS subscribe
   * @param event Event name
   * @param listener Callback function
   */
  on(event: string, listener: (...args: any[]) => void): this {
    const subscription = this.getSubject(event).subscribe({
      next: (args: any[]) => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
    }
      }
    });
    
    // Store the subscription on the listener function for later cleanup
    (listener as any).__subscription = subscription;
    
    return this;
  }

  /**
   * Add one-time event listener
   * @param event Event name
   * @param listener Callback function
   */
  once(event: string, listener: (...args: any[]) => void): this {
    const onceWrapper = (...args: any[]) => {
      listener(...args);
      this.off(event, onceWrapper);
    };
    return this.on(event, onceWrapper);
  }

  /**
   * Remove event listener
   * @param event Event name
   * @param listener Callback function to remove
   */
  off(_event: string, _listener: (...args: any[]) => void): this {
    // Unsubscribe if there's a subscription
    const subscription = (_listener as any).__subscription;
    if (subscription) {
      subscription.unsubscribe();
      delete (_listener as any).__subscription;
    }
    
    return this;
  }

  /**
   * Remove all listeners for an event
   * @param event Event name (optional - if not provided, removes all listeners)
   */
  removeAllListeners(event?: string): this {
    if (event) {
      const subject = this.subjects[event];
      if (subject) {
        subject.complete();
        delete this.subjects[event];
      }
    } else {
      // Complete and remove all subjects
      Object.values(this.subjects).forEach(subject => subject.complete());
      this.subjects = {};
    }
    return this;
  }

  /**
   * Emit an event
   * @param event Event name
   * @param args Arguments to pass to listeners
   */
  emit(event: string, ...args: any[]): boolean {
    const subject = this.subjects[event];
    if (!subject) {
      return false;
    }
    
    subject.next(args);
    return true;
  }

  /**
   * Get all listeners for an event
   * Note: With RxJS this is not directly accessible, so we return an empty array
   * This is maintained for API compatibility only
   * @param event Event name
   */
  listeners(_event: string): Array<(...args: any[]) => void> {
    // Not directly supported with Subjects, returning empty array for compatibility
    return [];
  }
  
  /**
   * Get the Observable for an event (new RxJS-specific method)
   * @param event Event name
   * @returns Observable for the event
   */
  asObservable(event: string) {
    return this.getSubject(event).asObservable();
  }
}

export default EventEmitter; 