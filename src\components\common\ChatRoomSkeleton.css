/* 聊天室骨架屏样式 */
.chat-room-skeleton {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #E4F4FD;
}

/* Header 骨架样式 */
.chat-skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  background-color: #FFFFFF;
  padding: 0 16px;
  box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

.chat-skeleton-nav-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-skeleton-nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-back-button {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-nav-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Tasks Bar 骨架样式 */
.chat-skeleton-tasks-bar {
  display: flex;
  padding: 12px 16px;
  gap: 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-task-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.skeleton-task-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Chat Content 骨架样式 */
.chat-skeleton-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  background-color: #E4F4FD;
}

.chat-skeleton-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 12px;
}

.chat-skeleton-message.user-message {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.skeleton-message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  flex-shrink: 0;
}

.skeleton-message-content {
  flex: 1;
  max-width: 60%;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 用户消息特殊样式 - 更高优先级 */
.chat-room-skeleton .chat-skeleton-message.user-message .skeleton-message-content {
  width: 70% !important;
  max-width: 70% !important;
  background-color: #386591 !important;
}

.chat-skeleton-message.user-message .skeleton-message-content {
  width: 70% !important;
  max-width: 70% !important;
  background-color: #386591;
}

.chat-skeleton-message.user-message .skeleton-line {
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0.3) 75%);
  background-size: 200% 100%;
}

/* Input Area 骨架样式 */
.chat-skeleton-input-area {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #FFFFFF;
  border-top: 1px solid #f0f0f0;
  gap: 8px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}

.chat-skeleton-toolbar {
  display: flex;
  gap: 8px;
}

.skeleton-toolbar-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-input-field {
  flex: 1;
  height: 36px;
  border-radius: 18px;
  background-color: #f5f5f5;
  padding: 0 16px;
  display: flex;
  align-items: center;
}

.skeleton-line-input {
  width: 40%;
  height: 14px;
}

/* 通用骨架线条样式 */
.skeleton-line {
  height: 14px;
  border-radius: 7px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 6px;
}

.skeleton-line:last-child {
  margin-bottom: 0;
}

.skeleton-line-short {
  width: 40px;
}

.skeleton-line-medium {
  width: 100px;
}

.skeleton-line-long {
  width: 80%;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式调整 */
@media (max-width: 375px) {
  .chat-skeleton-header {
    padding: 0 12px;
  }
  
  .chat-skeleton-content {
    padding: 12px;
  }
  
  .chat-skeleton-input-area {
    padding: 8px 12px;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
  }
  
  .chat-skeleton-tasks-bar {
    padding: 8px 12px;
    gap: 12px;
  }
}
