import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Toast } from 'antd-mobile';
import { useTranslation } from 'react-i18next';
import QRCode from 'qrcode';
import { logService } from '@/services/system/logService.ts';
import deviceService from '@/services/platform/deviceService.ts';
import { Share } from '@capacitor/share';
import { Filesystem, Directory } from '@capacitor/filesystem';
import { Media } from '@capacitor-community/media';

import tenantService from '@/services/core/tenant/tenantService';
import authService from '@/services/core/auth/authService.ts';
import stateService from '@/services/stateService.ts';
import qr_download from '@/assets/icons/qrcode/downland.svg';
import qr_travel from '@/assets/icons/qrcode/travel.svg';
import qr_system from '@/assets/icons/qrcode/system-qrcode.svg';
import back_arrow from '@/assets/icons/qrcode/back-arrow.svg';
import { ConstantUtil } from '@/utils/constantUtil.ts';
import { CryptoUtil } from '@/utils/cryptoUtil.ts';
import snService, { IServiceNumberInfo } from '@/services/core/tenant/snService';
import { downloadAndCacheAvatarImage } from '@/utils/avatarImageUtil.ts';
import './AddMemberQRCodePage.css';

// 定义路由状态类型
interface LocationState {
  tenantId?: string;
  tenantName?: string;
  avatarId?: string;
}

const AddMemberQRCodePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  // const params = useParams<{ tenantId?: string }>();
  const location = useLocation();
  const locationState = location.state as LocationState;

  const [qrCodeDataURL, setQrCodeDataURL] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(true);
  const [, setInviteUrl] = useState<string>('');
  const [inviteCode, setInviteCode] = useState<string>('');
  const [bossInfo, setBossInfo] = useState<IServiceNumberInfo | null>(null);
  const [mergedQRCodeDataURL, setMergedQRCodeDataURL] = useState<string>('');

  // 獲取當前租戶信息 - 優先使用路由參數，然後是Redux狀態
  const tenantId = locationState.tenantId;
  const tenantName = locationState?.tenantName;

  useEffect(() => {
    logService.info('進入添加成員二維碼頁', {
      platform: deviceService.getPlatform(),
      tenantId,
      tenantName
    });

    // 先獲取Boss信息，然後生成邀請URL
    initializeQRCodeGeneration();
  }, [tenantId]);

  // 初始化二維碼生成流程
  const initializeQRCodeGeneration = async () => {
    try {
      // 第一步：獲取Boss信息
      const bossData = await generateBossInfo();

      // 第二步：使用Boss信息生成邀請URL
      if (bossData) {
        await generateInviteUrl(bossData);
      } else {
        logService.error('無法獲取Boss信息，無法生成邀請二維碼');
        setIsGenerating(false);
        Toast.show({
          content: '無法獲取服務號信息，請稍後再試',
          duration: 3000,
          position: 'bottom'
        });
      }
    } catch (error) {
      logService.error('初始化二維碼生成流程失敗', { error });
      setIsGenerating(false);
      Toast.show({
        content: '生成二維碼失敗，請稍後再試',
        duration: 3000,
        position: 'bottom'
      });
    }
  };

  // 当头像获取完成后，重新生成合并的二维码
  useEffect(() => {
    if (qrCodeDataURL && bossInfo) {
      const regenerateMergedQRCode = async () => {
        try {
          const mergedDataURL = await mergeQRCodeWithAvatar(qrCodeDataURL, bossInfo.avatarId);
          setMergedQRCodeDataURL(mergedDataURL);
          logService.info('頭像獲取完成後重新生成合併二維碼成功');
        } catch (error) {
          logService.warn('重新生成合併二維碼失敗，使用原二維碼', { error });
          setMergedQRCodeDataURL(qrCodeDataURL);
        }
      };
      regenerateMergedQRCode();
    }
  }, [bossInfo, qrCodeDataURL]);


  const generateBossInfo = async (): Promise<IServiceNumberInfo | null> => {
    try {
      logService.info('開始獲取Boss服務號信息', { tenantId });

      // 獲取Boss服務號信息
      const bossInfo = tenantId
        ? await snService.getBossServiceNumberInfo(tenantId)
        : await snService.getCurrentBossServiceNumber();

      if (bossInfo) {
        setBossInfo(bossInfo);
        logService.info('Boss服務號信息獲取成功', {
          serviceNumberId: bossInfo.id,
          name: bossInfo.name,
          avatarId: bossInfo.avatarId
        });
        return bossInfo;
      } else {
        logService.warn('獲取Boss服務號信息失敗，返回null');
        return null;
      }
    } catch (error) {
      logService.error('獲取Boss服務號信息時發生錯誤', { error });
      return null;
    }
  };

  /**
   * 合并二维码和头像图片
   * @param qrCodeDataURL 二维码的base64数据
   * @param avatarId 头像ID
   * @returns 合并后的图片base64数据
   */
  const mergeQRCodeWithAvatar = async (qrCodeDataURL: string, avatarId: string): Promise<string> => {
    return new Promise(async (resolve, reject) => {
      try {
        logService.info('開始合併二維碼和頭像', { avatarId });

        // 创建canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          throw new Error('無法創建Canvas上下文');
        }

        // 设置canvas尺寸 (与二维码相同)
        const qrSize = 300; // 二维码尺寸
        canvas.width = qrSize;
        canvas.height = qrSize;

        // 加载二维码图片
        const qrImage = new Image();
        qrImage.crossOrigin = 'anonymous';

        qrImage.onload = async () => {
          try {
            // 绘制二维码背景
            ctx.drawImage(qrImage, 0, 0, qrSize, qrSize);

            // 获取头像图片
            let avatarDataURL: string | null = null;
            if (avatarId) {
              try {
                avatarDataURL = await downloadAndCacheAvatarImage(avatarId, '55.28', 's');
              } catch (avatarError) {
                logService.warn('獲取頭像失敗，使用默認頭像', { error: avatarError });
              }
            }

            if (avatarDataURL) {
              // 加载头像图片
              const avatarImage = new Image();
              avatarImage.crossOrigin = 'anonymous';

              avatarImage.onload = () => {
                try {
                  // 计算头像位置 (居中)
                  const avatarSize = 55.28;
                  const avatarX = (qrSize - avatarSize) / 2;
                  const avatarY = (qrSize - avatarSize) / 2;

                  // 绘制头像 (保持正方形，无背景)
                  ctx.drawImage(avatarImage, avatarX, avatarY, avatarSize, avatarSize);

                  // 返回合并后的图片
                  const mergedDataURL = canvas.toDataURL('image/png', 1.0);
                  logService.info('二維碼和頭像合併成功');
                  resolve(mergedDataURL);
                } catch (error) {
                  logService.error('繪製頭像時發生錯誤', { error });
                  reject(error);
                }
              };

              avatarImage.onerror = () => {
                logService.warn('頭像圖片加載失敗，返回純二維碼');
                resolve(qrCodeDataURL);
              };

              avatarImage.src = avatarDataURL;
            } else {
              // 没有头像，返回原二维码
              logService.info('沒有頭像，返回原二維碼');
              resolve(qrCodeDataURL);
            }
          } catch (error) {
            logService.error('處理頭像時發生錯誤', { error });
            resolve(qrCodeDataURL); // 返回原二维码作为fallback
          }
        };

        qrImage.onerror = () => {
          logService.error('二維碼圖片加載失敗');
          reject(new Error('二維碼圖片加載失敗'));
        };

        qrImage.src = qrCodeDataURL;
      } catch (error) {
        logService.error('合併二維碼和頭像時發生錯誤', { error });
        reject(error);
      }
    });
  };

  const generateInviteUrl = async (bossData: IServiceNumberInfo) => {
    try {
      logService.info('開始生成邀請URL', {
        tenantId,
        serviceNumberId: bossData.id,
        bossName: bossData.name
      });

      if (!tenantId) {
        logService.error('無法獲取租戶ID，無法生成邀請鏈接', { tenantId });
        setIsGenerating(false);
        return;
      }

      // 使用傳入的Boss信息獲取服務號ID
      const serviceNumberId = bossData.id;

      if (!serviceNumberId) {
        logService.error('Boss信息中沒有服務號ID，無法創建邀請碼', { tenantId, bossData });
        setIsGenerating(false);
        Toast.show({
          content: '服務號信息不完整，請稍後再試',
          duration: 3000,
          position: 'bottom'
        });
        return;
      }

      // 調用API創建邀請碼
      const invitationResult = await tenantService.createInvitation({
        type: 'JoinServiceNumber',
        serviceNumberId: serviceNumberId,
        tenantId: tenantId
      });

      if (invitationResult.success && invitationResult.data) {
        const apiInviteCode = invitationResult.data.code;
        setInviteCode(apiInviteCode);

        // 生成邀請URL - 使用API返回的邀請碼
        // 获取用户信息
        const account = authService.getAccount();
        const loginUser = stateService.loginUser();
        // const loginTenant = stateService.loginTenant();

        if (!account || !loginUser) {
          logService.error('無法獲取用戶信息，無法生成二維碼', { tenantId });
          setIsGenerating(false);
          Toast.show({
            content: '無法獲取用戶信息，請重新登錄',
            duration: 3000,
            position: 'bottom'
          });
          return;
        }

        const content = {
          tenantId: tenantId,
          // accountId: account.accountId || '',
          // tenantName: tenantName || loginTenant?.name || '',
          // userId: loginUser.userId || '',
          // name: account.name || loginUser.name || ''
          inviteCode: apiInviteCode
        };

        const data = JSON.stringify(content); // 将 JS 对象转换为 JSON 字符串

        // 使用 CryptoUtil 进行加密
        const aesStr = CryptoUtil.encryptApiRequest(data,ConstantUtil.DEFAULT_SECRET_KEY);
        if (!aesStr) {
          logService.error('加密二維碼數據失敗', { content });
          setIsGenerating(false);
          Toast.show({
            content: '生成二維碼失敗，請稍後再試',
            duration: 3000,
            position: 'bottom'
          });
          return;
        }

        const url = `${ConstantUtil.QRCODE_SERVERPATH}${ConstantUtil.QRCODE_SEPERATOR}${aesStr}`;

        setInviteUrl(url);
        logService.info('生成邀請URL和邀請碼成功', {
          url,
          tenantId,
          inviteCode: apiInviteCode,
          serviceNumberId
        });

        // 生成二維碼
        generateQRCode(url);
      } else {
        logService.error('創建邀請碼失敗', {
          tenantId,
          serviceNumberId,
          result: invitationResult
        });
        setIsGenerating(false);
        Toast.show({
          content: invitationResult.msg || '創建邀請碼失敗，請稍後再試',
          duration: 3000,
          position: 'bottom'
        });
      }
    } catch (error) {
      logService.error('生成邀請URL失敗', { error });
      setIsGenerating(false);
      Toast.show({
        content: '生成邀請碼時發生錯誤，請稍後再試',
        duration: 3000,
        position: 'bottom'
      });
    }
  };



  const generateQRCode = async (url: string) => {
    try {
      setIsGenerating(true);
      const qrDataURL = await QRCode.toDataURL(url, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeDataURL(qrDataURL);
      logService.info('二維碼生成成功', { url });

      // 如果有头像，合并二维码和头像
      if (bossInfo) {
        try {
          const mergedDataURL = await mergeQRCodeWithAvatar(qrDataURL, bossInfo.avatarId);
          setMergedQRCodeDataURL(mergedDataURL);
          logService.info('二維碼和頭像合併成功');
        } catch (mergeError) {
          logService.warn('合併二維碼和頭像失敗，使用原二維碼', { error: mergeError });
          setMergedQRCodeDataURL(qrDataURL);
        }
      } else {
        // 没有头像，直接使用原二维码
        setMergedQRCodeDataURL(qrDataURL);
      }
    } catch (error) {
      logService.error('二維碼生成失敗', { error, url });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleBack = () => {
    logService.info('點擊返回', {});
    navigate(-1);
  };

  /**
   * 保存base64圖片到相冊的輔助函數
   */
  const saveBase64ImageToGallery = async (base64Data: string, filename: string): Promise<{ success: boolean; message: string; uri?: string }> => {
    const platform = deviceService.getPlatform();

    try {
      if (platform === 'android') {
        // 方法1: 優先使用 Media 插件保存到相冊（Android）
        try {
          // 首先嘗試獲取現有相冊
          let albumIdentifier: string | undefined;
          try {
            const albums = await Media.getAlbums();
            const aileAppAlbum = albums.albums.find(album => album.name === 'AileApp');
            albumIdentifier = aileAppAlbum?.identifier;
          } catch (albumError) {
            logService.warn('獲取相冊列表失敗', { error: albumError });
          }

          // 如果沒有找到相冊，嘗試創建
          if (!albumIdentifier) {
            try {
              await Media.createAlbum({ name: 'AileApp' });
              logService.info('成功創建 AileApp 相冊');

              // 重新獲取相冊 ID
              const albums = await Media.getAlbums();
              const aileAppAlbum = albums.albums.find(album => album.name === 'AileApp');
              albumIdentifier = aileAppAlbum?.identifier;
            } catch (createError) {
              logService.warn('創建相冊失敗', { error: createError });
            }
          }

          // 使用 Media 插件保存到相冊
          const saveOptions: any = {
            path: base64Data, // 直接使用完整的 data URL
            fileName: filename.replace('.png', '') // Media 插件會自動添加副檔名
          };

          // 如果有相冊 ID，則指定相冊
          if (albumIdentifier) {
            saveOptions.albumIdentifier = albumIdentifier;
          }

          const result = await Media.savePhoto(saveOptions);

          logService.info('使用 Media 插件保存成功', { result, albumIdentifier });
          return {
            success: true,
            message: '二維碼已保存到相冊',
            uri: result.filePath || result.identifier
          };
        } catch (mediaError) {
          logService.warn('Media 插件保存失敗，回退到 Filesystem API', { error: mediaError });

          // 方法2: 回退到 Filesystem API
          try {
            const cleanBase64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');

            // Android: 嘗試保存到Pictures目錄
            let result;
            try {
              result = await Filesystem.writeFile({
                path: `Pictures/AileApp/${filename}`,
                data: cleanBase64,
                directory: Directory.External,
                recursive: true
              });
              return {
                success: true,
                message: '二維碼已保存到Pictures/AileApp文件夾',
                uri: result.uri
              };
            } catch (externalError) {
              // 回退到Documents目錄
              result = await Filesystem.writeFile({
                path: `AileApp/${filename}`,
                data: cleanBase64,
                directory: Directory.Documents,
                recursive: true
              });
              return {
                success: true,
                message: '二維碼已保存到應用文檔文件夾',
                uri: result.uri
              };
            }
          } catch (fsError) {
            logService.warn('Filesystem API保存失敗', { error: fsError });
            return {
              success: false,
              message: '保存失敗，請稍後再試'
            };
          }
        }
      } else {
        return {
          success: false,
          message: '當前平台不支持保存到相冊'
        };
      }
    } catch (error) {
      logService.error('保存圖片到相冊失敗', { error });
      return {
        success: false,
        message: '保存失敗，請稍後再試'
      };
    }
  };

  const handleDownload = async () => {
    if (!mergedQRCodeDataURL) {
      logService.warn('二維碼未生成，無法下載');
      Toast.show({
        content: '二維碼未生成，請稍後再試',
        duration: 2000
      });
      return;
    }

    try {
      // 生成文件名，包含租戶信息和時間戳
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const filename = `invite-qrcode-${tenantId}-${timestamp}.png`;
      const platform = deviceService.getPlatform();

      // 移動端使用 Capacitor Filesystem API 保存到相冊
      // 暂时只在Android上启用，iOS由于网络问题暂时禁用
      if (platform === 'android') {
        try {
          // 檢查並請求權限（Android需要）
          const permissions = await Filesystem.checkPermissions();
          if (permissions.publicStorage !== 'granted') {
            const requestResult = await Filesystem.requestPermissions();
            if (requestResult.publicStorage !== 'granted') {
              logService.warn('用戶拒絕了存儲權限');
              Toast.show({
                content: '需要存儲權限才能保存到相冊',
                duration: 3000,
                position: 'bottom'
              });
              return;
            }
          }

          // 使用輔助函數保存圖片
          const saveResult = await saveBase64ImageToGallery(mergedQRCodeDataURL, filename);

          if (saveResult.success) {
            logService.info('二維碼保存成功', {
              filename,
              tenantId,
              platform,
              uri: saveResult.uri
            });

            Toast.show({
              content: saveResult.message,
              duration: 4000,
              position: 'bottom'
            });
            return;
          } else {
            logService.warn('保存失敗，回退到傳統方法', { message: saveResult.message });
            // 繼續執行傳統下載方法
          }
        } catch (fsError) {
          logService.warn('Filesystem API 保存失敗，回退到傳統方法', { error: fsError });
          // 繼續執行傳統下載方法
        }
      }

      // Web端或移動端回退方案：檢查是否支持新的文件系統API
      if ('showSaveFilePicker' in window) {
        try {
          // 使用新的文件系統API
          const fileHandle = await (window as any).showSaveFilePicker({
            suggestedName: filename,
            types: [{
              description: 'PNG images',
              accept: { 'image/png': ['.png'] }
            }]
          });

          const writable = await fileHandle.createWritable();
          const response = await fetch(mergedQRCodeDataURL);
          const blob = await response.blob();
          await writable.write(blob);
          await writable.close();

          logService.info('二維碼下載成功（文件系統API）', { filename, tenantId });
          Toast.show({
            content: '二維碼下載成功',
            duration: 3000,
            position: 'bottom'
          });
          return;
        } catch (fsError) {
          // 如果用戶取消或其他錯誤，回退到傳統方法
          logService.debug('文件系統API下載失敗，回退到傳統方法', { error: fsError });
        }
      }

      // 傳統下載方法
      const link = document.createElement('a');
      link.href = mergedQRCodeDataURL;
      link.download = filename;

      // 對於移動端Safari的兼容性處理
      if (platform === 'ios') {
        // iOS Safari需要用戶手動長按保存
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
      }

      // 觸發下載
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      logService.info('二維碼下載成功', { filename, tenantId, platform });

      // 根據平台顯示不同的提示
      if (platform === 'ios') {
        Toast.show({
          content: '請長按圖片保存到相冊',
          duration: 3000
        });
      } else {
        Toast.show({
          content: '二維碼下載成功',
          duration: 2000
        });
      }
    } catch (error) {
      logService.error('二維碼下載失敗', { error });
      Toast.show({
        content: '下載失敗，請稍後再試',
        duration: 2000
      });
    }
  };

  const handleShareInviteCode = async () => {
    if (!inviteCode) {
      logService.warn('邀請碼未生成，無法分享');
      Toast.show({
        content: '邀請碼未生成，請稍後再試',
        duration: 2000
      });
      return;
    }

    try {
      // 構建分享文案
      const shareText = `分享加入團隊邀請碼${inviteCode}，開啟Aile側邊欄選擇加入團隊，輸入上方邀請碼，按下「確定」加入團隊。`;

      // 檢查是否在移動端環境
      const platform = deviceService.getPlatform();

      if (platform === 'ios' || platform === 'android') {
        // 移動端：使用 Capacitor Share 插件
        try {
          await Share.share({
            title: '團隊邀請',
            text: shareText,
            dialogTitle: '分享邀請碼'
          });

          logService.info('邀請碼分享成功（原生）', { inviteCode, platform });
        } catch (shareError) {
          logService.warn('移動端分享邀請碼失敗', { shareError });

          // 檢查是否是用戶取消分享
          const errorMessage = (shareError as Error)?.message?.toLowerCase() || shareError?.toString()?.toLowerCase() || '';
          const isUserCancelled = errorMessage.includes('cancelled') ||
                                 errorMessage.includes('dismissed') ||
                                 errorMessage.includes('cancel') ||
                                 errorMessage.includes('abort');

          if (isUserCancelled) {
            logService.info('用戶取消了移動端邀請碼分享操作');
            return;
          }

          // 移動端分享失敗，嘗試複製到剪貼板作為回退
          throw shareError;
        }
      } else {
        // Web 環境：使用 Web Share API 或回退到剪貼板
        if (navigator.share) {
          try {
            await navigator.share({
              title: '團隊邀請',
              text: shareText
            });
            logService.info('邀請碼分享成功（Web Share API）', { inviteCode });
          } catch (webShareError) {
            logService.warn('Web Share API 分享邀請碼失敗', { webShareError });

            // 檢查是否是用戶取消分享
            const webErrorMessage = (webShareError as Error)?.message?.toLowerCase() || webShareError?.toString()?.toLowerCase() || '';
            const isWebUserCancelled = webErrorMessage.includes('cancelled') ||
                                      webErrorMessage.includes('dismissed') ||
                                      webErrorMessage.includes('cancel') ||
                                      webErrorMessage.includes('abort') ||
                                      webErrorMessage.includes('user') ||
                                      (webShareError as Error)?.name === 'AbortError';

            if (isWebUserCancelled) {
              logService.info('用戶取消了Web邀請碼分享操作');
              return;
            }

            // Web分享失敗，嘗試複製到剪貼板作為回退
            throw webShareError;
          }
        } else {
          // 瀏覽器不支持 Web Share API，直接複製到剪貼板
          await navigator.clipboard.writeText(shareText);
          Toast.show({
            content: '邀請碼已複製到剪貼板',
            duration: 2000
          });
          logService.info('邀請碼複製到剪貼板成功', { inviteCode });
        }
      }
    } catch (error) {
      logService.error('分享邀請碼失敗', { error, inviteCode });

      // 最終檢查是否是用戶取消操作
      const finalErrorMessage = (error as Error)?.message?.toLowerCase() || error?.toString()?.toLowerCase() || '';
      const isFinalUserCancelled = finalErrorMessage.includes('cancelled') ||
                                  finalErrorMessage.includes('dismissed') ||
                                  finalErrorMessage.includes('cancel') ||
                                  finalErrorMessage.includes('abort') ||
                                  (error as Error)?.name === 'AbortError';

      if (isFinalUserCancelled) {
        logService.info('用戶取消了邀請碼分享操作（最終檢查）');
        return;
      }

      // 只有在真正的技術錯誤時才嘗試複製到剪貼板
      try {
        const shareText = `分享加入團隊邀請碼${inviteCode}，開啟Aile側邊欄選擇加入團隊，輸入上方邀請碼，按下「確定」加入團隊。`;
        await navigator.clipboard.writeText(shareText);
        Toast.show({
          content: '分享失敗，已複製到剪貼板',
          duration: 2000
        });
        logService.info('邀請碼複製到剪貼板成功（回退方案）', { inviteCode });
      } catch (clipboardError) {
        logService.error('複製到剪貼板也失敗', { clipboardError });
        Toast.show({
          content: '分享失敗，請稍後再試',
          duration: 2000
        });
      }
    }
  };



  const handleShareQRCode = async () => {
    if (!mergedQRCodeDataURL) {
      logService.warn('二維碼未生成，無法分享');
      Toast.show({
        content: '二維碼未生成，請稍後再試',
        duration: 2000
      });
      return;
    }

    try {
      const platform = deviceService.getPlatform();
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const filename = `invite-qrcode-${tenantId}-${timestamp}.png`;

      if (platform === 'ios' || platform === 'android') {
        // 移動端：先保存圖片到臨時文件，然後分享文件
        try {
          // 清理 base64 數據
          const cleanBase64 = mergedQRCodeDataURL.replace(/^data:image\/[a-z]+;base64,/, '');

          // 保存到臨時文件
          const result = await Filesystem.writeFile({
            path: filename,
            data: cleanBase64,
            directory: Directory.Cache, // 使用緩存目錄
            recursive: true
          });

          logService.info('二維碼臨時文件保存成功', { uri: result.uri });

          // 分享文件
          await Share.share({
            title: '團隊邀請二維碼',
            text: '掃描此二維碼加入團隊',
            url: result.uri, // 使用文件 URI
            dialogTitle: '分享二維碼'
          });

          logService.info('二維碼圖片分享成功（原生）', { platform, uri: result.uri });

          // 分享完成後清理臨時文件
          try {
            await Filesystem.deleteFile({
              path: filename,
              directory: Directory.Cache
            });
            logService.info('臨時文件清理成功', { filename });
          } catch (cleanupError) {
            logService.warn('臨時文件清理失敗', { error: cleanupError });
          }

        } catch (shareError) {
          logService.warn('分享圖片文件失敗', { shareError });

          // 檢查是否是用戶取消分享（通常錯誤信息會包含 "cancelled" 或 "dismissed"）
          const errorMessage = (shareError as Error)?.message?.toLowerCase() || shareError?.toString()?.toLowerCase() || '';
          const isUserCancelled = errorMessage.includes('cancelled') ||
                                 errorMessage.includes('dismissed') ||
                                 errorMessage.includes('cancel') ||
                                 errorMessage.includes('abort');

          if (isUserCancelled) {
            logService.info('用戶取消了分享操作');
            // 用戶取消分享，不需要嘗試其他方法，直接返回
            return;
          }

          // 只有在真正的技術錯誤時才嘗試其他方法
          try {
            await Share.share({
              title: '團隊邀請二維碼',
              text: '掃描此二維碼加入團隊',
              url: mergedQRCodeDataURL, // 直接使用 data URL
              dialogTitle: '分享二維碼'
            });

            logService.info('二維碼 data URL 分享成功（原生）', { platform });
          } catch (dataUrlError) {
            logService.warn('data URL 分享也失敗', { dataUrlError });

            // 再次檢查是否是用戶取消
            const dataUrlErrorMessage = (dataUrlError as Error)?.message?.toLowerCase() || dataUrlError?.toString()?.toLowerCase() || '';
            const isDataUrlUserCancelled = dataUrlErrorMessage.includes('cancelled') ||
                                          dataUrlErrorMessage.includes('dismissed') ||
                                          dataUrlErrorMessage.includes('cancel') ||
                                          dataUrlErrorMessage.includes('abort');

            if (isDataUrlUserCancelled) {
              logService.info('用戶取消了 data URL 分享操作');
              return;
            }

            // 最後回退到文本分享
            await Share.share({
              title: '團隊邀請二維碼',
              text: `掃描此二維碼加入團隊。邀請碼：${inviteCode || '請查看二維碼'}`,
              dialogTitle: '分享邀請信息'
            });

            logService.info('已分享邀請信息（文本回退）');
          }
        }
      } else {
        // Web 環境：使用 Web Share API 分享圖片文件
        if (navigator.share && navigator.canShare) {
          try {
            // 將 base64 轉換為 Blob 和 File
            const response = await fetch(mergedQRCodeDataURL);
            const blob = await response.blob();
            const file = new File([blob], filename, { type: 'image/png' });

            // 檢查是否支持文件分享
            if (navigator.canShare({ files: [file] })) {
              await navigator.share({
                title: '團隊邀請二維碼',
                text: '掃描此二維碼加入團隊',
                files: [file]
              });
              logService.info('二維碼圖片分享成功（Web Share API）');
            } else {
              // 不支持文件分享，回退到下載
              throw new Error('瀏覽器不支持文件分享');
            }
          } catch (webShareError) {
            logService.warn('Web Share API 分享失敗', { webShareError });

            // 檢查是否是用戶取消分享
            const webErrorMessage = (webShareError as Error)?.message?.toLowerCase() || webShareError?.toString()?.toLowerCase() || '';
            const isWebUserCancelled = webErrorMessage.includes('cancelled') ||
                                      webErrorMessage.includes('dismissed') ||
                                      webErrorMessage.includes('cancel') ||
                                      webErrorMessage.includes('abort') ||
                                      webErrorMessage.includes('user') ||
                                      (webShareError as Error)?.name === 'AbortError';

            if (isWebUserCancelled) {
              logService.info('用戶取消了Web分享操作');
              return;
            }

            // 只有在真正的技術錯誤時才回退到下載
            const link = document.createElement('a');
            link.href = mergedQRCodeDataURL;
            link.download = filename;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            Toast.show({
              content: '瀏覽器不支持分享，已下載二維碼圖片',
              duration: 3000
            });
            logService.info('二維碼下載成功（Web回退方案）');
          }
        } else {
          // 瀏覽器不支持 Web Share API，直接下載
          const link = document.createElement('a');
          link.href = mergedQRCodeDataURL;
          link.download = filename;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          Toast.show({
            content: '瀏覽器不支持分享，已下載二維碼圖片',
            duration: 3000
          });
          logService.info('二維碼下載成功（Web不支持分享）');
        }
      }
    } catch (error) {
      logService.error('分享二維碼失敗', { error });

      // 檢查是否是用戶取消操作
      const finalErrorMessage = (error as Error)?.message?.toLowerCase() || error?.toString()?.toLowerCase() || '';
      const isFinalUserCancelled = finalErrorMessage.includes('cancelled') ||
                                  finalErrorMessage.includes('dismissed') ||
                                  finalErrorMessage.includes('cancel') ||
                                  finalErrorMessage.includes('abort') ||
                                  (error as Error)?.name === 'AbortError';

      if (isFinalUserCancelled) {
        logService.info('用戶取消了分享操作（最終檢查）');
        return;
      }

      // 最終回退方案：下載圖片
      try {
        const link = document.createElement('a');
        link.href = mergedQRCodeDataURL;
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        link.download = `invite-qrcode-${tenantId}-${timestamp}.png`;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Toast.show({
          content: '分享失敗，已下載二維碼圖片',
          duration: 3000
        });
        logService.info('二維碼下載成功（最終回退方案）');
      } catch (downloadError) {
        logService.error('下載也失敗', { downloadError });
        Toast.show({
          content: '分享和下載都失敗，請稍後再試',
          duration: 3000
        });
      }
    }
  };

  return (
    <div className="add-member-qrcode-page">
      {/* NavBar */}
      <div className="amq-navbar">
        <button className="amq-back-btn" onClick={handleBack} data-testid="back-btn">
          <img src={back_arrow} alt="back" />
        </button>
        <div className="amq-title">{t('invite_join', '邀請加入')}</div>
      </div>
      {/* Main Content */}
      <div className="amq-main">
        <div className="amq-user-block">
          <div className="amq-username">{ bossInfo?.name || t('doctor_wang', '王小明醫師')}</div>
          {/* {inviteCode && (
            <div className="amq-invite-code" onClick={handleCopyInviteCode}>
              <div className="amq-invite-code-label">邀請碼（點擊複製）</div>
              <div className="amq-invite-code-value">{inviteCode}</div>
            </div>
          )} */}
          <div className="amq-qrcode-block">
            {isGenerating ? (
              <div className="amq-qrcode-loading">
                <div className="amq-loading-spinner"></div>
                <div className="amq-loading-text">生成二維碼中...</div>
              </div>
            ) : mergedQRCodeDataURL ? (
              <div className="amq-qrcode-container">
                <img className="amq-qrcode-img" src={mergedQRCodeDataURL} alt="qrcode with avatar" />
              </div>
            ) : (
              <div className="amq-qrcode-error">
                <div className="amq-error-text">二維碼生成失敗</div>
                <button className="amq-retry-btn" onClick={() => initializeQRCodeGeneration()}>重試</button>
              </div>
            )}
          </div>
        </div>
        <div className="amq-actions">
          <div
            className={`amq-action-item ${!mergedQRCodeDataURL || isGenerating ? 'disabled' : ''}`}
            onClick={mergedQRCodeDataURL && !isGenerating ? handleDownload : undefined}
          >
            <div className="amq-action-icon">
              <img src={qr_download} alt="download" />
            </div>
            <div className="amq-action-label">{t('download', '下載')}</div>
          </div>
          <div
            className={`amq-action-item ${!inviteCode || isGenerating ? 'disabled' : ''}`}
            onClick={inviteCode && !isGenerating ? handleShareInviteCode : undefined}
          >
            <div className="amq-action-icon">
              <img src={qr_travel} alt="travel" />
            </div>
            <div className="amq-action-label">{t('send_invite_code', '傳送邀請碼')}</div>
          </div>
          <div
            className={`amq-action-item ${!mergedQRCodeDataURL || isGenerating ? 'disabled' : ''}`}
            onClick={mergedQRCodeDataURL && !isGenerating ? handleShareQRCode : undefined}
          >
            <div className="amq-action-icon">
              <img src={qr_system} alt="system-qrcode" />
            </div>
            <div className="amq-action-label">{t('share_qrcode', '分享QR Code')}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddMemberQRCodePage; 