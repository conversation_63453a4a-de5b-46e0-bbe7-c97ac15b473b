/* 添加成員二維碼頁面專屬樣式 */
.add-member-qrcode-page {
  width: 100vw;
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.amq-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 45px;
  padding: 0 12px;
  background: #fff;
  position: relative;
  z-index: 10;
}
.amq-back-btn {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  padding: 0;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.amq-back-btn img {
  width: 24px;
  height: 24px;
}
.amq-title {
  flex: 1;
  text-align: center;
  font-family: 'SF Pro', 'PingFang TC', sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: #333;
  line-height: 45px;
}
.amq-navbar-btn {
  margin-left: auto;
  font-size: 14px;
  border-radius: 4px;
  height: 32px;
  padding: 0 12px;
}

.amq-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 41px 36px 0 36px;
  gap: 36px;
}
.amq-user-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}
.amq-username {
  font-family: 'SF Pro', 'PingFang TC', sans-serif;
  font-size: 24px;
  font-weight: 400;
  color: #000;
  text-align: center;
}

.amq-invite-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
}

.amq-invite-code:hover {
  background: #e9ecef;
  transform: scale(1.02);
}

.amq-invite-code:active {
  transform: scale(0.98);
}

.amq-invite-code-label {
  font-family: 'SF Pro', 'PingFang TC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  text-align: center;
}

.amq-invite-code-value {
  font-family: 'SF Pro', 'PingFang TC', sans-serif;
  font-size: 32px;
  font-weight: 600;
  color: #1677FF;
  text-align: center;
  letter-spacing: 4px;
  user-select: all;
  cursor: pointer;
}
.amq-qrcode-block {
  width: 303px;
  height: 296px;
  background: #fff;
  border-radius: 16px;
  /* box-shadow: 0px 12px 30px 0px rgba(0,0,0,0.45); */
  box-shadow: 0px 12px 30px 0px #00000073;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.amq-qrcode-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.amq-qrcode-img {
  width: 232px;
  height: 232px;
  z-index: 1;
}
.amq-avatar {
  width: 55.28px;
  height: 55.28px;
  border-radius: 4px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  object-fit: cover;
}

/* 加載狀態樣式 */
.amq-qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.amq-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1677FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.amq-loading-text {
  font-family: 'SF Pro', 'PingFang TC', sans-serif;
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* 錯誤狀態樣式 */
.amq-qrcode-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.amq-error-text {
  font-family: 'SF Pro', 'PingFang TC', sans-serif;
  font-size: 14px;
  color: #ff4d4f;
  text-align: center;
}

.amq-retry-btn {
  padding: 8px 16px;
  background: #1677FF;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.amq-retry-btn:hover {
  background: #1456CC;
}
.amq-actions {
  display: flex;
  flex-direction: row;
  gap: 39px;
  justify-content: space-between;
  position: fixed;
  bottom: 41px;
}
.amq-action-item {
  width: 75px;
  height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  background: none;
  border: none;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.amq-action-item:hover {
  opacity: 0.7;
}

.amq-action-item:active {
  transform: scale(0.95);
}

.amq-action-item.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.amq-action-item.disabled:hover {
  opacity: 0.4;
  transform: none;
}
.amq-action-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.amq-action-icon img {
  width: 20px;
  height: 20px;
}
.amq-action-label {
  font-family: 'SF Pro', 'PingFang TC', sans-serif;
  font-size: 12px;
  color: #999;
  text-align: center;
  margin-top: 2px;
} 