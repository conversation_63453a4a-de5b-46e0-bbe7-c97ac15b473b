/* UserSignUpPage 页面专属样式 */
.register-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: white;
  position: relative;
}

.register-page__navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 48px 16px 20px;
  gap: 8px;
}

.register-page__back-button {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.register-page__back-icon {
  width: 24px;
  height: 24px;
}

.register-page__title-container {
  flex: 1;
}

.register-page__title {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 600;
  font-size: 20px;
  line-height: 1.4em;
  color: #333333;
}

.register-page__spacer {
  width: 24px;
}

.register-page__form {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  padding-top: 32px;
  flex: 1;
  --border-top: none !important;
}

.register-page__avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  padding: 12px 0;
}

.register-page__avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  box-sizing: border-box;
}

.register-page__avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.register-page__avatar-text {
  font-size: 40px;
  color: white;
  font-family: 'PingFang TC', sans-serif;
  line-height: 1;
}

.register-page__avatar-placeholder {
  width: 40px;
  height: 40px;
}

.register-page__avatar-input {
  display: none;
}

.register-page__error-text {
  text-align: center;
  font-size: 12px;
  color: #FF3141;
  margin-bottom: 16px;
}

.register-page__form-label {
  font-size: 15px;
  color: #666666;
  padding: 12px 0 4px 0;
}

.register-page__input-container {
  border-bottom-width: 1px;
  padding-bottom: 12px;
}

.register-page__input-container--error {
  border-color: #FF3141;
}

.register-page__input-container--normal {
  border-color: #EEEEEE;
}

.register-page__input {
  width: 100%;
  outline: none;
  font-size: 16px;
}

/* 底部按钮容器 */
.register-page__button-container {
  padding: 16px 16px 48px;
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #FFFFFF;
}

.register-page__continue-button {
  font-size: 18px;
  border-radius: 4px;
  height: 49px;
} 

.register-page__form-group{
  padding-left: unset !important;
}