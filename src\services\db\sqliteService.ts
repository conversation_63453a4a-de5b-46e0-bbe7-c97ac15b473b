import { Capacitor } from "@capacitor/core";
import { CapacitorSQLite, SQLiteConnection } from "@capacitor-community/sqlite";
import { logService } from "../system/logService";

/**
 * SQLite 服务接口
 */
export interface ISQLiteService {
  getPlatform(): string;
  initWebStore(): Promise<void>;
  addUpgradeStatement(options: any): Promise<void>;
  openDatabase(dbName: string, loadToVersion: number, readOnly: boolean): Promise<any>;
  closeDatabase(dbName: string, readOnly: boolean): Promise<void>;
  saveToStore(dbName: string): Promise<void>;
  saveToLocalDisk(dbName: string): Promise<void>;
  isConnection(dbName: string, readOnly: boolean): Promise<boolean>;
}

/**
 * SQLite 数据库服务
 * 提供底层数据库操作功能
 */
class SQLiteService implements ISQLiteService {
  private platform = Capacitor.getPlatform();
  private sqlitePlugin = CapacitorSQLite;
  private sqliteConnection = new SQLiteConnection(CapacitorSQLite);
  private dbNameVersionDict: Map<string, number> = new Map();

  /**
   * 获取当前平台
   */
  getPlatform(): string {
    return this.platform;
  }

  /**
   * 初始化 Web 存储
   */
  async initWebStore(): Promise<void> {
    try {
      await this.sqliteConnection.initWebStore();
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      logService.error("SQLite 初始化 Web 存储失败", { error: msg });
      throw new Error(`sqliteService.initWebStore: ${msg}`);
    }
  }

  /**
   * 添加数据库升级语句
   * @param options 升级选项
   */
  async addUpgradeStatement(options: any): Promise<void> {
    try {
      await this.sqlitePlugin.addUpgradeStatement(options);
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      logService.error("SQLite 添加升级语句失败", { error: msg });
      throw new Error(`sqliteService.addUpgradeStatement: ${msg}`);
    }
  }

  /**
   * 打开数据库连接
   * @param dbName 数据库名称
   * @param loadToVersion 加载的版本号
   * @param readOnly 是否只读
   * @returns 数据库连接
   */
  async openDatabase(
    dbName: string,
    loadToVersion: number,
    readOnly: boolean
  ): Promise<any> {
    this.dbNameVersionDict.set(dbName, loadToVersion);
    let encrypted = false;
    const mode = encrypted ? "secret" : "no-encryption";
    
    try {
      let db: any;
      const retCC = (await this.sqliteConnection.checkConnectionsConsistency()).result;
      let isConn = (await this.sqliteConnection.isConnection(dbName, readOnly)).result;
      
      if (retCC && isConn) {
        db = await this.sqliteConnection.retrieveConnection(dbName, readOnly);
      } else {
        db = await this.sqliteConnection.createConnection(
          dbName,
          encrypted,
          mode,
          loadToVersion,
          readOnly
        );
      }
      
      await db.open();
      const res = await db.isDBOpen();
      
      if (!res.result) {
        throw new Error(`数据库 ${dbName} 打开失败`);
      }
      
      return db;
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      logService.error("SQLite 打开数据库失败", { error: msg, dbName });
      throw new Error(`sqliteService.openDatabase: ${msg}`);
    }
  }

  /**
   * 检查是否存在数据库连接
   * @param dbName 数据库名称
   * @param readOnly 是否只读
   * @returns 是否存在连接
   */
  async isConnection(dbName: string, readOnly: boolean): Promise<boolean> {
    try {
      const isConn = (await this.sqliteConnection.isConnection(dbName, readOnly)).result;
      if (isConn !== undefined) {
        return isConn;
      } else {
        throw new Error(`sqliteService.isConnection undefined`);
      }
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      logService.error("SQLite 检查连接失败", { error: msg, dbName });
      throw new Error(`sqliteService.isConnection: ${msg}`);
    }
  }

  /**
   * 关闭数据库连接
   * @param dbName 数据库名称
   * @param readOnly 是否只读
   */
  async closeDatabase(dbName: string, readOnly: boolean): Promise<void> {
    try {
      const isConn = (await this.sqliteConnection.isConnection(dbName, readOnly)).result;
      if (isConn) {
        await this.sqliteConnection.closeConnection(dbName, readOnly);
      }
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      logService.error("SQLite 关闭数据库失败", { error: msg, dbName });
      throw new Error(`sqliteService.closeDatabase: ${msg}`);
    }
  }

  /**
   * 保存数据库到存储
   * @param dbName 数据库名称
   */
  async saveToStore(dbName: string): Promise<void> {
    try {
      await this.sqliteConnection.saveToStore(dbName);
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      logService.error("SQLite 保存到存储失败", { error: msg, dbName });
      throw new Error(`sqliteService.saveToStore: ${msg}`);
    }
  }

  /**
   * 保存数据库到本地磁盘
   * @param dbName 数据库名称
   */
  async saveToLocalDisk(dbName: string): Promise<void> {
    try {
      await this.sqliteConnection.saveToLocalDisk(dbName);
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      logService.error("SQLite 保存到本地磁盘失败", { error: msg, dbName });
      throw new Error(`sqliteService.saveToLocalDisk: ${msg}`);
    }
  }
}

export default new SQLiteService();

