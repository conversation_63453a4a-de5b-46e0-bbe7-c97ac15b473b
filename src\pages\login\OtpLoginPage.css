.phone-login-page {
  background-color: #FFFFFF;
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 顶部导航样式 */
.phone-login-page__navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 48px 16px 20px;
  gap: 8px;
}

.phone-login-page__back-button {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.phone-login-page__back-icon {
  width: 24px;
  height: 24px;
}

.phone-login-page__title-container {
  flex: 1;
}

.phone-login-page__title {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 600;
  font-size: 20px;
  line-height: 1.4em;
  color: #333333;
}

.phone-login-page__spacer {
  width: 24px;
}

/* 表单区域样式 */
.phone-login-page__form {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  gap: 20px;
  flex: 1;
  --border-top: none !important;
  --border-bottom: none !important;
}

/* 覆盖 Ant Mobile Form 样式 */
.phone-login-page__form .adm-form-item {
  margin-bottom: 0;
  padding-left: 0 !important
}

/* 错误提示样式 */
.phone-login-page__form .adm-form-item-feedback-error {
  font-family: 'SF Pro', sans-serif;
  font-size: 13px;
  color: #FF3141;
  margin-top: 4px;
}

.phone-login-page__form-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.phone-login-page__form-group .adm-list-item-content-main {
  padding: unset;
  padding-bottom: 12px;
  border-bottom: 1px solid #EEEEEE;
}

.adm-list-item-content{
  border-top: unset !important;
}
.phone-login-page__form-label {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4em;
  color: #666666;
  padding: 12px 0 4px;
}

/* 手机号输入区域 */
.phone-login-page__phone-input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 错误状态边框 */
.adm-form-item-has-error .phone-login-page__phone-input-container {
  border-bottom-color: #FF3141;
}

.phone-login-page__country-code-container {
  position: relative;
  z-index: 9999;
}

.phone-login-page__country-code {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.phone-login-page__country-code-text {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 17px;
  line-height: 1.4em;
  color: #333333;
}

.phone-login-page__down-icon {
  width: 16px;
  height: 16px;
}

.phone-login-page__divider {
  width: 1px;
  height: 26px;
  background-color: #EEEEEE;
  margin: 0 8px;
}

.phone-login-page__phone-input {
  flex: 1;
  border: none;
  outline: none;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 17px;
  line-height: 1.4em;
  color: #333333;
  width: 100%;
  background-color: transparent;
  padding: 0;
}

.phone-login-page__phone-input::placeholder {
  color: #CCCCCC;
}

/* 覆盖 Ant Mobile Input 样式 */
.phone-login-page__phone-input .adm-input {
  --font-size: 17px;
  --color: #333333;
  --placeholder-color: #CCCCCC;
  padding: 0;
}

/* 验证码输入区域 */
.phone-login-page__verification-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  border-bottom: none;
  padding: 0;
}

.phone-login-page__verification-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  padding: 12px 0 0;
}

/* 错误状态边框 */
.adm-form-item-has-error .phone-login-page__verification-row {
  border-bottom-color: #FF3141;
}

.phone-login-page__form-label-inline {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.4em;
  color: #666666;
  margin-right: 71px;
  white-space: nowrap;
}

.phone-login-page__verification-input {
  flex: 1;
  border: none;
  outline: none;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 17px;
  line-height: 1.4em;
  color: #333333;
  padding: 0;
  width: 100%;
  background-color: transparent;
}

.phone-login-page__verification-input::placeholder {
  color: #CCCCCC;
}

/* 添加验证码输入框禁用状态的样式 */
.phone-login-page__verification-input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.phone-login-page__verification-input:disabled::placeholder {
  color: #bbbbbb;
}

/* 覆盖 Ant Mobile Input 样式 */
.phone-login-page__verification-input .adm-input {
  --font-size: 17px;
  --color: #333333;
  --placeholder-color: #CCCCCC;
  padding: 0;
}

.phone-login-page__send-button {
  border: none;
  background: transparent;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 13px;
  line-height: 1.4em;
  color: #1677FF;
  padding: 4px 12px;
  cursor: pointer;
}

.phone-login-page__send-button--disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

/* 错误提示 */
.phone-login-page__error-text {
  font-family: 'SF Pro', sans-serif;
  font-size: 13px;
  color: #FF3141;
  margin-top: 4px;
}

/* 错误状态样式 */
.phone-login-page__phone-input-container--error {
  border-bottom-color: #FF3141 !important;
}

.phone-login-page__verification-container--error {
  border-bottom-color: #FF3141 !important;
}

/* 底部按钮容器 */
.phone-login-page__button-container {
  padding: 16px 16px 48px;
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #FFFFFF;
}

/* 继续按钮 */
.phone-login-page__continue-button {
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 18px;
  line-height: 1.4em;
  border-radius: 4px;
  height: 49px;
}

/* 覆盖 Ant Mobile Button 样式 */
.phone-login-page__continue-button.adm-button {
  --border-radius: 4px;
  --border-width: 0;
  --background-color: #1677FF;
  --text-color: #FFFFFF;
  --font-size: 18px;
}

/* 国家代码下拉菜单 */
.phone-login-page__country-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100px;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  margin-top: 4px;
}

.phone-login-page__country-option {
  padding: 12px;
  font-family: 'SF Pro', sans-serif;
  font-size: 17px;
  color: #333333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.phone-login-page__country-option:hover {
  background-color: #F5F5F5;
}

.phone-login-page__country-option:first-child {
  border-bottom: 1px solid #EEEEEE;
}

/* 弹窗样式 */
.phone-login-page__popup {
  padding: 16px;
}

.phone-login-page__popup-title {
  font-family: 'PingFang TC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
}

.phone-login-page__popup-list {
  display: flex;
  flex-direction: column;
}

.phone-login-page__popup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #EEEEEE;
  font-family: 'SF Pro', sans-serif;
  font-size: 17px;
  color: #333333;
  cursor: pointer;
}

.phone-login-page__popup-item--last {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-family: 'SF Pro', sans-serif;
  font-size: 17px;
  color: #333333;
  cursor: pointer;
} 