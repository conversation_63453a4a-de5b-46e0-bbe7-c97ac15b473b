{"name": "new_aile", "version": "5.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "e2e": "playwright test", "android": "npm run build && npx cap sync android && npx cap open android", "android:16kb": "node scripts/build-android-16kb.js", "android:verify": "node scripts/verify-16kb-support.js", "android:clean": "cd android && ./gradlew clean", "generate": "graphql-codegen --config codegen.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@ant-design/icons": "^6.0.0", "@apollo/client": "^3.13.9", "@capacitor-community/media": "^8.0.1", "@capacitor-community/safe-area": "^7.0.0-alpha.1", "@capacitor-community/sqlite": "^7.0.1", "@capacitor-mlkit/barcode-scanning": "^7.2.1", "@capacitor/android": "^7.4.2", "@capacitor/app": "^7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/core": "^7.4.0", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.2", "@capacitor/ios": "^7.4.2", "@capacitor/push-notifications": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@reduxjs/toolkit": "^2.0.1", "@sentry/react": "^7.99.0", "@stagewise/toolbar-react": "^0.4.9", "@types/pinyin": "^2.10.2", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "aile-capacitor-line-login": "^1.3.10", "antd-mobile": "^5.39.0", "axios": "^1.10.0", "barcode-detector": "^3.0.5", "crypto-js": "^4.2.0", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "i18next": "^25.3.0", "jeep-sqlite": "^2.8.0", "pinyin": "^4.0.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-redux": "^9.0.4", "react-router-dom": "^7.6.3", "rxjs": "^7.8.2", "socket.io-client": "^4.7.2", "sql.js": "^1.12.0", "typescript": "^5.8.3", "vite": "^7.0.0"}, "devDependencies": {"@capacitor/cli": "^7.4.0", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@playwright/test": "^1.42.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.6.1", "@types/crypto-js": "^4.2.2", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^24.0.10", "@types/redux-mock-store": "^1.5.0", "@types/sql.js": "^1.4.9", "autoprefixer": "^10.4.16", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.35", "redux-mock-store": "^1.5.5", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.0"}}