{"__schema": {"queryType": {"name": "Query", "kind": "OBJECT"}, "mutationType": {"name": "Mutation", "kind": "OBJECT"}, "subscriptionType": {"name": "Subscription", "kind": "OBJECT"}, "types": [{"kind": "OBJECT", "name": "AccountProfileData", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "email", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mobile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AccountProfileResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "AccountProfileData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AgentStartRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AgentStartResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "SessionData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "AgentStopRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AgentStopResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "AvatarBase64Response", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "BaseListMessageData", "description": null, "isOneOf": null, "fields": [{"name": "count", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasNextPage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "items", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "Message", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastSequence", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "BaseListRoomData", "description": null, "isOneOf": null, "fields": [{"name": "count", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasNextPage", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "items", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "RoomData", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "BaseListRoomMember", "description": null, "isOneOf": null, "fields": [{"name": "count", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasNextPage", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "items", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "RoomMember", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "BaseListTenantContact", "description": null, "isOneOf": null, "fields": [{"name": "count", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasNextPage", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "items", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TenantContact", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalCount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "BasePaginationInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "direction", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "SortDirection", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "key", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "orderBy", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageIndex", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageSize", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "BetweenMessageRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "maxSequence", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "minSequence", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageSize", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "BloodType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "A", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "AB", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "B", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "O", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "Channel", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Admin", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Ai", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "<PERSON><PERSON>", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "AileLite", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Aiwow", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Anonymous", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Cbm", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Facebook", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Google", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Instagram", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Internal", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Line", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Webchat", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "CheckLoginOtpResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreateInvitationRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "serviceNumberId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "InvitationType", "ofType": null}}, "defaultValue": "Jo<PERSON><PERSON><PERSON><PERSON>", "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "CreateInvitationResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "InvitationData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CreatePersonalTenantInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "file", "description": null, "type": {"kind": "SCALAR", "name": "Upload", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "phone", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "CreatePersonalTenantResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TenantInfo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DeviceCheckRequestInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "onceToken", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "EmployeeProfileResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TenantEmployee", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "FindMessageRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Float", "description": "The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "Gender", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Female", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Male", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Other", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Unknown", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "GetAvatarBase64Input", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "avatarId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "size", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "IdentityData", "description": null, "isOneOf": null, "fields": [{"name": "avatarId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "fbIdentityId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDefault", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "ServiceIdentityType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "IdentityListRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "serviceNumberId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "IdentityListResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "IdentityData", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "InvitationData", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "text", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "ENUM", "name": "InvitationType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "InvitationType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "JoinServiceNumber", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Jo<PERSON><PERSON><PERSON><PERSON>", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "SCALAR", "name": "JSON", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "JoinType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Guarant<PERSON>", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Invitation", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "Language", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "EN_US", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ZH_CN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ZH_TW", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "LoginData", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "accountType", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "currentEmployeeInfo", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TenantEmployee", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "currentTenantInfo", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TenantInfo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isInitial", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isMute", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastTenantId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "loginStatus", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "loginType", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mobile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "onlineId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "personRoomId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "systemAccountId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "systemRoomId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantRelations", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TenantRelation", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tokenId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "LoginRequestInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "accountId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "address", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "checkCode", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "countryCode", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "loginType", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mobile", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "onceToken", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "password", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "scopeId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "thirdChannel", "description": null, "type": {"kind": "ENUM", "name": "Channel", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "useNonMobile", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LoginResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "LoginData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "LoginWithOtpRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "checkCode", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "onceToken", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "LoginWithThirdRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "scopeId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "thirdChannel", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Channel", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "MaritalStatus", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Divorced", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Married", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Single", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Widowed", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "MemberItemRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "memberId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MemberItemResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "RoomMember", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "MemberListRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "direction", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "key", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "orderBy", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageIndex", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageSize", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MemberListResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "BaseListRoomMember", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Message", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "appoint<PERSON><PERSON><PERSON>", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Channel", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "channel", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Channel", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "channelMessageId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "content", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "excludeMemberIds", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "flag", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "nearMessageId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "osType", "description": null, "args": [], "type": {"kind": "ENUM", "name": "OsType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "recipientAccountId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "recipientId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sendTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "senderId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sender<PERSON>ame", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sequence", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sessionId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sourceType", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "SourceType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tag", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "themeId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "time", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "MessageType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MessageListResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "BaseListMessageData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "MessageRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "pageSize", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "sequence", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MessageResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Message", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "MessageType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Action", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "At", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Audio", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Event", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "File", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Image", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Json", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Location", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "<PERSON>er", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Template", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Text", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Video", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Voice", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Mutation", "description": null, "isOneOf": null, "fields": [{"name": "_empty", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "agentStart", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AgentStartRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AgentStartResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "agentStop", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "AgentStopRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AgentStopResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "checkLoginOtp", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "checkLoginOtpRequestInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "CheckLoginOtpResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createPersonalTenant", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreatePersonalTenantInput", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "CreatePersonalTenantResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deviceCheck", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "DeviceCheckRequestInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LoginResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "login", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "LoginRequestInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LoginResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "loginWithOtp", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "LoginWithOtpRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LoginResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "loginWithThird", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "LoginWithThirdRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LoginResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "requestLoginOtp", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "OtpInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "OtpResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "roomItem", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "RoomItemRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RoomItemResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sendMessage", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "SendMessageRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SendMessageResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "switchIdentity", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "SwitchIdentityRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SwitchIdentityResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "switchTenant", "description": null, "args": [{"name": "tenantId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SwitchTenantResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferCancel", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TransferCancelRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TransferResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferComplete", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TransferCompleteRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TransferResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferMake", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TransferMakeRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TransferResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferRobotMake", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TransferRobotMakeRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TransferResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferStart", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TransferStartRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TransferResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateAccountProfile", "description": null, "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "UpdateAccountProfileInput", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AccountProfileResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "OptionalPaginationInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "direction", "description": null, "type": {"kind": "ENUM", "name": "SortDirection", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "key", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "orderBy", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageIndex", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageSize", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "OsType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "android", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ios", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mac", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pc", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "web", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "windows", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "OtpData", "description": null, "isOneOf": null, "fields": [{"name": "onceToken", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "validSecond", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "OtpInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "countryCode", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mobile", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "OtpResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "OtpData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "Privilege", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Common", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Manager", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Owner", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Provisional", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Query", "description": null, "isOneOf": null, "fields": [{"name": "createInvitation", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CreateInvitationRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "CreateInvitationResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "employeeProfile", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "EmployeeProfileResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "endRoomList", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "RoomListRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RoomListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "findMessage", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "FindMessageRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MessageResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getAvatarBase64", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "GetAvatarBase64Input", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AvatarBase64Response", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getBetweenSquenceMessages", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "BetweenMessageRequest", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MessageListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getLessThanSquenceMessages", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "MessageRequest", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MessageListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getMoreThanSquenceMessages", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "MessageRequest", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MessageListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "identityList", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "IdentityListRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "IdentityListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "memberItem", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "MemberItemRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MemberItemResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "memberList", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "MemberListRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "MemberListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "robotRoomList", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "RoomListRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "RoomListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantContactDetail", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TenantContactDetailRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TenantContactDetailResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantContactList", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TenantContactListRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TenantContactListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantRelations", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TenantRelationsRequest", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TenantRelationsResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferList", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TransferListRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TransferListResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "verifyInvitationCode", "description": null, "args": [{"name": "request", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "VerifyInvitationCodeRequest", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "VerifyInvitationCodeResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "RoomData", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "businessDescription", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "businessEndTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "businessId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "businessName", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "businessStatus", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "homePagePicId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isCustomName", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isExternal", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastMessage", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastSequence", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mainRoomId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "members", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "RoomMember", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ownerId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "ENUM", "name": "RoomStatus", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "RoomItemRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "RoomItemResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "RoomData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "RoomListRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "pageIndex", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageSize", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "RoomListResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "BaseListRoomData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "RoomMember", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dfrTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "firstSequence", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastMessage", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Message", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastReadSequence", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastReceivedSequence", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mainRoomId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "memberId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mute", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "privilege", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Privilege", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "roomType", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "ENUM", "name": "RoomMemberStatus", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "top", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "topTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "ENUM", "name": "RoomMemberType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "userInfo", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "UserInfo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "RoomMemberStatus", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Block", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Danger", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Delete", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Disable", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Enable", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Forbid", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "RoomMemberType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Robot", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ServiceNumber", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "User", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "RoomStatus", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Block", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Danger", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Delete", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Disable", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Enable", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Forbid", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "SendMessageRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "content", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "JSON", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "tag", "description": null, "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "MessageType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SendMessageResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Message", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ServiceIdentityType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Assistant", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Boss", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Robot", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ServiceNumber", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "ServiceNumberSimpleData", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ownerId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "ServiceNumberType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Boss", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Manage", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Normal", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Official", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Professional", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "SessionData", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "activeTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "agentFirstMessageTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "agentId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "agentLastMessageTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "appoint<PERSON><PERSON><PERSON>", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "channel", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Channel", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "customerId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "customerLastMessageChannel", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Channel", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "customerLastMessageTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "customerName", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "distributeTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "endTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "gwSessionId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "idleTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOwnerStop", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ownerAutoStopTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ownerAutoTransferTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ownerId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ownerStopTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "robotEndTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "robotTimeoutTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "scopeId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberType", "description": null, "args": [], "type": {"kind": "ENUM", "name": "ServiceNumberType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "startTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "ENUM", "name": "SessionStatus", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeoutTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "warned", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "warningType", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "SessionStatus", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "AgentActive", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "AgentStop", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "CustomerStop", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DistributeActive", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "RobotActive", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "RobotStop", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Timeout", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "SortDirection", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "SourceType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Broadcast", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Consult", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "System", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "User", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "Status", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Audit", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Delete", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Disable", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Enable", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "New", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Subscription", "description": null, "isOneOf": null, "fields": [{"name": "message", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Message", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "SwitchIdentityRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "identityId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SwitchIdentityResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "IdentityData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SwitchTenantResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TenantInfo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TenantContact", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "age", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "alias", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "birthday", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "bloodType", "description": null, "args": [], "type": {"kind": "ENUM", "name": "BloodType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "businessCardId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "channel", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Channel", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "company", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "companyAddress", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "companyDepartment", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "companyDuty", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "companyEmail", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "companyPhone", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "email", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "gender", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Gender", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "interests", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isAileCompany", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isBindAile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isCollectInfo", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isJoinAile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "languages", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Language", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "maritalStatus", "description": null, "args": [], "type": {"kind": "ENUM", "name": "MaritalStatus", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "openId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "parentAddressBookId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "phone", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Status", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TenantContactDetailRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TenantContactDetailResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TenantContact", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TenantContactListRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "direction", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"DESC\"", "isDeprecated": false, "deprecationReason": null}, {"name": "key", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "orderBy", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageSize", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "serviceNumberId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TenantContactListResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "BaseListTenantContact", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TenantEmployee", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "age", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "birthday", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "channel", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Channel", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "gender", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Gender", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "homePagePicId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isBindAile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isCollectInfo", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isJoinAile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "joinType", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "JoinType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mood", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "openId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "personRoomId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "Status", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TenantInfo", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "address", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "certificateFailReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "certificateFileId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "certificateStatus", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "city", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "employeeInfo", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TenantEmployee", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "endTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "industry", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "industrySub", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "phone", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "representative<PERSON><PERSON><PERSON>", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "scale", "description": null, "args": [], "type": {"kind": "ENUM", "name": "TenantScale", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "shortName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "startTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "switchTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TenantType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "unifiedNumber", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "upgrade", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "website", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TenantRelation", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "bossServiceNumber", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "ServiceNumberSimpleData", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "industry", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "industrySub", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isLastTenant", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "joinTime", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Float", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "manageServiceNumber", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ServiceNumberSimpleData", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "manageServiceNumberId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "officialServiceNumber", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "ServiceNumberSimpleData", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "officialServiceNumberId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "openId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "relationId", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "scale", "description": null, "args": [], "type": {"kind": "ENUM", "name": "TenantScale", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "shortName", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TenantType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "unReadCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TenantRelationsRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "accountId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "channel", "description": null, "type": {"kind": "ENUM", "name": "Channel", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "direction", "description": null, "type": {"kind": "ENUM", "name": "SortDirection", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "key", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "orderBy", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageIndex", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pageSize", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "refreshTime", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TenantRelationsResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TenantRelation", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "TenantScale", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Enterprise", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Middle", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Small", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "TenantType", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "Common", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Official", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Person", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Public", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "Service", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TransferCancelRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TransferCompleteRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TransferItem", "description": null, "isOneOf": null, "fields": [{"name": "acceptorId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "reason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sponsorId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TransferListRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "serviceNumberId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TransferListResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TransferItem", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TransferMakeRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TransferResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "timeCost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TransferRobotMakeRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TransferStartRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "reason", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "roomId", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "UpdateAccountProfileInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "file", "description": null, "type": {"kind": "SCALAR", "name": "Upload", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Upload", "description": "The `Upload` scalar type represents a file upload.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "UserInfo", "description": null, "isOneOf": null, "fields": [{"name": "accountId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "avatarId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "channel", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Channel", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isBindAile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isCollectInfo", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isJoinAile", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "openId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "ENUM", "name": "Status", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tenantId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateTime", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VerifyInvitationCodeRequest", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "code", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VerifyInvitationCodeResponse", "description": null, "isOneOf": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "msg", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "isOneOf": null, "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "isOneOf": null, "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByURL", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOneOf", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "checkLoginOtpRequestInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "checkCode", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "onceToken", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}], "directives": [{"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "isRepeatable": false, "locations": ["ARGUMENT_DEFINITION", "ENUM_VALUE", "FIELD_DEFINITION", "INPUT_FIELD_DEFINITION"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\"", "isDeprecated": false, "deprecationReason": null}]}, {"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}, {"name": "oneOf", "description": "Indicates exactly one field must be supplied and this field must not be `null`.", "isRepeatable": false, "locations": ["INPUT_OBJECT"], "args": []}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}, {"name": "specifiedBy", "description": "Exposes a URL that specifies the behavior of this scalar.", "isRepeatable": false, "locations": ["SCALAR"], "args": [{"name": "url", "description": "The URL that specifies the behavior of this scalar.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}]}}