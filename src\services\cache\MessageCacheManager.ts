import { logService } from '../system/logService';
import { Messages } from '../db/initSql';

/**
 * LRU緩存節點
 */
class CacheNode {
  key: string;
  value: Messages[];
  prev: CacheNode | null = null;
  next: CacheNode | null = null;
  
  constructor(key: string, value: Messages[]) {
    this.key = key;
    this.value = value;
  }
}

/**
 * 房間緩存統計
 */
interface RoomCacheStats {
  totalPages: number;
  totalMessages: number;
  lastAccessTime: number;
  memoryUsage: number; // 估算的內存使用量（字節）
}

/**
 * 消息緩存管理器
 * 實現LRU緩存策略和智能內存管理，支持持久化存储
 */
export class MessageCacheManager {
  private cache = new Map<string, CacheNode>();
  private head: CacheNode;
  private tail: CacheNode;
  private maxSize: number;
  private maxMemoryMB: number;
  private currentMemoryUsage = 0;

  // 房間級別的緩存統計
  private roomStats = new Map<string, RoomCacheStats>();

  // 緩存命中統計
  private hitCount = 0;
  private missCount = 0;

  // 持久化相关
  private readonly STORAGE_KEY = 'message_cache_data';
  private readonly STATS_KEY = 'message_cache_stats';
  private readonly MAX_STORAGE_SIZE = 10 * 1024 * 1024; // 10MB localStorage 限制
  private saveTimer: NodeJS.Timeout | null = null;
  private debounceTimer: NodeJS.Timeout | null = null;

  constructor(maxSize = 100, maxMemoryMB = 50) {
    this.maxSize = maxSize;
    this.maxMemoryMB = maxMemoryMB;

    // 創建虛擬頭尾節點
    this.head = new CacheNode('', []);
    this.tail = new CacheNode('', []);
    this.head.next = this.tail;
    this.tail.prev = this.head;

    // 从持久化存储恢复缓存
    this.loadFromStorage();

    // 设置页面卸载时保存缓存
    this.setupBeforeUnloadHandler();

    // 设置定期保存（每5分钟）
    this.startPeriodicSave();

    logService.info('消息緩存管理器初始化', {
      maxSize,
      maxMemoryMB,
      restoredCacheSize: this.cache.size
    });
  }
  
  /**
   * 生成緩存鍵
   */
  private generateKey(roomId: string, page: number): string {
    return `${roomId}:${page}`;
  }
  
  /**
   * 估算消息數組的內存使用量（字節）
   */
  private estimateMemoryUsage(messages: Messages[]): number {
    let totalSize = 0;
    
    for (const message of messages) {
      // 估算每個消息對象的大小
      totalSize += JSON.stringify(message).length * 2; // UTF-16編碼，每字符2字節
      totalSize += 200; // 對象開銷
    }
    
    return totalSize;
  }
  
  /**
   * 添加節點到鏈表頭部
   */
  private addToHead(node: CacheNode): void {
    node.prev = this.head;
    node.next = this.head.next;
    
    if (this.head.next) {
      this.head.next.prev = node;
    }
    this.head.next = node;
  }
  
  /**
   * 移除節點
   */
  private removeNode(node: CacheNode): void {
    if (node.prev) {
      node.prev.next = node.next;
    }
    if (node.next) {
      node.next.prev = node.prev;
    }
  }
  
  /**
   * 移動節點到頭部
   */
  private moveToHead(node: CacheNode): void {
    this.removeNode(node);
    this.addToHead(node);
  }
  
  /**
   * 移除尾部節點
   */
  private removeTail(): CacheNode | null {
    const lastNode = this.tail.prev;
    if (lastNode && lastNode !== this.head) {
      this.removeNode(lastNode);
      return lastNode;
    }
    return null;
  }
  
  /**
   * 更新房間統計信息
   */
  private updateRoomStats(roomId: string, page: number, messages: Messages[]): void {
    const memoryUsage = this.estimateMemoryUsage(messages);
    const stats = this.roomStats.get(roomId) || {
      totalPages: 0,
      totalMessages: 0,
      lastAccessTime: 0,
      memoryUsage: 0
    };
    
    stats.totalPages = Math.max(stats.totalPages, page + 1);
    stats.totalMessages += messages.length;
    stats.lastAccessTime = Date.now();
    stats.memoryUsage += memoryUsage;
    
    this.roomStats.set(roomId, stats);
  }
  
  /**
   * 檢查並清理內存
   */
  private checkMemoryUsage(): void {
    const maxMemoryBytes = this.maxMemoryMB * 1024 * 1024;
    
    while (this.currentMemoryUsage > maxMemoryBytes && this.cache.size > 0) {
      const removedNode = this.removeTail();
      if (removedNode) {
        const memoryUsage = this.estimateMemoryUsage(removedNode.value);
        this.currentMemoryUsage -= memoryUsage;
        this.cache.delete(removedNode.key);
        
        logService.debug('因內存限制移除緩存', {
          key: removedNode.key,
          memoryUsage,
          currentMemoryUsage: this.currentMemoryUsage
        });
      }
    }
  }
  
  /**
   * 獲取緩存的消息
   */
  get(roomId: string, page: number): Messages[] | null {
    const key = this.generateKey(roomId, page);
    const node = this.cache.get(key);
    
    if (node) {
      this.hitCount++;
      this.moveToHead(node);
      
      // 更新訪問統計
      const stats = this.roomStats.get(roomId);
      if (stats) {
        stats.lastAccessTime = Date.now();
      }
      
      logService.debug('緩存命中', { roomId, page, messagesCount: node.value.length });
      return node.value;
    }
    
    this.missCount++;
    logService.debug('緩存未命中', { roomId, page });
    return null;
  }
  
  /**
   * 設置緩存
   */
  set(roomId: string, page: number, messages: Messages[]): void {
    const key = this.generateKey(roomId, page);
    const memoryUsage = this.estimateMemoryUsage(messages);
    
    if (this.cache.has(key)) {
      // 更新現有緩存
      const node = this.cache.get(key)!;
      const oldMemoryUsage = this.estimateMemoryUsage(node.value);
      
      node.value = messages;
      this.currentMemoryUsage = this.currentMemoryUsage - oldMemoryUsage + memoryUsage;
      this.moveToHead(node);
    } else {
      // 添加新緩存
      const newNode = new CacheNode(key, messages);
      
      if (this.cache.size >= this.maxSize) {
        const removedNode = this.removeTail();
        if (removedNode) {
          const removedMemoryUsage = this.estimateMemoryUsage(removedNode.value);
          this.currentMemoryUsage -= removedMemoryUsage;
          this.cache.delete(removedNode.key);
        }
      }
      
      this.cache.set(key, newNode);
      this.addToHead(newNode);
      this.currentMemoryUsage += memoryUsage;
    }
    
    // 更新房間統計
    this.updateRoomStats(roomId, page, messages);
    
    // 檢查內存使用量
    this.checkMemoryUsage();

    // 延迟保存到持久化存储（避免频繁保存）
    this.debouncedSave();

    logService.debug('設置緩存', {
      roomId,
      page,
      messagesCount: messages.length,
      memoryUsage,
      totalMemoryUsage: this.currentMemoryUsage
    });
  }
  
  /**
   * 清除指定房間的所有緩存
   */
  clearRoom(roomId: string): void {
    const keysToRemove: string[] = [];
    
    for (const [key, node] of this.cache) {
      if (key.startsWith(`${roomId}:`)) {
        keysToRemove.push(key);
        const memoryUsage = this.estimateMemoryUsage(node.value);
        this.currentMemoryUsage -= memoryUsage;
        this.removeNode(node);
      }
    }
    
    keysToRemove.forEach(key => this.cache.delete(key));
    this.roomStats.delete(roomId);

    // 更新持久化存储
    this.debouncedSave();

    logService.info('清除房間緩存', { roomId, removedKeys: keysToRemove.length });
  }
  
  /**
   * 清除所有緩存
   */
  clear(): void {
    this.cache.clear();
    this.roomStats.clear();
    this.currentMemoryUsage = 0;
    this.head.next = this.tail;
    this.tail.prev = this.head;

    // 清除持久化存储
    this.clearStorage();

    logService.info('清除所有緩存');
  }

  /**
   * 从 localStorage 加载缓存数据
   */
  private loadFromStorage(): void {
    try {
      const cacheData = localStorage.getItem(this.STORAGE_KEY);
      const statsData = localStorage.getItem(this.STATS_KEY);

      if (cacheData) {
        const parsed = JSON.parse(cacheData);
        const { cacheEntries, timestamp } = parsed;

        // 检查数据是否过期（24小时）
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        if (now - timestamp < maxAge && Array.isArray(cacheEntries)) {
          // 恢复缓存数据
          cacheEntries.forEach(({ key, value }: { key: string, value: Messages[] }) => {
            const node = new CacheNode(key, value);
            this.cache.set(key, node);
            this.addToHead(node);
            this.currentMemoryUsage += this.estimateMemoryUsage(value);
          });

          logService.info('从持久化存储恢复缓存', {
            entriesCount: cacheEntries.length,
            memoryUsage: this.currentMemoryUsage
          });
        } else {
          logService.info('缓存数据已过期，清除存储');
          this.clearStorage();
        }
      }

      if (statsData) {
        const stats = JSON.parse(statsData);
        this.roomStats = new Map(stats.roomStats || []);
        this.hitCount = stats.hitCount || 0;
        this.missCount = stats.missCount || 0;
      }

    } catch (error) {
      logService.warn('加载持久化缓存失败', { error });
      this.clearStorage();
    }
  }

  /**
   * 保存缓存数据到 localStorage
   */
  private saveToStorage(): void {
    try {
      // 准备缓存数据
      const cacheEntries: Array<{ key: string, value: Messages[] }> = [];

      for (const [key, node] of this.cache) {
        cacheEntries.push({ key, value: node.value });
      }

      const cacheData = {
        cacheEntries,
        timestamp: Date.now()
      };

      const statsData = {
        roomStats: Array.from(this.roomStats.entries()),
        hitCount: this.hitCount,
        missCount: this.missCount
      };

      // 检查存储大小
      const cacheStr = JSON.stringify(cacheData);
      const statsStr = JSON.stringify(statsData);

      if (cacheStr.length + statsStr.length > this.MAX_STORAGE_SIZE) {
        logService.warn('缓存数据过大，跳过持久化存储', {
          size: cacheStr.length + statsStr.length,
          maxSize: this.MAX_STORAGE_SIZE
        });
        return;
      }

      localStorage.setItem(this.STORAGE_KEY, cacheStr);
      localStorage.setItem(this.STATS_KEY, statsStr);

      logService.debug('缓存数据已保存到持久化存储', {
        entriesCount: cacheEntries.length,
        dataSize: cacheStr.length + statsStr.length
      });

    } catch (error) {
      logService.warn('保存缓存到持久化存储失败', { error });
    }
  }

  /**
   * 清除持久化存储
   */
  private clearStorage(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.STATS_KEY);
      logService.debug('已清除持久化缓存存储');
    } catch (error) {
      logService.warn('清除持久化存储失败', { error });
    }
  }

  /**
   * 设置页面卸载时保存缓存
   */
  private setupBeforeUnloadHandler(): void {
    if (typeof window !== 'undefined') {
      const handleBeforeUnload = () => {
        this.saveToStorage();
      };

      window.addEventListener('beforeunload', handleBeforeUnload);
      window.addEventListener('pagehide', handleBeforeUnload);

      // 移动端额外监听 visibilitychange
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          this.saveToStorage();
        }
      });
    }
  }

  /**
   * 开始定期保存
   */
  private startPeriodicSave(): void {
    // 每5分钟保存一次
    this.saveTimer = setInterval(() => {
      this.saveToStorage();
    }, 5 * 60 * 1000);
  }

  /**
   * 停止定期保存
   */
  private stopPeriodicSave(): void {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
      this.saveTimer = null;
    }
  }

  /**
   * 防抖保存（避免频繁保存）
   */
  private debouncedSave(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      this.saveToStorage();
      this.debounceTimer = null;
    }, 1000); // 1秒防抖
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    this.stopPeriodicSave();

    // 清除防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    this.saveToStorage(); // 最后保存一次
    this.clear();
    logService.info('消息缓存管理器已销毁');
  }

  /**
   * 獲取緩存統計信息
   */
  getStats() {
    const hitRate = this.hitCount + this.missCount > 0 
      ? (this.hitCount / (this.hitCount + this.missCount) * 100).toFixed(2)
      : '0.00';
    
    return {
      cacheSize: this.cache.size,
      maxSize: this.maxSize,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: `${hitRate}%`,
      memoryUsageMB: (this.currentMemoryUsage / 1024 / 1024).toFixed(2),
      maxMemoryMB: this.maxMemoryMB,
      roomCount: this.roomStats.size,
      roomStats: Array.from(this.roomStats.entries()).map(([roomId, stats]) => ({
        roomId,
        ...stats,
        memoryUsageMB: (stats.memoryUsage / 1024 / 1024).toFixed(2)
      }))
    };
  }
  
  /**
   * 預載入指定房間的下一頁
   */
  async preloadNextPage(
    roomId: string, 
    currentPage: number, 
    loadFunction: (roomId: string, page: number) => Promise<Messages[]>
  ): Promise<void> {
    const nextPage = currentPage + 1;
    const cached = this.get(roomId, nextPage);
    
    if (!cached) {
      try {
        const messages = await loadFunction(roomId, nextPage);
        if (messages.length > 0) {
          this.set(roomId, nextPage, messages);
          logService.debug('預載入下一頁成功', { roomId, nextPage, messagesCount: messages.length });
        }
      } catch (error) {
        logService.error('預載入下一頁失敗', { error, roomId, nextPage });
      }
    }
  }
}

// 導出單例
export const messageCacheManager = new MessageCacheManager();
export default messageCacheManager;
