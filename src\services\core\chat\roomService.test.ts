import { fetchRoomServiceWeightSync, fetchRoomServiceRobotList, fetchRoomServiceEndList } from './roomService';
import httpService from '../../system/httpService';
import { ConstantUtil } from '@/utils/constantUtil';
import { logService } from '../../system/logService';
import CryptoUtil from '@/utils/cryptoUtil';
import { firstValueFrom } from 'rxjs';
import { IWeightRoomListRequest, IServiceRoomListRequest } from '@/types/room.types';

jest.mock('../../system/httpService');
jest.mock('../../system/logService');
jest.mock('@/utils/cryptoUtil');
jest.mock('rxjs', () => ({
  ...(jest.requireActual('rxjs') as object),
  firstValueFrom: jest.fn()
}));

const mockParams = { id: 1 };
const mockEncryptedResponse = 'encrypted-response';
const mockDecryptedResponse = { success: true };

describe('fetchRoomCommonWeightSync', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('應正確處理查詢參數', async () => {
    const params: IWeightRoomListRequest = {
      direction: 'asc',
      key: '測試',
      orderBy: 'createdAt',
      pageIndex: 1,
      pageSize: 20,
      refreshTime: Date.now(),
      serviceNumberId: 'SN123',
      weight: 10,
    };
    await fetchRoomServiceWeightSync(params);
    // 斷言...
  });

  it('should call httpService.post, decrypt response and log success', async () => {
    (httpService.post as jest.Mock).mockReturnValue('observable');
    (firstValueFrom as jest.Mock).mockResolvedValue(mockEncryptedResponse);
    (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockDecryptedResponse);
    const result = await fetchRoomServiceWeightSync(mockParams);
    expect(httpService.post).toHaveBeenCalledWith(ConstantUtil.API_ROOM_SERVICE_WEIGHT_SYNC, mockParams);
    expect(firstValueFrom).toHaveBeenCalledWith('observable');
    expect(CryptoUtil.decryptApiResponse).toHaveBeenCalledWith(mockEncryptedResponse);
    expect(logService.info).toHaveBeenCalledWith('fetchRoomCommonWeightSync success', { params: mockParams, response: mockDecryptedResponse });
    expect(result).toEqual(mockDecryptedResponse);
  });

  it('should log error and throw when httpService.post or decrypt fails', async () => {
    (httpService.post as jest.Mock).mockReturnValue('observable');
    (firstValueFrom as jest.Mock).mockResolvedValue(mockEncryptedResponse);
    (CryptoUtil.decryptApiResponse as jest.Mock).mockImplementation(() => { throw new Error('decrypt fail'); });
    await expect(fetchRoomServiceWeightSync(mockParams)).rejects.toThrow('decrypt fail');
    expect(logService.error).toHaveBeenCalledWith('fetchRoomCommonWeightSync error', { params: mockParams, error: expect.any(Error) });
  });
});

describe('fetchRoomServiceRobotList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('應正確處理查詢參數', async () => {
    const params: IServiceRoomListRequest = {
      direction: 'desc',
      key: '機器人',
      orderBy: 'updatedAt',
      pageIndex: 2,
      pageSize: 10,
      refreshTime: Date.now(),
      serviceNumberId: 'SN456',
    };
    await fetchRoomServiceRobotList(params);
    // 斷言...
  });

  it('should call httpService.post, decrypt response and log success', async () => {
    (httpService.post as jest.Mock).mockReturnValue('observable');
    (firstValueFrom as jest.Mock).mockResolvedValue(mockEncryptedResponse);
    (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockDecryptedResponse);
    const result = await fetchRoomServiceRobotList(mockParams);
    expect(httpService.post).toHaveBeenCalledWith(ConstantUtil.API_ROOM_SERVICE_ROBOT_LIST, mockParams);
    expect(firstValueFrom).toHaveBeenCalledWith('observable');
    expect(CryptoUtil.decryptApiResponse).toHaveBeenCalledWith(mockEncryptedResponse);
    expect(logService.info).toHaveBeenCalledWith('fetchRoomServiceRobotList success', { params: mockParams, response: mockDecryptedResponse });
    expect(result).toEqual(mockDecryptedResponse);
  });

  it('should log error and throw when httpService.post or decrypt fails', async () => {
    (httpService.post as jest.Mock).mockReturnValue('observable');
    (firstValueFrom as jest.Mock).mockResolvedValue(mockEncryptedResponse);
    (CryptoUtil.decryptApiResponse as jest.Mock).mockImplementation(() => { throw new Error('decrypt fail'); });
    await expect(fetchRoomServiceRobotList(mockParams)).rejects.toThrow('decrypt fail');
    expect(logService.error).toHaveBeenCalledWith('fetchRoomServiceRobotList error', { params: mockParams, error: expect.any(Error) });
  });
});

describe('fetchRoomServiceEndList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call httpService.post, decrypt response and log success', async () => {
    (httpService.post as jest.Mock).mockReturnValue('observable');
    (firstValueFrom as jest.Mock).mockResolvedValue(mockEncryptedResponse);
    (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockDecryptedResponse);
    const result = await fetchRoomServiceEndList(mockParams);
    expect(httpService.post).toHaveBeenCalledWith(ConstantUtil.API_ROOM_SERVICE_END_LIST, mockParams);
    expect(firstValueFrom).toHaveBeenCalledWith('observable');
    expect(CryptoUtil.decryptApiResponse).toHaveBeenCalledWith(mockEncryptedResponse);
    expect(logService.info).toHaveBeenCalledWith('fetchRoomServiceEndList success', { params: mockParams, response: mockDecryptedResponse });
    expect(result).toEqual(mockDecryptedResponse);
  });

  it('should log error and throw when httpService.post or decrypt fails', async () => {
    (httpService.post as jest.Mock).mockReturnValue('observable');
    (firstValueFrom as jest.Mock).mockResolvedValue(mockEncryptedResponse);
    (CryptoUtil.decryptApiResponse as jest.Mock).mockImplementation(() => { throw new Error('decrypt fail'); });
    await expect(fetchRoomServiceEndList(mockParams)).rejects.toThrow('decrypt fail');
    expect(logService.error).toHaveBeenCalledWith('fetchRoomServiceEndList error', { params: mockParams, error: expect.any(Error) });
  });
}); 