import { useState, useEffect, useRef } from 'react';
import { TabBar, Toast } from 'antd-mobile';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { logService } from '../../services/system/logService';
import aileDBService from '../../services/db/aileDBService';
import { ConstantUtil } from '../../utils/constantUtil';
import './HomePage.css';
import Sidebar from '../sidebar';

// Import tab components
import { 
  RoomListTab, 
  TaskTab, 
  ContactTab, 
  MemoTab, 
  AccountTab 
} from './tabs';

// Import tab bar icons
import messageActiveIcon from '../../assets/icons/message-active.svg';
import taskIcon from '../../assets/icons/task.svg';
import clientIcon from '../../assets/icons/client.svg';
import memoIcon from '../../assets/icons/memo.svg';
import accountIcon from '../../assets/icons/account.svg';

// Import header icons
import pointsIcon from '../../assets/icons/points-icon.svg';
import downArrowIcon from '../../assets/icons/down-arrow.svg';
import searchIcon from '../../assets/icons/search.svg';
import scanningIcon from '../../assets/icons/scanning.svg';
import moreIcon from '../../assets/icons/more.svg';

// Import avatars
import { AvatarImage, HomeSkeleton } from '@/components/common';
import stateService from '@/services/stateService';
import { useStatusBarColor } from '../../hooks/useStatusBarColor';
import { useAppSelector } from '../../app/hooks';
import { getRoomsByFilter } from '@/services';
import { getLocalStorage, setLocalStorage } from '@/utils/storage';
import { ROUTE_LOGIN, ROUTE_QRCODE_SCAN } from '../../config/app/routes';

const HomePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('roomlist');
  const [showUserInfo, setShowUserInfo] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [hasCachedData, setHasCachedData] = useState(false);
  const lastScrollY = useRef(0);
  const headerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // 获取Redux状态
  const roomState = useAppSelector(state => state.room);
  const currentTenantId = useAppSelector(state => state.tenant.currentTenantId);
  const isAdmin = useAppSelector(state => state.tenant.isAdmin);

  // 早期缓存检查 - 在组件挂载时立即执行
  useEffect(() => {
    const earlyCheck = async () => {
      // 快速检查Redux状态中是否有数据
      const hasReduxData = roomState.robotRooms.length > 0 ||
                          roomState.activeRooms.length > 0 ||
                          roomState.processedRooms.length > 0;

      if (hasReduxData) {
        logService.info('早期检查：发现Redux缓存数据，准备快速加载');
        setHasCachedData(true);
      }
    };

    earlyCheck();
  }, [roomState]);
  
  // 判斷是否為管理員（owner）身份
  // 刪除原本的 let isAdmin = false; 及 stateService?.isAdmin?.() 相關邏輯
  
  // 根據身份設置背景色
  const headerBgColor = isAdmin ? '#E4F4FD' : '#E6FBF3';
  const chatBgColor = isAdmin ? '#E4F4FD' : '#E6FBF3';

  // 使用状态栏颜色管理
  const { setColor: setStatusBarColor } = useStatusBarColor({
    customColor: headerBgColor,
    autoDetectStyle: true,
    delay: 100
  });

  // 检查是否从聊天室返回
  const isFromChatRoom = (): boolean => {
    const referrer = document.referrer;
    const currentOrigin = window.location.origin;

    // 检查是否从同域的聊天室页面返回
    if (referrer.startsWith(currentOrigin)) {
      const referrerPath = referrer.replace(currentOrigin, '');
      const chatRoomPaths = ['/chat/', '/customer-chat/', '/system-chat/', '/team-chat/'];
      return chatRoomPaths.some(path => referrerPath.includes(path));
    }

    return false;
  };

  // 记录访问模式，用于优化后续加载
  const recordVisitPattern = (hasCachedData: boolean) => {
    try {
      const visitHistory = getLocalStorage<any>('home_visit_history', {
        lastVisit: 0,
        hasCachedDataCount: 0,
        totalVisits: 0
      });

      visitHistory.lastVisit = Date.now();
      visitHistory.totalVisits += 1;
      if (hasCachedData) {
        visitHistory.hasCachedDataCount += 1;
      }

      setLocalStorage('home_visit_history', visitHistory);

      logService.debug('记录访问模式', {
        hasCachedData,
        cacheRatio: visitHistory.hasCachedDataCount / visitHistory.totalVisits
      });
    } catch (error) {
      logService.warn('记录访问模式失败', { error });
    }
  };

  // 检查是否有缓存数据
  const checkCachedData = async (): Promise<boolean> => {
    try {
      // 0. 如果是从聊天室返回，直接返回true（不显示骨架屏）
      if (isFromChatRoom()) {
        logService.info('检测到从聊天室返回，跳过骨架屏');
        return true;
      }

      // 1. 检查是否是页面刷新但有持久化的认证状态
      const persistedState = getLocalStorage<any>(ConstantUtil.REDUX_STATE_KEY, null);
      const hasPersistedAuth = persistedState?.auth?.isAuthenticated && persistedState?.auth?.authToken;

      if (hasPersistedAuth) {
        logService.info('检测到持久化的认证状态，可能是页面刷新');
      }

      // 2. 检查Redux store中是否有房间数据
      const hasReduxData = roomState.robotRooms.length > 0 ||
                          roomState.activeRooms.length > 0 ||
                          roomState.processedRooms.length > 0;

      if (hasReduxData) {
        logService.info('发现Redux缓存数据', {
          robotCount: roomState.robotRooms.length,
          activeCount: roomState.activeRooms.length,
          processedCount: roomState.processedRooms.length
        });
        return true;
      }

      // 3. 检查数据库中是否有房间数据
      const dbRobotRooms = await getRoomsByFilter('robot', 0, 10);
      const dbActiveRooms = await getRoomsByFilter('active', 0, 10);
      const dbProcessedRooms = await getRoomsByFilter('processed', 0, 10);

      const hasDbData = dbRobotRooms.length > 0 ||
                       dbActiveRooms.length > 0 ||
                       dbProcessedRooms.length > 0;

      if (hasDbData) {
        logService.info('发现数据库缓存数据', {
          dbRobotCount: dbRobotRooms.length,
          dbActiveCount: dbActiveRooms.length,
          dbProcessedCount: dbProcessedRooms.length
        });
        return true;
      }

      // 4. 如果有持久化认证状态但没有数据，可能是首次登录后的页面刷新
      // 这种情况下仍然显示骨架屏，但时间稍短
      if (hasPersistedAuth) {
        logService.info('有持久化认证但无数据缓存，可能是首次登录后刷新');
        return false; // 仍需要显示骨架屏，但会在后续逻辑中缩短时间
      }

      logService.info('未发现缓存数据，需要显示骨架屏');
      return false;
    } catch (error) {
      logService.error('检查缓存数据失败', { error });
      return false;
    }
  };

  // 初始化檢查
  useEffect(() => {
    const checkInitialization = async () => {
      try {
        // 檢查必要的初始化條件
        const dbInitialized = aileDBService.isInitialized();
        let userReady = false;
        let tenantReady = false;

        try {
          userReady = !!stateService?.loginUser?.();
        } catch (error) {
          logService.error('檢查 stateService.loginUser 出錯', { error });
        }

        try {
          tenantReady = !!stateService?.tenantId?.();
        } catch (error) {
          logService.error('檢查 stateService.tenantId 出錯', { error });
        }

        if (dbInitialized && userReady && tenantReady) {
          setInitialized(true);

          // 检查是否有缓存数据
          const hasCached = await checkCachedData();
          setHasCachedData(hasCached);

          // 记录访问模式
          recordVisitPattern(hasCached);

          if (hasCached) {
            // 有缓存数据，立即显示内容
            logService.info('检测到缓存数据，跳过骨架屏');
            setLoading(false);
          } else {
            // 无缓存数据，根据情况调整骨架屏显示时间
            const persistedState = getLocalStorage<any>(ConstantUtil.REDUX_STATE_KEY, null);
            const hasPersistedAuth = persistedState?.auth?.isAuthenticated && persistedState?.auth?.authToken;

            if (hasPersistedAuth) {
              // 有持久化认证但无数据缓存，可能是首次登录后的页面刷新，缩短骨架屏时间
              logService.info('有持久化认证但无数据缓存，显示较短骨架屏');
              setTimeout(() => {
                setLoading(false);
              }, 400);
            } else {
              // 完全首次加载，显示完整骨架屏时间
              logService.info('首次加载无缓存数据，显示完整骨架屏');
              setTimeout(() => {
                setLoading(false);
              }, 800);
            }
          }
        } else {
          // 如果檢查失敗，設置短暫延遲後重試
          setTimeout(checkInitialization, 300);
        }
      } catch (error) {
        logService.error('HomePage: 初始化檢查失敗', error as Error);
        
        // 初始化檢查失敗，提示用戶並重定向到登錄頁面
        Toast.show({
          icon: 'fail',
          content: '頁面載入失敗，請重新登入'
        });
        
        setTimeout(() => {
          navigate(ROUTE_LOGIN, { 
            replace: true,
            state: { skipTokenCheck: true }
          });
        }, 2000);
      }
    };
    
    checkInitialization();
  }, [navigate]);

  useEffect(() => {
    // 只有初始化成功後才執行標籤頁相關邏輯
    if (!initialized) return;
    
    // Parse the tab from the URL if present
    const hash = location.hash.replace('#', '');
    if (hash && ['roomlist', 'task', 'contact', 'memo', 'account'].includes(hash)) {
      // 如果不是管理員且選擇了 account 標籤，則默認顯示 roomlist 標籤
      if (!isAdmin && hash === 'account') {
        setActiveTab('roomlist');
        navigate('#roomlist');
      } else {
        setActiveTab(hash);
      }
    }

    // 移除全局 touchmove 事件攔截，保證 SwipeAction 正常
    // 設置聊天室背景色
    document.documentElement.style.setProperty('--chat-background-color', chatBgColor);

    // 设置状态栏颜色与页面头部颜色一致
    setStatusBarColor(headerBgColor);

    return () => {
      // 重置聊天室背景色
      document.documentElement.style.removeProperty('--chat-background-color');
    };
  }, [location, isAdmin, chatBgColor, navigate, initialized]);

  // Handle scroll event for header visibility
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const currentScrollY = e.currentTarget.scrollTop;
    if (currentScrollY > lastScrollY.current && currentScrollY > 50) {
      // Scrolling down - hide user info
      setShowUserInfo(false);
    } else {
      // Scrolling up - show user info
      setShowUserInfo(true);
    }
    lastScrollY.current = currentScrollY;
  };

  // Handle tab change
  const handleTabChange = (key: string) => {
    // 如果不是管理員且選擇了 account 標籤，則不進行切換
    if (!isAdmin && key === 'account') {
      return;
    }
    setActiveTab(key);
    // Update the URL hash to reflect the current tab
    navigate(`#${key}`);
  };

  // 根據當前標籤頁獲取標題文案
  const getHeaderTitle = () => {
    switch (activeTab) {
      case 'roomlist':
        return '全部列表';
      case 'task':
        return '任務列表';
      case 'contact':
        return '聯繫人列表';
      case 'memo':
        return '備忘錄列表';
      case 'account':
        return '帳號設定';
      default:
        return '全部列表';
    }
  };

  // 取得登入用戶資訊
  let loginUser = null;
  try {
    loginUser = stateService?.loginUser?.();
  } catch (error) {
    logService.error('獲取登入用戶信息失敗', { error });
  }
  const avatarId: string | null = loginUser?.avatarId ?? null;
  const userName = loginUser?.name || '';

  // 打開側邊欄
  const handleOpenSidebar = () => {
    setSidebarOpen(true);
  };

  // 關閉側邊欄
  const handleCloseSidebar = () => {
    setSidebarOpen(false);
  };

  // 處理掃描圖標點擊
  const handleScanClick = () => {
    navigate(ROUTE_QRCODE_SCAN);
  };

  // 顯示加載中畫面
  if (loading || !initialized) {
    // 根据缓存状态调整骨架屏的显示阶段
    const loadingStage = hasCachedData ? "authenticated" : "data-loading";

    logService.debug('显示骨架屏', {
      loading,
      initialized,
      hasCachedData,
      loadingStage
    });

    return (
      <HomeSkeleton
        showUserInfo={showUserInfo}
        isAdmin={isAdmin}
        loadingStage={loadingStage}
      />
    );
  }

  return (
    <div className="home-page">
      <div className={`header ${showUserInfo ? '' : 'user-info-hidden'}`} ref={headerRef} style={{ backgroundColor: headerBgColor }}>
        <div className={`user-info ${showUserInfo ? 'visible' : 'hidden'}`} onClick={handleOpenSidebar}>  
          {(
            <AvatarImage avatarId={avatarId} size="L" className="user-avatar" alt={userName} name={userName} />
          )}
          <div className="user-name">
            <span className="name">{userName}</span>
          </div>
          <div className="points">
            <img src={pointsIcon} alt="points" className="points-icon" />
            <span className="points-value">9375</span>
            <span className="points-text">點</span>
          </div>
        </div>
        <div className="nav-bar">
          <div className="nav-left" >
            <div className="dropdown">
              <span className="dropdown-text">{getHeaderTitle()}</span>
              <img src={downArrowIcon} alt="dropdown" className="dropdown-icon" />
            </div>
          </div>
          <div className="nav-right">
            <div className="nav-icon">
              <img src={searchIcon} alt="search" className="nav-icon-search" />
            </div>
            <div className="nav-icon" onClick={handleScanClick}>
              <img src={scanningIcon} alt="scan" className="nav-icon-scan" />
            </div>
            <div className="nav-icon">
              <img src={moreIcon} alt="more" className="nav-icon-more" />
            </div>
          </div>
        </div>
      </div>

      <div ref={contentRef} style={{ flex: 1, overflow: 'hidden' }}>
        {/* 所有 tab 組件同時掛載，僅用 CSS 控制顯示 */}
        <div style={{ display: activeTab === 'roomlist' ? 'block' : 'none', height: '100%' }}>
          <RoomListTab onScroll={handleScroll} />
        </div>
        <div style={{ display: activeTab === 'task' ? 'block' : 'none', height: '100%' }}>
          <TaskTab onScroll={handleScroll} />
        </div>
        <div style={{ display: activeTab === 'contact' ? 'block' : 'none', height: '100%' }}>
          <ContactTab onScroll={handleScroll} active={activeTab === 'contact'} />
        </div>
        <div style={{ display: activeTab === 'memo' ? 'block' : 'none', height: '100%' }}>
          <MemoTab onScroll={handleScroll} />
        </div>
        {isAdmin && (
          <div style={{ display: activeTab === 'account' ? 'block' : 'none', height: '100%' }}>
            <AccountTab onScroll={handleScroll} />
          </div>
        )}
      </div>

      {/* TabBar */}
      <TabBar
        key={currentTenantId}
        activeKey={activeTab}
        onChange={handleTabChange}
        className="tab-bar-wrapper"
      >
          <TabBar.Item 
            key="roomlist" 
            icon={
              <div className="tab-icon">
                <img 
                  src={messageActiveIcon} 
                  alt="roomlist" 
                  className={activeTab === 'roomlist' ? 'active' : ''} 
                />
              </div>
            } 
            title={t('消息')}
          />
          <TabBar.Item 
            key="task" 
            icon={
              <div className="tab-icon">
                <img 
                  src={taskIcon} 
                  alt="task" 
                  className={activeTab === 'task' ? 'active' : ''} 
                />
              </div>
            } 
            title={t('任務')}
          />
          <TabBar.Item 
            key="contact" 
            icon={
              <div className="tab-icon">
                <img 
                  src={clientIcon} 
                  alt="contact" 
                  className={activeTab === 'contact' ? 'active' : ''} 
                />
              </div>
            } 
            title={t('客戶')}
          />
          <TabBar.Item 
            key="memo" 
            icon={
              <div className="tab-icon">
                <img 
                  src={memoIcon} 
                  alt="memo" 
                  className={activeTab === 'memo' ? 'active' : ''} 
                />
              </div>
            } 
            title={t('備忘')}
          />
        {isAdmin && (
          <TabBar.Item 
            key="account" 
            icon={
              <div className="tab-icon">
                <img 
                  src={accountIcon} 
                  alt="account" 
                  className={activeTab === 'account' ? 'active' : ''} 
                />
              </div>
            } 
            title={t('帳號')}
          />
        )}
        </TabBar>
      
      {/* 側邊欄 */}
      <Sidebar isOpen={sidebarOpen} onClose={handleCloseSidebar} />
    </div>
  );
};

export default HomePage; 