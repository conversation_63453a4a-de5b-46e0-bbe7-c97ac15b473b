import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { Tenant } from '../../services/db/initSql';
import { ConstantUtil } from '../../utils/constantUtil';
import { getLocalStorage, setLocalStorage, removeLocalStorage } from '../../utils/storage';
import tenantService from '../../services/core/tenant/tenantService';
import { RootState } from '../store';
import { logService } from '../../services/system/logService';
import { RelationTenantVO } from '@/services/core/tenant/tenantTypes';
import snService from '@/services/core/tenant/snService';
import { fetchRoomItem } from '@/services/core/chat/roomService';
import stateService from '@/services/stateService';
import contactService from '@/services/core/contact/contactService';
import { resetAll as resetRoomState } from './roomSlice';
import { LoginTenant } from '@/types/chat.types';

// 租户状态接口
export interface TenantState {
  tenants: Record<string, Tenant>;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  currentTenantId: string | null;
  currentTenantInfo: Tenant | null;
  // 租戶關係列表相關狀態
  tenantRelations: RelationTenantVO[];
  tenantRelationsStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
  tenantRelationsError: string | null;
  tenantRelationsLastFetched: number | null;
  isAdmin: boolean;
  // 新增: 商务号房间拥有者ID
  bossServiceNumberOwnerId: string | null;
  // 新增: 应用Token状态
  applyTokenStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
  applyTokenError: string | null;
}

const getInitialTenantId = (): string | null => {
  return getLocalStorage<string | null>(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
};

const initialState: TenantState = {
  tenants: {},
  status: 'idle',
  error: null,
  currentTenantId: getInitialTenantId(),
  currentTenantInfo: null,
  // 租戶關係列表狀態
  tenantRelations: [],
  tenantRelationsStatus: 'idle',
  tenantRelationsError: null,
  tenantRelationsLastFetched: null,
  isAdmin: false,
  // 新增: 商务号房间拥有者ID
  bossServiceNumberOwnerId: null,
  // 新增: 应用Token初始状态
  applyTokenStatus: 'idle',
  applyTokenError: null,
};

export const fetchAndSetTenants = createAsyncThunk(
  'tenant/fetchAndSetTenants',
  async (options: { skipFetch?: boolean } = {}, { rejectWithValue }) => {
    try {
      // 避免冗餘調用 - 只有當明確要求不跳過時才調用 fetchTenantRelationList
      if (!options.skipFetch) {
        await tenantService.fetchTenantRelationList();
      }
      
      // 獲取租戶列表（不需要再進行同步）
      const tenantsResult = await tenantService.getTenantRelationList({});
      if (tenantsResult.success) {
        return tenantsResult.data;
      }
      return rejectWithValue('無法獲取租戶列表');
    } catch (error) {
      logService.error('fetchAndSetTenants thunk 失敗', { error });
      return rejectWithValue((error as Error).message);
    }
  }
);

// 缓存相关配置常量
const TENANT_CACHE_CONFIG = {
  // 缓存有效期从5分钟延长到30分钟
  CACHE_TTL: 30 * 60 * 1000,
  // 后台刷新间隔从30分钟延长到2小时
  FORCE_REFRESH_INTERVAL: 2 * 60 * 60 * 1000
};

// 獲取租戶關係列表的 thunk
export const fetchTenantRelations = createAsyncThunk(
  'tenant/fetchTenantRelations',
  async (options: { forceRefresh?: boolean } = {}, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { tenantRelationsLastFetched } = state.tenant;
      const now = Date.now();
      
      // 緩存檢查: 如果不強制刷新且距離上次獲取時間小於缓存有效期，直接使用現有數據
      if (
        !options.forceRefresh && 
        tenantRelationsLastFetched && 
        now - tenantRelationsLastFetched < TENANT_CACHE_CONFIG.CACHE_TTL &&
        state.tenant.tenantRelations.length > 0
      ) {
        logService.info('使用 Redux 緩存的租戶關係列表數據', { 
          age: now - tenantRelationsLastFetched,
          count: state.tenant.tenantRelations.length 
        });
        return state.tenant.tenantRelations;
      }
      
      // 從服務獲取數據
      logService.info('從服務獲取租戶關係列表數據', { forceRefresh: options.forceRefresh });
      const relations = await tenantService.fetchTenantRelationList({
        forceRefresh: options.forceRefresh
      });
      
      if (!relations) {
        return rejectWithValue('無法獲取租戶關係列表');
      }
      
      return relations;
    } catch (error) {
      logService.error('fetchTenantRelations thunk 失敗', { error });
      return rejectWithValue((error as Error).message);
    }
  }
);

// 新增: 应用租户Token的thunk
export const applyTenantToken = createAsyncThunk(
  'tenant/applyTenantToken',
  async (tenantId: string, { dispatch, rejectWithValue }) => {
    try {
      logService.info('開始應用租戶Token', { tenantId });
      
      if (!tenantId) {
        return rejectWithValue('未提供有效的租戶ID');
      }
      
      // 调用服务进行Token申请
      const response = await tenantService.applyTokenRaw(tenantId);
      
      if (!response.success) {
        logService.warn('應用租戶Token失敗', {
          tenantId,
          status: response.status,
          msg: response.msg,
          code: response.code
        });
        return rejectWithValue(response.msg || '應用Token失敗');
      }
      
      const { data } = response;
      
      // 更新本地存储
      setLocalStorage(ConstantUtil.CURRENT_TENANT_ID_KEY, tenantId);
      
      // 更新Redux状态
      dispatch(setCurrentTenantId(tenantId));

      // 立即更新租戶關係列表中的 isLastTenant 狀態，避免等待 API 刷新
      dispatch(updateTenantActiveStatus(tenantId));

      // 重置房间状态，确保不会残留上一个租户的数据
      dispatch(resetRoomState());
      // 更新stateService
      stateService.setTenantId(tenantId);

      // 通知contact页面租户已切换，需要重新渲染当前租户的数据
      stateService.notifyContactListChanged();
      
      if (data?.user) {
        // 设置用户信息
        stateService.setLoginUser(data.user);
      }
      
      if (data?.tenantInfo) {
        // 更新租户信息
        stateService.setLoginTenant(data.tenantInfo as LoginTenant);
        dispatch(setCurrentTenantInfo({
          id: data.tenantInfo.id || '',
          type: data.tenantInfo.type,
          description: data.tenantInfo.description || '',
          code: data.tenantInfo.code,
          name: data.tenantInfo.name,
          shortName: data.tenantInfo.shortName,
          avatarId: data.tenantInfo.avatarId || '',
          accountId: data.tenantInfo.accountId
        }));
      }




      const bossInfo = await snService.getCurrentBossServiceNumber();
      if (bossInfo?.id) {
        // 如果API响应中没有bossServiceNumberId，使用获取到的值
        if (!data?.bossServiceNumberId) {
          setLocalStorage(ConstantUtil.BOSS_SERVICENUMBERID, bossInfo.id);
          stateService.setBossServiceNumberId(bossInfo.id);
        }else{
          setLocalStorage(ConstantUtil.BOSS_SERVICENUMBERID, data.bossServiceNumberId);
          stateService.setBossServiceNumberId(data.bossServiceNumberId);
        }

        // 同步处理管理员权限（权限很重要，不能异步）
        if (bossInfo.memberRoomId) {
          try {
            logService.info('獲取管理員權限信息', { memberRoomId: bossInfo.memberRoomId });

            const room = await fetchRoomItem({ roomId: bossInfo.memberRoomId });
            const user = data?.user;
            const isAdmin = (room?.data.type === 'serviceMember' &&
              user?.id &&
              room?.data.ownerId === user.id) || false;

            stateService.setIsAdmin(isAdmin);
            dispatch(setIsAdmin(isAdmin));

            // 存储商务号房间拥有者ID到Redux
            if (room?.data.ownerId) {
              dispatch(setBossServiceNumberOwnerId(room.data.ownerId));
            }

            logService.info('管理員權限設置完成', {
              isAdmin,
              ownerId: room?.data.ownerId,
              userId: user?.id,
              tenantId
            });
          } catch (error) {
            logService.error('獲取管理員權限失敗', {
              error,
              memberRoomId: bossInfo.memberRoomId,
              tenantId
            });

            // 权限获取失败时设置为非管理员，确保安全
            stateService.setIsAdmin(false);
            dispatch(setIsAdmin(false));
          }
        } else {
          // 没有memberRoomId时设置为非管理员
          logService.warn('商務號沒有memberRoomId，設置為非管理員', { tenantId });
          stateService.setIsAdmin(false);
          dispatch(setIsAdmin(false));
        }

        // 在权限确定后，异步同步客户资料（不阻塞租户切换完成）
        setTimeout(async () => {
          try {
            logService.info('開始同步客戶資料到DB', { bossServiceNumberId: bossInfo.id, tenantId });

            // 直接同步到数据库，不使用Redux缓存
            await contactService.syncContactsToDb(bossInfo.id);

            logService.info('客戶資料同步到DB完成', { bossServiceNumberId: bossInfo.id, tenantId });

            // 同步完成后通知ContactTab从DB重新加载数据
            stateService.notifyContactListChanged();
          } catch (syncErr) {
            logService.error('後台同步客戶資料失敗', {
              error: syncErr,
              bossServiceNumberId: bossInfo.id,
              tenantId
            });
          }
        }, 100); // 延迟100ms执行，确保租户切换完成
      } else {
        // 无法获取商务号信息时设置为非管理员
        logService.warn('無法獲取商務號信息，設置為非管理員', { tenantId });
        stateService.setIsAdmin(false);
        dispatch(setIsAdmin(false));
      }
      
      logService.info('租戶Token應用成功', { 
        tenantId,
        tenantName: data?.tenantInfo?.name,
        userName: data?.user?.name
      });
      
      return data;
    } catch (error) {
      logService.error('應用租戶Token時發生錯誤', { error, tenantId });
      return rejectWithValue((error as Error).message || '應用Token失敗');
    }
  }
);

const tenantSlice = createSlice({
  name: 'tenant',
  initialState,
  reducers: {
    setCurrentTenantId(state, action: PayloadAction<string>) {
      state.currentTenantId = action.payload;
      setLocalStorage(ConstantUtil.CURRENT_TENANT_ID_KEY, action.payload);

      if (state.tenants[action.payload]) {
        state.currentTenantInfo = state.tenants[action.payload];
      }
    },
    setCurrentTenantInfo(state, action: PayloadAction<Tenant | null>) {
      state.currentTenantInfo = action.payload;
    },
    setIsAdmin(state, action: PayloadAction<boolean>) {
      state.isAdmin = action.payload;
    },
    setBossServiceNumberOwnerId(state, action: PayloadAction<string | null>) {
      state.bossServiceNumberOwnerId = action.payload;
    },
    clearTenantState(state) {
      state.tenants = {};
      state.status = 'idle';
      state.error = null;
      state.currentTenantId = null;
      state.currentTenantInfo = null;
      state.tenantRelations = [];
      state.tenantRelationsStatus = 'idle';
      state.tenantRelationsError = null;
      state.tenantRelationsLastFetched = null;
      state.isAdmin = false;
      state.bossServiceNumberOwnerId = null;
      state.applyTokenStatus = 'idle';
      state.applyTokenError = null;
      removeLocalStorage(ConstantUtil.CURRENT_TENANT_ID_KEY);
      removeLocalStorage(ConstantUtil.BOSS_SERVICENUMBERID);
    },
    // 設置租戶關係列表
    setTenantRelations(state, action: PayloadAction<RelationTenantVO[]>) {
      state.tenantRelations = action.payload;
      state.tenantRelationsLastFetched = Date.now();
    },
    // 新增：单独设置最后获取时间
    setTenantRelationsLastFetched(state, action: PayloadAction<number>) {
      state.tenantRelationsLastFetched = action.payload;
    },
    // 更新租戶活躍狀態
    updateTenantActiveStatus(state, action: PayloadAction<string>) {
      const newActiveTenantId = action.payload;
      state.tenantRelations = state.tenantRelations.map(tenant => ({
        ...tenant,
        isLastTenant: tenant.id === newActiveTenantId
      }));
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAndSetTenants.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchAndSetTenants.fulfilled, (state, action: PayloadAction<Tenant[]>) => {
        state.status = 'succeeded';
        const tenantMap: Record<string, Tenant> = {};
        action.payload.forEach(tenant => {
          tenantMap[tenant.id] = tenant;
        });
        state.tenants = tenantMap;
        if (state.currentTenantId && tenantMap[state.currentTenantId]) {
          state.currentTenantInfo = tenantMap[state.currentTenantId];
        }
      })
      .addCase(fetchAndSetTenants.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      // 處理租戶關係列表獲取狀態
      .addCase(fetchTenantRelations.pending, (state) => {
        state.tenantRelationsStatus = 'loading';
      })
      .addCase(fetchTenantRelations.fulfilled, (state, action: PayloadAction<RelationTenantVO[]>) => {
        state.tenantRelationsStatus = 'succeeded';
        state.tenantRelations = action.payload;
        state.tenantRelationsLastFetched = Date.now();
        state.tenantRelationsError = null;
      })
      .addCase(fetchTenantRelations.rejected, (state, action) => {
        state.tenantRelationsStatus = 'failed';
        state.tenantRelationsError = action.payload as string;
      })
      // 新增: 处理应用租户Token状态
      .addCase(applyTenantToken.pending, (state) => {
        state.applyTokenStatus = 'loading';
      })
      .addCase(applyTenantToken.fulfilled, (state) => {
        state.applyTokenStatus = 'succeeded';
        state.applyTokenError = null;
      })
      .addCase(applyTenantToken.rejected, (state, action) => {
        state.applyTokenStatus = 'failed';
        state.applyTokenError = action.payload as string;
      });
  },
});

export const {
  setCurrentTenantId,
  setCurrentTenantInfo,
  clearTenantState,
  setTenantRelations,
  updateTenantActiveStatus,
  setIsAdmin,
  setBossServiceNumberOwnerId
} = tenantSlice.actions;

export const selectTenantById = (state: RootState, tenantId: string) => state.tenant.tenants[tenantId];
export const selectAllTenants = (state: RootState) => Object.values(state.tenant.tenants);
export const selectCurrentTenantInfo = (state: RootState) => state.tenant.currentTenantInfo;
export const selectTenantStatus = (state: RootState) => state.tenant.status;

// 選擇器函數
export const selectTenantRelations = (state: RootState) => state.tenant.tenantRelations;
export const selectTenantRelationsStatus = (state: RootState) => state.tenant.tenantRelationsStatus;
export const selectTenantRelationsLastFetched = (state: RootState) => state.tenant.tenantRelationsLastFetched;

// 新增: 应用Token选择器
export const selectApplyTokenStatus = (state: RootState) => state.tenant.applyTokenStatus;
export const selectApplyTokenError = (state: RootState) => state.tenant.applyTokenError;

// 新增: 商务号房间拥有者ID选择器
export const selectBossServiceNumberOwnerId = (state: RootState) => state.tenant.bossServiceNumberOwnerId;

export default tenantSlice.reducer; 