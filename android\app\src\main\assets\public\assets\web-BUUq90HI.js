import{W as d}from"./index-CzeWrtPj.js";class u extends d{constructor(){super(),this.config=null,this.currentUser=null,this.accessToken=null,this.isInitialized=!1,this.CONFIG_STORAGE_KEY="line_login_config",this.loadConfig()}debugLog(...e){var i;!((i=this.config)===null||i===void 0)&&i.debug&&console.log(...e)}debugWarn(...e){var i;!((i=this.config)===null||i===void 0)&&i.debug&&console.warn(...e)}saveConfig(){if(this.config)try{localStorage.setItem(this.CONFIG_STORAGE_KEY,JSON.stringify(this.config))}catch(e){this.debugWarn("Failed to save Line Login config to localStorage:",e)}}loadConfig(){try{const e=localStorage.getItem(this.CONFIG_STORAGE_KEY);e&&(this.config=JSON.parse(e),this.isInitialized=!0,this.debugLog("LineLoginWeb: config restored from localStorage"))}catch(e){console.warn("Failed to load Line Login config from localStorage:",e),this.clearConfig()}}clearConfig(){try{localStorage.removeItem(this.CONFIG_STORAGE_KEY)}catch(e){this.debugWarn("Failed to clear Line Login config from localStorage:",e)}}async initialize(e){if(e.debug&&console.log("LineLoginWeb: initialize",e),!e)throw new Error("LineLoginConfig is required");if(!e.channelId)throw new Error("channelId is required in LineLoginConfig");if(typeof e.channelId!="string"||e.channelId.trim()==="")throw new Error("channelId must be a non-empty string");if(e.redirectUri&&typeof e.redirectUri!="string")throw new Error("redirectUri must be a string");if(e.scope&&!Array.isArray(e.scope))throw new Error("scope must be an array");if(e.botPrompt&&typeof e.botPrompt!="string")throw new Error("botPrompt must be a string");this.config={channelId:e.channelId.trim(),redirectUri:e.redirectUri||window.location.origin+"/line-callback",scope:e.scope||["profile"],botPrompt:e.botPrompt,debug:e.debug||!1},this.isInitialized=!0,this.saveConfig(),this.debugLog("LineLoginWeb: initialized successfully",{channelId:this.config.channelId,scope:this.config.scope,hasRedirectUri:!!this.config.redirectUri,hasBotPrompt:!!this.config.botPrompt,debug:this.config.debug})}async login(e){if(this.debugLog("LineLoginWeb: login",e),this.isInitialized||this.loadConfig(),!this.isInitialized||!this.config)throw new Error("Plugin not initialized. Call initialize() first.");if(window.location.search.includes("code="))return this.handleLoginCallback();if(window.location.search.includes("error="))throw new Error("Line login failed: "+window.location.search);const i=this.generateCodeVerifier(),r=await this.generateCodeChallenge(i),t=this.generateRandomString(16);sessionStorage.setItem("line_code_verifier",i),sessionStorage.setItem("line_state",t);const s=this.buildAuthUrl(t,r);return this.debugLog("LineLoginWeb: redirecting to",s.toString()),window.location.assign(s.toString()),new Promise(()=>{})}async handleLoginCallback(){var e,i;if(this.isInitialized||this.loadConfig(),!this.isInitialized||!this.config)throw new Error("Plugin not initialized. Configuration lost after redirect.");const r=new URLSearchParams(window.location.search),t=r.get("code"),s=r.get("state"),a=r.get("error"),c=r.get("error_description");if(a)throw new Error(`Line login failed: ${a} - ${c||"Unknown error"}`);if(!t)throw new Error("No authorization code received");const l=sessionStorage.getItem("line_state");if(s!==l)throw new Error("State parameter mismatch - possible CSRF attack");const n=sessionStorage.getItem("line_code_verifier");if(!n)throw new Error("Code verifier not found in session storage");try{const o=await this.exchangeCodeForToken(t,n),g=await this.fetchUserProfile(o.access_token);return this.accessToken=o.access_token,this.currentUser=g,sessionStorage.removeItem("line_code_verifier"),sessionStorage.removeItem("line_state"),{accessToken:o.access_token,expiresIn:o.expires_in||2592e3,refreshToken:o.refresh_token,scope:((i=(e=this.config)===null||e===void 0?void 0:e.scope)===null||i===void 0?void 0:i.join(" "))||"profile",tokenType:o.token_type||"Bearer",userProfile:g}}catch(o){throw sessionStorage.removeItem("line_code_verifier"),sessionStorage.removeItem("line_state"),o}}async exchangeCodeForToken(e,i){var r,t;const s="https://api.line.me/oauth2/v2.1/token",a=((r=this.config)===null||r===void 0?void 0:r.redirectUri)||window.location.origin+"/line-callback",c=((t=this.config)===null||t===void 0?void 0:t.channelId)||"",l=new URLSearchParams({grant_type:"authorization_code",code:e,redirect_uri:a,code_verifier:i,client_id:c}),n=await fetch(s,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:l.toString()});if(!n.ok){const o=await n.json().catch(()=>({}));throw new Error(`Token exchange failed: ${n.status} - ${o.error_description||"Unknown error"}`)}return n.json()}async fetchUserProfile(e){const i=await fetch("https://api.line.me/v2/profile",{headers:{Authorization:`Bearer ${e}`}});if(!i.ok)throw new Error(`Failed to fetch user profile: ${i.status}`);const r=await i.json();return{userId:r.userId,displayName:r.displayName,pictureUrl:r.pictureUrl,statusMessage:r.statusMessage,language:r.language}}generateCodeVerifier(){const e=new Uint8Array(32);return crypto.getRandomValues(e),Array.from(e,i=>i.toString(16).padStart(2,"0")).join("")}async generateCodeChallenge(e){const r=new TextEncoder().encode(e),t=await crypto.subtle.digest("SHA-256",r);return btoa(String.fromCharCode(...new Uint8Array(t))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}generateRandomString(e){const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return Array.from(crypto.getRandomValues(new Uint8Array(e))).map(r=>i[r%i.length]).join("")}buildAuthUrl(e,i){var r;if(!this.config)throw new Error("Plugin not initialized");const t=new URL("https://access.line.me/oauth2/v2.1/authorize");return t.searchParams.append("response_type","code"),t.searchParams.append("client_id",this.config.channelId),t.searchParams.append("redirect_uri",this.config.redirectUri||""),t.searchParams.append("state",e),t.searchParams.append("scope",((r=this.config.scope)===null||r===void 0?void 0:r.join(" "))||"profile"),t.searchParams.append("code_challenge",i),t.searchParams.append("code_challenge_method","S256"),this.config.botPrompt&&t.searchParams.append("bot_prompt",this.config.botPrompt),t}async getUserProfile(){if(this.debugLog("LineLoginWeb: getUserProfile"),this.isInitialized||this.loadConfig(),!this.isInitialized||!this.config)throw new Error("Plugin not initialized. Call initialize() first.");if(!this.currentUser)throw new Error("User not logged in. Call login() first.");return this.currentUser}async isLoggedIn(){if(this.debugLog("LineLoginWeb: isLoggedIn"),this.isInitialized||this.loadConfig(),!this.isInitialized||!this.config)throw new Error("Plugin not initialized. Call initialize() first.");return{isLoggedIn:this.accessToken!==null&&this.currentUser!==null}}async logout(){if(this.debugLog("LineLoginWeb: logout"),this.isInitialized||this.loadConfig(),!this.isInitialized||!this.config)throw new Error("Plugin not initialized. Call initialize() first.");this.currentUser=null,this.accessToken=null,sessionStorage.removeItem("line_code_verifier"),sessionStorage.removeItem("line_state")}async refreshToken(){throw this.debugLog("LineLoginWeb: refreshToken"),this.isInitialized||this.loadConfig(),!this.isInitialized||!this.config?new Error("Plugin not initialized. Call initialize() first."):this.accessToken?new Error("Token refresh not supported in web environment. Please re-login."):new Error("No access token available. User not logged in.")}async echo(e){return this.debugLog("LineLoginWeb: echo",e),e}}export{u as LineLoginWeb};
