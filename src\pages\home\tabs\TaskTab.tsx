import React from 'react';
import { Tabs, Badge, List } from 'antd-mobile';
import { useTranslation } from 'react-i18next';

interface TaskItem {
  id: string;
  title: string;
  description: string;
  status: string;
  time: string;
  priority?: 'high' | 'medium' | 'low';
}

interface TaskTabProps {
  onScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

const TaskTab: React.FC<TaskTabProps> = ({ onScroll }) => {
  const { t } = useTranslation();
  const [activeTaskTab, setActiveTaskTab] = React.useState('all');

  const taskTabs = [
    { key: 'all', title: t('全部') },
    { key: 'todo', title: t('待處理') },
    { key: 'processing', title: t('處理中'), hasBadge: true },
    { key: 'completed', title: t('已完成') },
  ];

  const taskItems: TaskItem[] = [
    {
      id: '1',
      title: t('預約諮詢'),
      description: t('王大明先生預約下週二下午3點進行視訊諮詢'),
      status: 'todo',
      time: '10:30',
      priority: 'high'
    },
    {
      id: '2',
      title: t('回覆客戶問題'),
      description: t('客戶詢問關於產品使用方法的問題，需要詳細回覆'),
      status: 'processing',
      time: '09:45',
      priority: 'medium'
    },
    {
      id: '3',
      title: t('確認訂單'),
      description: t('確認王小明客戶的訂單並安排送貨時間'),
      status: 'processing',
      time: '11:20',
      priority: 'high'
    },
    {
      id: '4',
      title: t('追蹤訂單狀態'),
      description: t('追蹤張三的訂單配送情況並通知客戶'),
      status: 'todo',
      time: '13:15',
      priority: 'low'
    },
    {
      id: '5',
      title: t('客戶回訪'),
      description: t('回訪舊客戶以了解產品使用情況及滿意度'),
      status: 'completed',
      time: '15:00',
      priority: 'medium'
    },
    {
      id: '6',
      title: t('產品培訓'),
      description: t('為新客戶提供產品使用培訓'),
      status: 'completed',
      time: '16:30',
      priority: 'medium'
    }
  ];

  const renderTask = (item: TaskItem) => (
    <List.Item
      key={item.id}
      prefix={
        <div className={`task-priority ${item.priority}`}></div>
      }
      title={
        <div className="task-title-row">
          <span className="task-title">{item.title}</span>
          <span className="task-time">{item.time}</span>
        </div>
      }
      description={
        <div className="task-description">{item.description}</div>
      }
      className="task-item"
    />
  );

  const filteredTasks = activeTaskTab === 'all' 
    ? taskItems 
    : taskItems.filter(task => {
        if (activeTaskTab === 'todo') return task.status === 'todo';
        if (activeTaskTab === 'processing') return task.status === 'processing';
        if (activeTaskTab === 'completed') return task.status === 'completed';
        return true;
      });

  return (
    <>
      <Tabs 
        className="task-tabs" 
        activeKey={activeTaskTab}
        onChange={setActiveTaskTab}
      >
        {taskTabs.map(tab => (
          <Tabs.Tab 
            key={tab.key} 
            title={
              <div className="task-tab-title">
                {tab.title}
                {tab.hasBadge && <Badge className="task-tab-badge" />}
              </div>
            } 
          />
        ))}
      </Tabs>
      <List className="task-list">
        <div className="task-list-wrapper" onScroll={onScroll}>
          {filteredTasks.map(renderTask)}
        </div>
      </List>
    </>
  );
};

export default TaskTab; 