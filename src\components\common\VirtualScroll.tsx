import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';

export interface VirtualScrollItem {
  id: string;
  height?: number;
  data: any;
}

export interface VirtualScrollProps {
  items: VirtualScrollItem[];
  itemHeight?: number; // 預估高度，用於動態高度計算
  containerHeight: number;
  renderItem: (item: VirtualScrollItem, index: number) => React.ReactNode;
  onLoadMore?: () => void;
  loadMoreThreshold?: number; // 觸發載入更多的閾值（像素）
  className?: string;
  maintainScrollPosition?: boolean; // 是否保持滾動位置
  scrollToBottom?: boolean; // 是否自動滾動到底部
  onScroll?: (scrollTop: number, scrollHeight: number, clientHeight: number) => void;
}

interface ItemPosition {
  index: number;
  top: number;
  height: number;
  bottom: number;
}

/**
 * 高性能虛擬滾動組件
 * 支持動態高度計算和滾動位置保持
 */
const VirtualScroll: React.FC<VirtualScrollProps> = ({
  items,
  itemHeight = 80,
  containerHeight,
  renderItem,
  onLoadMore,
  loadMoreThreshold = 200,
  className = '',
  maintainScrollPosition = false,
  scrollToBottom = false,
  onScroll
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 存儲每個項目的實際高度
  const [itemHeights, setItemHeights] = useState<Map<string, number>>(new Map());
  const [positions, setPositions] = useState<ItemPosition[]>([]);
  
  // 計算項目位置
  const calculatePositions = useCallback(() => {
    const newPositions: ItemPosition[] = [];
    let top = 0;
    
    items.forEach((item, index) => {
      const height = itemHeights.get(item.id) || item.height || itemHeight;
      newPositions.push({
        index,
        top,
        height,
        bottom: top + height
      });
      top += height;
    });
    
    setPositions(newPositions);
  }, [items, itemHeights, itemHeight]);
  
  // 當項目或高度變化時重新計算位置
  useEffect(() => {
    calculatePositions();
  }, [calculatePositions]);
  
  // 獲取可見範圍
  const visibleRange = useMemo(() => {
    if (positions.length === 0) return { start: 0, end: 0 };
    
    const containerTop = scrollTop;
    const containerBottom = scrollTop + containerHeight;
    
    let start = 0;
    let end = positions.length - 1;
    
    // 二分查找起始位置
    for (let i = 0; i < positions.length; i++) {
      if (positions[i].bottom > containerTop) {
        start = Math.max(0, i - 1); // 預載入前一個項目
        break;
      }
    }
    
    // 二分查找結束位置
    for (let i = start; i < positions.length; i++) {
      if (positions[i].top > containerBottom) {
        end = Math.min(positions.length - 1, i + 1); // 預載入後一個項目
        break;
      }
    }
    
    return { start, end };
  }, [positions, scrollTop, containerHeight]);
  
  // 總高度
  const totalHeight = useMemo(() => {
    if (positions.length === 0) return 0;
    return positions[positions.length - 1].bottom;
  }, [positions]);
  
  // 處理滾動事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    const newScrollTop = target.scrollTop;
    
    setScrollTop(newScrollTop);
    setIsScrolling(true);
    
    // 清除之前的超時
    if (scrollingTimeoutRef.current) {
      clearTimeout(scrollingTimeoutRef.current);
    }
    
    // 設置滾動結束標記
    scrollingTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);
    
    // 觸發載入更多
    if (onLoadMore && newScrollTop <= loadMoreThreshold) {
      onLoadMore();
    }
    
    // 觸發外部滾動回調
    if (onScroll) {
      onScroll(newScrollTop, target.scrollHeight, target.clientHeight);
    }
  }, [onLoadMore, loadMoreThreshold, onScroll]);
  
  // 項目高度變化回調
  const handleItemHeightChange = useCallback((itemId: string, height: number) => {
    setItemHeights(prev => {
      const newMap = new Map(prev);
      if (newMap.get(itemId) !== height) {
        newMap.set(itemId, height);
        return newMap;
      }
      return prev;
    });
  }, []);
  
  // 滾動到底部
  const scrollToBottomFn = useCallback(() => {
    if (scrollElementRef.current) {
      scrollElementRef.current.scrollTop = scrollElementRef.current.scrollHeight;
    }
  }, []);
  
  // 自動滾動到底部
  useEffect(() => {
    if (scrollToBottom && !isScrolling) {
      scrollToBottomFn();
    }
  }, [scrollToBottom, scrollToBottomFn, isScrolling, items.length]);
  
  // 保持滾動位置（當新項目添加到頂部時）
  useEffect(() => {
    if (maintainScrollPosition && scrollElementRef.current) {
      const element = scrollElementRef.current;
      const prevScrollHeight = element.scrollHeight;
      
      // 在下一個渲染週期後調整滾動位置
      requestAnimationFrame(() => {
        const newScrollHeight = element.scrollHeight;
        const heightDiff = newScrollHeight - prevScrollHeight;
        if (heightDiff > 0) {
          element.scrollTop = scrollTop + heightDiff;
        }
      });
    }
  }, [items.length, maintainScrollPosition, scrollTop]);
  
  // 渲染可見項目
  const visibleItems = useMemo(() => {
    const result: React.ReactNode[] = [];
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      if (i >= 0 && i < items.length && positions[i]) {
        const item = items[i];
        const position = positions[i];
        
        result.push(
          <VirtualScrollItem
            key={item.id}
            item={item}
            index={i}
            top={position.top}
            renderItem={renderItem}
            onHeightChange={handleItemHeightChange}
          />
        );
      }
    }
    
    return result;
  }, [visibleRange, items, positions, renderItem, handleItemHeightChange]);
  
  return (
    <div
      ref={containerRef}
      className={`virtual-scroll-container ${className}`}
      style={{ height: containerHeight, overflow: 'hidden', position: 'relative' }}
    >
      <div
        ref={scrollElementRef}
        className="virtual-scroll-content"
        style={{
          height: '100%',
          overflow: 'auto',
          position: 'relative'
        }}
        onScroll={handleScroll}
      >
        <div
          className="virtual-scroll-spacer"
          style={{ height: totalHeight, position: 'relative' }}
        >
          {visibleItems}
        </div>
      </div>
    </div>
  );
};

// 虛擬滾動項目組件
interface VirtualScrollItemProps {
  item: VirtualScrollItem;
  index: number;
  top: number;
  renderItem: (item: VirtualScrollItem, index: number) => React.ReactNode;
  onHeightChange: (itemId: string, height: number) => void;
}

const VirtualScrollItem: React.FC<VirtualScrollItemProps> = ({
  item,
  index,
  top,
  renderItem,
  onHeightChange
}) => {
  const itemRef = useRef<HTMLDivElement>(null);
  
  // 測量項目高度
  useEffect(() => {
    if (itemRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const height = entry.contentRect.height;
          onHeightChange(item.id, height);
        }
      });
      
      resizeObserver.observe(itemRef.current);
      
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [item.id, onHeightChange]);
  
  return (
    <div
      ref={itemRef}
      className="virtual-scroll-item"
      style={{
        position: 'absolute',
        top,
        left: 0,
        right: 0,
        minHeight: item.height || 'auto'
      }}
    >
      {renderItem(item, index)}
    </div>
  );
};

export default VirtualScroll;
