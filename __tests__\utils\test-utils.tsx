/**
 * 测试工具函数
 * 提供常用的测试辅助函数
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, AuthProvider } from '../../src/contexts';

// 创建测试用的store
function createTestStore(initialState = {}) {
  return configureStore({
    reducer: {
      // TODO: 添加实际的reducer
      test: (state = {}) => state,
    },
    preloadedState: initialState,
  });
}

// 测试包装器
interface TestWrapperProps {
  children: React.ReactNode;
  initialState?: any;
}

function TestWrapper({ children, initialState = {} }: TestWrapperProps) {
  const store = createTestStore(initialState);
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
}

// 自定义render函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any;
}

function customRender(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { initialState, ...renderOptions } = options;
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper initialState={initialState}>{children}</TestWrapper>
  );
  
  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// 创建mock用户
export function createMockUser(overrides = {}) {
  return {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
    roles: ['user'],
    permissions: ['read'],
    ...overrides,
  };
}

// 创建mock房间
export function createMockRoom(overrides = {}) {
  return {
    id: '1',
    name: 'Test Room',
    roomType: 'group',
    memberCount: 5,
    unreadCount: 0,
    isTop: false,
    isMute: false,
    lastMessage: null,
    ...overrides,
  };
}

// 创建mock消息
export function createMockMessage(overrides = {}) {
  return {
    id: '1',
    content: 'Test message',
    time: new Date().toISOString(),
    isUser: true,
    sender: createMockUser(),
    status: 'sent',
    ...overrides,
  };
}

// 等待异步操作完成
export function waitForAsync() {
  return new Promise(resolve => setTimeout(resolve, 0));
}

// 重新导出testing-library的所有工具
export * from '@testing-library/react';
export { customRender as render };
