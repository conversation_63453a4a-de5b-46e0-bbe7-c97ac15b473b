import * as React from 'react';
import { useState, useRef, useEffect } from 'react';
import { Button, Toast, Form } from 'antd-mobile';
import type { Rule } from 'antd-mobile/es/components/form';
import { useNavigate } from 'react-router-dom';

import { logService } from '../../services/system/logService';
import backArrow from '../../assets/images/back_arrow.svg';
import picturesIcon from '../../assets/images/pictures_icon.svg';
import './BusinessSignUpPage.css';
import {  applyTenantToken } from '../../app/slices/tenantSlice';
import { tenantService } from '../../services/core/tenant';
import { ROUTE_HOME, ROUTE_LOGIN } from '../../config/app/routes';
import { useAppDispatch } from '@/app/hooks';
import { useTranslation } from 'react-i18next';


const BusinessSignUpPage: React.FC = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const companyAddressRef = useRef<HTMLInputElement>(null);
  const [form] = Form.useForm();

  // 状态管理
  const [companyLogoUrl, setCompanyLogoUrl] = useState<string | null>(null);
  const [companyLogoFile, setCompanyLogoFile] = useState<File | null>(null);
  const [companyLogoError, setCompanyLogoError] = useState('');
  const [isFormValid, setIsFormValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // 新增加载状态

  // 使用类型安全的 dispatch
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  /**
   * 处理返回按钮点击
   */
  const handleBackClick = () => {
    logService.info('用户点击返回按钮');
    navigate(-1);
  };

  /**
   * 定义表单验证规则
   */
  const companyNameRules: Rule[] = [
    { required: true, message: '請輸入公司/團隊名稱' },
    { max: 18, message: '公司/團隊名稱字數上限18個字元' }
  ];

  const companyUrlRules: Rule[] = [
    {required: true, message: '請輸入公司網址' },
    {
      pattern: /^https:\/\/(www\.)?[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](\.[a-zA-Z]{2,})+([/?].*)?$/,
      message: '請確認公司網址格式是否正確，必須以https開頭'
    }
  ];

  const companyPhoneRules: Rule[] = [
    {required: true, message: '請輸入公司電話' },
    {
      pattern: /^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/,
      message: '請確認電話號碼格式'
    }
  ];

  const companyAddressRules: Rule[] = [
    {required: true, message: '請輸入公司地址' },
    { max: 100, message: '請確認地址格式是否正確' }
  ];

  /**
   * 检查表单是否有效
   */
  const checkFormValidity = () => {
    const values = form.getFieldsValue();
    const companyName = values.companyName?.trim();
    setIsFormValid(!!companyName);
    console.log('公司名稱:', companyName);
    console.log('表單有效性:', !!companyName);
  };

  /**
   * 处理公司Logo点击，触发文件选择
   */
  const handleLogoClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  /**
   * 处理公司Logo文件选择
   */
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setCompanyLogoError('');

    if (!file) {
      return;
    }

    // 验证文件类型
    const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      setCompanyLogoError('請上傳圖片格式文件');
      return;
    }

    // 验证文件大小（2MB = 2 * 1024 * 1024 bytes）
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      setCompanyLogoError('圖片請選擇小於2M的圖片');
      return;
    }

    // 创建预览URL
    const objectUrl = URL.createObjectURL(file);
    setCompanyLogoUrl(objectUrl);
    setCompanyLogoFile(file);

    logService.info('用户上传了公司Logo', { fileSize: file.size, fileType: file.type });
  };

  /**
   * 处理确认按钮点击
   */
  const handleConfirm = async () => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      const values = await form.validateFields();
      logService.info('用户点击确认按钮', {
        ...values,
        hasLogo: !!companyLogoFile
      });
      if (!companyLogoFile) {
        setCompanyLogoError(t('請上傳公司/團隊照片'));
        setIsLoading(false);
        return;
      }
      // 調用 API
      const response = await tenantService.createPersonTenant({
        companyName: values.companyName,
        companyAddress: values.companyAddress,
        companyUrl: values.companyUrl,
        companyPhone: values.companyPhone,
        file: companyLogoFile
      });
      if (!response.success) {
        Toast.show({ content: response.msg ? t(response.msg) : t('公司/團隊資訊註冊失敗，請稍後再試'), duration: 3000 });
        setIsLoading(false);
        return;
      }
      if (response.data?.id) {
        try {
          const resultAction = await dispatch(applyTenantToken(response.data.id));
          if (resultAction.meta.requestStatus === 'fulfilled') {
            navigate(ROUTE_HOME);
          } else {
            const error = resultAction.payload || t('切換公司/團隊資訊失敗');
            logService.error('applyTenantToken 失敗', { error });
            Toast.show({
              content: typeof error === 'string' ? error : t('切換公司/團隊資訊失敗，請稍後再試'),
              duration: 3000
            });
            navigate(ROUTE_LOGIN);
          }
        } catch (error: any) {
          logService.error('applyTenantToken 失敗', error);
          let content = '';
          if (error?.message === 'Network Error' || error?.code === 'ECONNABORTED' || /timeout/i.test(error?.message)) {
            content = t('網路連線異常，請檢查您的網路後重試');
          } else {
            content = error?.message || t('切換公司/團隊資訊失敗，請稍後再試');
          }
          Toast.show({ content, duration: 3000 });
          navigate(ROUTE_LOGIN);
        }
      }
    } catch (error: any) {
      setIsLoading(false);
      logService.error('註冊失敗', error);
      let content = '';
      if (error?.message === 'Network Error' || error?.code === 'ECONNABORTED' || /timeout/i.test(error?.message)) {
        content = t('網路連線異常，請檢查您的網路後重試');
      } else {
        content = error?.message || t('公司/團隊資訊註冊失敗，請稍後再試');
      }
      Toast.show({ content, duration: 3000 });
      return;
    }
  };

  // 初始化表单验证
  useEffect(() => {
    checkFormValidity();
  }, []);

  // 清理预览URL
  useEffect(() => {
    return () => {
      if (companyLogoUrl) {
        URL.revokeObjectURL(companyLogoUrl);
      }
    };
  }, [companyLogoUrl]);

  /**
   * 处理公司地址输入框聚焦，自动滚动到该位置
   */
  const handleAddressFocus = () => {
    setTimeout(() => {
      if (companyAddressRef.current) {
        companyAddressRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }, 300); // 延迟300ms等待虚拟键盘弹出
  };

  return (
      <div className="business-register-page">
        {/* 顶部导航 */}
        <div className="business-register-page__navbar">
          <div
              className="business-register-page__back-button"
              onClick={handleBackClick}
          >
            <img src={backArrow} alt="返回" className="business-register-page__back-icon" />
          </div>
          <div className="business-register-page__title-container">
            <h1 className="business-register-page__title">請填寫公司/團隊資訊</h1>
          </div>
          <div className="business-register-page__spacer"></div> {/* 为了对称留出空间 */}
        </div>

        {/* 表单区域 */}
        <Form
            form={form}
            className="business-register-page__form"
            requiredMarkStyle="none"
            layout="vertical"
            onFinish={handleConfirm}
            onValuesChange={checkFormValidity}
            initialValues={{
              companyName: '',
              companyUrl: '',
              companyPhone: '',
              companyAddress: ''
            }}
        >
          {/* 公司/团队名称 */}
          <Form.Item
              name="companyName"
              className="business-register-page__form-group"
              label={
                <div className="business-register-page__form-label">
                  <span className="business-register-page__label-text">公司/團隊名稱</span>
                  <span className="business-register-page__required">*</span>
                </div>
              }
              rules={companyNameRules}
          >
            <input
                type="text"
                placeholder="請輸入公司/團隊名稱"
                className="business-register-page__input"
                maxLength={18}
            />
          </Form.Item>

          {/* 公司/团队照片 */}
          <div className="business-register-page__form-group">
            <div className="business-register-page__logo-label">
              <span className="business-register-page__label-text">公司/團隊照片</span>
              <span className="business-register-page__required">*</span>
            </div>
            <div
                className="business-register-page__logo-container"
                onClick={handleLogoClick}
            >
              {companyLogoUrl ? (
                  <img src={companyLogoUrl} alt="Company Logo" className="business-register-page__logo" />
              ) : (
                  <img src={picturesIcon} alt="Upload" className="business-register-page__logo-placeholder" />
              )}
            </div>
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept="image/*"
                className="business-register-page__logo-input"
            />
            {/* 错误提示 */}
            {companyLogoError && (
                <div className="business-register-page__error-text">{companyLogoError}</div>
            )}
          </div>

          {/* 公司网址 */}
          <Form.Item
              name="companyUrl"
              className="business-register-page__form-group"
              label={
                <div className="business-register-page__form-label">
                  <span className="business-register-page__label-text">公司網址</span>
                  <span className="business-register-page__required">*</span>
                </div>
              }
              rules={companyUrlRules}
          >
            <input
                type="text"
                placeholder="請輸入公司網址,必須以https開頭"
                className="business-register-page__input"
            />
          </Form.Item>

          {/* 公司电话 */}
          <Form.Item
              name="companyPhone"
              className="business-register-page__form-group"
              label={
                <div className="business-register-page__form-label">
                  <span className="business-register-page__label-text">公司電話</span>
                  <span className="business-register-page__required">*</span>
                </div>
              }
              rules={companyPhoneRules}
          >
            <input
                type="tel"
                placeholder="請輸入公司聯絡電話"
                className="business-register-page__input"
            />
          </Form.Item>

          {/* 地址 */}
          <Form.Item
              name="companyAddress"
              className="business-register-page__form-group mb-10"
              label={
                <div className="business-register-page__form-label">
                  <span className="business-register-page__label-text">地址</span>
                  <span className="business-register-page__required">*</span>
                </div>
              }
              rules={companyAddressRules}
          >
            <input
                ref={companyAddressRef}
                type="text"
                placeholder="請輸入公司地址"
                className="business-register-page__input"
                maxLength={100}
                onFocus={handleAddressFocus}
            />
          </Form.Item>
        </Form>

        {/* 底部固定按钮 */}
        <div className="business-register-page__button-container">
          <Button
              block
              color="primary"
              disabled={!isFormValid || isLoading} // 禁用按钮条件
              onClick={() => form.submit()}
              className={`business-register-page__confirm-button ${isLoading ? 'business-register-page__confirm-button--loading' : ''}`}
              style={{
                opacity: isLoading ? 0.8 : (isFormValid ? 1 : 0.4),
                backgroundColor: '#1677FF'
              }}
          >
            {isLoading ? (
                <span>
                確認中，請稍後<span className="business-register-page__loading-text"></span>
              </span>
            ) : '確認'}
          </Button>
        </div>
      </div>
  );
};

export default BusinessSignUpPage;
