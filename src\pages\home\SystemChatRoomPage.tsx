import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import './SystemChatRoomPage.css';
import { ChatRoom } from './chat';
import { ChatRoomType } from '../../types/chat.types';
import { fetchRoomItem } from '@/services';
import { logService } from '@/services/system/logService';
import { useFixedStatusBarColor } from '../../hooks/useStatusBarColor';
import { PAGE_COLORS } from '../../config/app/pageColors';

const SystemChatRoomPage: React.FC = () => {
  const navigate = useNavigate();
  const { roomId } = useParams<{ roomId: string }>();
  const [roomInfo, setRoomInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 设置系统聊天室状态栏颜色
  useFixedStatusBarColor(PAGE_COLORS.DEFAULT);

  const handleBack = () => {
    navigate(-1);
  };

  useEffect(() => {
    if (!roomId) return;
    setError(null);

    logService.info('開始獲取系統聊天室資訊', { roomId });

    fetchRoomItem({ roomId })
      .then((res) => {
        const roomData = res?.data || null;
        logService.info('系統聊天室資訊獲取成功', {
          roomId,
          hasData: !!roomData,
          lastSequence: roomData?.lastSequence
        });
        setRoomInfo(roomData);
      })
      .catch((err) => {
        logService.error('房間資訊獲取失敗', { error: err, roomId });
        setError('房間資訊獲取失敗');
      });
  }, [roomId]);

  return (
    <div className="system-chat-room-page">
      {error ? (
        <div className="page-error">
          <span>{error}</span>
          <button onClick={() => window.location.reload()}>重新載入</button>
        </div>
      ) : (
        <ChatRoom
          type={ChatRoomType.SYSTEM}
          title={'系統聊天室'}
          dateGroups={[]}
          roomId={roomId || ''}
          roomInfo={roomInfo}
          onBackClick={handleBack}
        />
      )}
    </div>
  );
};

export default SystemChatRoomPage; 