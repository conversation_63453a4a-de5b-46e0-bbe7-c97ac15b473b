@import './chat/ChatRoom.css';

/* 团队聊天室页面样式 */
.team-chat-room-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #E4F4FD;
}

/* 页面错误状态样式 */
.page-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #E4F4FD;
  color: #ff4d4f;
  font-size: 14px;
  gap: 16px;
  padding: 20px;
  text-align: center;
}

.page-error button {
  background-color: #1677FF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.page-error button:hover {
  background-color: #0958d9;
}