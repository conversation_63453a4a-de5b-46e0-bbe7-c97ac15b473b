import React, { useState } from 'react';
import { Button, Space } from 'antd-mobile';
import { HomeSkeleton, MessageTabSkeleton, ChatRoomSkeleton } from '@/components/common';

/**
 * 骨架屏测试页面
 * 用于测试和预览各种骨架屏效果
 */
const SkeletonTestPage: React.FC = () => {
  const [showHomeSkeleton, setShowHomeSkeleton] = useState(false);
  const [showMessageSkeleton, setShowMessageSkeleton] = useState(false);
  const [showChatRoomSkeleton, setShowChatRoomSkeleton] = useState(false);
  const [showUserInfo, setShowUserInfo] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loadingStage, setLoadingStage] = useState<'initial' | 'authenticated' | 'data-loading'>('initial');
  const [chatType, setChatType] = useState<'customer' | 'system' | 'team' | 'my'>('customer');
  const [showTasksBar, setShowTasksBar] = useState(true);

  // 模拟自动加载效果
  const simulateLoading = (type: 'home' | 'message' | 'chatroom') => {
    // 先清除所有显示状态
    setShowHomeSkeleton(false);
    setShowMessageSkeleton(false);
    setShowChatRoomSkeleton(false);

    if (type === 'home') {
      setShowHomeSkeleton(true);
      setTimeout(() => setShowHomeSkeleton(false), 3000);
    } else if (type === 'message') {
      setShowMessageSkeleton(true);
      setTimeout(() => setShowMessageSkeleton(false), 2000);
    } else if (type === 'chatroom') {
      setShowChatRoomSkeleton(true);
      setTimeout(() => setShowChatRoomSkeleton(false), 3000);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>骨架屏测试页面</h2>
      
      <Space direction="vertical" style={{ width: '100%', marginBottom: '20px' }}>
        <h3>Home页面骨架屏</h3>
        <Space wrap>
          <Button 
            color="primary" 
            onClick={() => simulateLoading('home')}
          >
            显示Home骨架屏 (3秒)
          </Button>
          <Button 
            color={showUserInfo ? 'success' : 'default'}
            onClick={() => setShowUserInfo(!showUserInfo)}
          >
            {showUserInfo ? '隐藏' : '显示'}用户信息
          </Button>
          <Button
            color={isAdmin ? 'success' : 'default'}
            onClick={() => setIsAdmin(!isAdmin)}
          >
            {isAdmin ? '普通用户' : '管理员'}模式
          </Button>
        </Space>

        <Space wrap>
          <Button
            color={loadingStage === 'initial' ? 'primary' : 'default'}
            onClick={() => setLoadingStage('initial')}
          >
            初始加载
          </Button>
          <Button
            color={loadingStage === 'authenticated' ? 'primary' : 'default'}
            onClick={() => setLoadingStage('authenticated')}
          >
            已认证
          </Button>
          <Button
            color={loadingStage === 'data-loading' ? 'primary' : 'default'}
            onClick={() => setLoadingStage('data-loading')}
          >
            数据加载中
          </Button>
        </Space>
        
        <h3>MessageTab骨架屏</h3>
        <Button
          color="primary"
          onClick={() => simulateLoading('message')}
        >
          显示MessageTab骨架屏 (2秒)
        </Button>

        <h3>ChatRoom骨架屏</h3>
        <Space wrap>
          <Button
            color="primary"
            onClick={() => simulateLoading('chatroom')}
          >
            显示ChatRoom骨架屏 (3秒)
          </Button>
          <Button
            color={showTasksBar ? 'success' : 'default'}
            onClick={() => setShowTasksBar(!showTasksBar)}
          >
            {showTasksBar ? '隐藏' : '显示'}任务栏
          </Button>
        </Space>

        <Space wrap>
          <Button
            color={chatType === 'customer' ? 'primary' : 'default'}
            onClick={() => setChatType('customer')}
          >
            客户聊天
          </Button>
          <Button
            color={chatType === 'system' ? 'primary' : 'default'}
            onClick={() => setChatType('system')}
          >
            系统聊天
          </Button>
          <Button
            color={chatType === 'team' ? 'primary' : 'default'}
            onClick={() => setChatType('team')}
          >
            团队聊天
          </Button>
          <Button
            color={chatType === 'my' ? 'primary' : 'default'}
            onClick={() => setChatType('my')}
          >
            我的聊天
          </Button>
        </Space>
      </Space>

      {/* 骨架屏展示区域 */}
      <div style={{ 
        border: '1px solid #ddd', 
        borderRadius: '8px', 
        overflow: 'hidden',
        height: '600px',
        position: 'relative'
      }}>
        {showHomeSkeleton ? (
          <HomeSkeleton
            showUserInfo={showUserInfo}
            isAdmin={isAdmin}
            loadingStage={loadingStage}
          />
        ) : showMessageSkeleton ? (
          <MessageTabSkeleton />
        ) : showChatRoomSkeleton ? (
          <ChatRoomSkeleton
            chatType={chatType}
            showTasksBar={showTasksBar}
          />
        ) : (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%',
            color: '#999',
            fontSize: '16px'
          }}>
            点击上方按钮预览骨架屏效果
          </div>
        )}
      </div>

      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <h4>说明：</h4>
        <ul>
          <li>Home骨架屏：模拟登录后进入home页面前的加载状态</li>
          <li>MessageTab骨架屏：模拟消息列表加载时的状态</li>
          <li>ChatRoom骨架屏：模拟聊天室页面刷新时的加载状态</li>
          <li>可以切换用户信息显示/隐藏和管理员/普通用户模式</li>
          <li>可以切换不同类型的聊天室骨架屏效果</li>
          <li>骨架屏会自动消失，模拟数据加载完成</li>
        </ul>
      </div>
    </div>
  );
};

export default SkeletonTestPage;
