/**
 * HTTP服务 - AileApp
 * 基于Axios的HTTP客户端，提供请求拦截、响应处理、错误统一处理等功能
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { BehaviorSubject, Observable, Subject, catchError, finalize, from, map, of, switchMap, takeUntil, tap, throttleTime } from 'rxjs';
import { CryptoUtil } from '../../utils/cryptoUtil';
import { logService } from './logService';
import { envConfig } from '../../config/env';
import { getLocalStorage } from '../../utils/storage';
import deviceService from '../platform/deviceService';

// 请求状态类型
export enum RequestStatus {
  SENT = 'sent',
  UP_PROGRESS = 'upProgress',
  DOWN_PROGRESS = 'downProgress',
  DONE = 'done',
  ERROR = 'error',
  CANCELED = 'canceled',
}

// 请求结果类型
export interface RequestResult<T = any> {
  status: RequestStatus;
  data?: T;
  percent?: number;
  message?: string;
}

// 队列请求项类型
interface QueueItem {
  id: number;
  url: string;
  config: AxiosRequestConfig;
  onComplete: (response: any) => void;
  onError: (error: any) => void;
}

/**
 * HTTP 客户端服务（单例）
 */
class HttpService {
  private static instance: HttpService;
  private axiosInstance: AxiosInstance;
  private requestQueue: Map<string, QueueItem[]> = new Map();
  private processingUrls: Set<string> = new Set();
  private requestCounter: number = 0;
  private requestIndex: number = 0;

  // 加载状态
  private _loading = new BehaviorSubject<boolean>(false);
  private _loadingCount = new BehaviorSubject<number>(0);
  public loading$ = this._loading.asObservable();
  public loadingCount$ = this._loadingCount.asObservable();

  // 网络状态
  private _isPaused = false;

  private constructor() {
    this.axiosInstance = axios.create({
      baseURL: envConfig.API_BASE_URL,
      timeout: envConfig.API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json;charset=utf-8',
      },
      // 确保能够获取到完整的响应头
      maxRedirects: 5,
      validateStatus: function (status) {
        return status < 500; // 允许所有小于500的状态码
      }
    });

    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
        (config) => {
          // 确保 headers 对象存在
          if (!config.headers) config.headers = {} as any;

          // 添加设备信息头部
          const deviceData = deviceService.getDeviceDataHeader();
          config.headers['deviceData'] = encodeURIComponent(JSON.stringify(deviceData));

          // 檢查是否需要認證，如果需要且有 token，添加到請求頭
          const noAuth = config.headers['x-no-auth'];
          if (!noAuth) {
            const token = getLocalStorage<string>('authToken', '');
            if (token) {
              config.headers['Authorization'] = `Bearer ${token}`;
              // 添加 satoken 到请求头
              config.headers['satoken'] = token;

              // 记录已添加的satoken，用于调试
              logService.debug('已添加satoken到请求头', {
                url: config.url,
                satoken: token.substring(0, 5) + '...',
                headers: Object.keys(config.headers)
              });
            }
          } else {
            // 移除特殊標記，避免發送到服務器
            delete config.headers['x-no-auth'];
          }

          // 如果请求体中有加密数据（字符串），添加签名
          if (config.data && typeof config.data === 'string') {
            config.headers['x-aile-siguare'] = CryptoUtil.sha256(config.data);
          }

          return config;
        },
        (error) => {
          logService.error('请求拦截器错误:', error);
          return Promise.reject(error);
        }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
        (response) => {
          // 记录响应拦截器中的完整头信息
          logService.debug(`Response Interceptor - ${response.config.method?.toUpperCase()} ${response.config.url}:`, {
            status: response.status,
            statusText: response.statusText,
            headers: response.headers,
            traceid: response.headers['traceid'] || response.headers['x-trace-id'] || response.headers['trace-id'],
            requestId: response.headers['request-id'] || response.headers['x-request-id'],
            correlationId: response.headers['correlation-id'] || response.headers['x-correlation-id'],
            timestamp: new Date().toISOString()
          });

          // 处理响应数据
          return response;
        },
        (error) => {
          // 记录错误拦截器中的头信息
          if (error.response) {
            logService.error(`HTTP Error Response for ${error.response.config?.method?.toUpperCase()} ${error.response.config?.url}:`, {
              status: error.response.status,
              statusText: error.response.statusText,
              headers: error.response.headers, // 记录完整的 headers 对象
              traceid: error.response.headers['traceid'] || error.response.headers['x-trace-id'] || error.response.headers['trace-id'],
              requestId: error.response.headers['request-id'] || error.response.headers['x-request-id'],
              correlationId: error.response.headers['correlation-id'] || error.response.headers['x-correlation-id'],
              url: error.response.config?.url,
              method: error.response.config?.method?.toUpperCase(),
              data: error.response.data,
              timestamp: new Date().toISOString()
            });
          }

          // 处理响应错误
          logService.error('HTTP 请求失败:', error);
          return Promise.reject(error);
        }
    );

    // 启动队列处理器
    this.startQueueProcessor();

    // 监听网络状态变化
    this.listenToNetworkChanges();
  }

  /**
   * 获取 HTTP 服务单例
   */
  public static getInstance(): HttpService {
    if (!HttpService.instance) {
      HttpService.instance = new HttpService();
    }
    return HttpService.instance;
  }

  /**
   * 设置网络暂停状态
   */
  public setPaused(isPaused: boolean): void {
    this._isPaused = isPaused;
    if (!isPaused) {
      // 恢复网络连接时立即处理队列
      this.processQueue();
    }
  }

  /**
   * 监听网络状态变化
   */
  private listenToNetworkChanges(): void {
    window.addEventListener('online', () => this.setPaused(false));
    window.addEventListener('offline', () => this.setPaused(true));
  }

  /**
   * 启动队列处理器
   */
  private startQueueProcessor(): void {
  }

  /**
   * 处理请求队列
   */
  private processQueue(): void {
    if (this._isPaused) return;

    // 遍历所有 URL 队列
    Array.from(this.requestQueue.entries()).forEach(([url, queue]) => {
      // 如果当前 URL 没有正在处理的请求且队列不为空
      if (!this.processingUrls.has(url) && queue.length > 0) {
        // 标记当前 URL 正在处理
        this.processingUrls.add(url);

        // 取出队列中的第一个请求
        const item = queue.shift();
        if (!item) return;

        // 执行请求
        this.executeRequest(url, item);
      }
    });

    // 清理空队列
    Array.from(this.requestQueue.entries()).forEach(([url, queue]) => {
      if (queue.length === 0) {
        this.requestQueue.delete(url);
      }
    });
  }

  /**
   * 执行请求
   */
  private executeRequest(url: string, item: QueueItem): void {
    // 移除冗餘satoken設置，統一由請求攔截器處理
    // 记录请求配置
    logService.debug(`executeRequest [${item.config.method?.toUpperCase()}] ${url}:`, {
      headers: item.config.headers ? Object.keys(item.config.headers) : [],
      hasSatoken: item.config.headers?.satoken ? true : false,
      satoken: item.config.headers?.satoken ? `${String(item.config.headers.satoken).substring(0, 5)}...` : 'none'
    });

    this.axiosInstance(item.config)
        .then((response) => {
          // 调试：记录响应对象的完整结构
          logService.debug(`HTTP Response Debug for ${item.config.method?.toUpperCase()} ${url}:`, {
            status: response.status,
            statusText: response.statusText,
            headersType: typeof response.headers,
            headersKeys: Object.keys(response.headers || {}),
            headersValues: response.headers,
            responseObjectKeys: Object.keys(response),
            config: {
              url: response.config?.url,
              method: response.config?.method,
              baseURL: response.config?.baseURL
            }
          });

          // 尝试不同的方式获取 traceid
          const possibleTraceIds = [
            response.headers?.['traceid'],
            response.headers?.['x-trace-id'],
            response.headers?.['trace-id'],
            response.headers?.['traceId'],
            response.headers?.['TraceId'],
            response.headers?.['TRACEID']
          ].filter(Boolean);

          logService.debug(`Trace ID candidates for ${url}:`, {
            possibleTraceIds,
            allHeaders: response.headers
          });

          item.onComplete(response.data);
        })
        .catch((error) => {
          // 调试错误响应
          if (error.response) {
            logService.error(`HTTP Error Response Debug for ${item.config.method?.toUpperCase()} ${url}:`, {
              status: error.response.status,
              statusText: error.response.statusText,
              headersType: typeof error.response.headers,
              headersKeys: Object.keys(error.response.headers || {}),
              headersValues: error.response.headers,
              data: error.response.data,
              errorType: error.name,
              errorMessage: error.message
            });
          } else {
            logService.error(`HTTP Network Error for ${item.config.method?.toUpperCase()} ${url}:`, {
              message: error.message,
              code: error.code,
              errorType: error.name,
              url: item.config.url,
              method: item.config.method?.toUpperCase()
            });
          }

          item.onError(error);
        })
        .finally(() => {
          // 标记当前 URL 请求处理完成
          this.processingUrls.delete(url);
          this.decrementLoading();

          // 继续处理该 URL 的下一个请求
          this.processQueue();
        });
  }

  /**
   * 增加加载计数
   */
  private incrementLoading(): void {
    this.requestCounter++;
    this._loadingCount.next(this.requestCounter);
    this._loading.next(this.requestCounter > 0);
  }

  /**
   * 减少加载计数
   */
  private decrementLoading(): void {
    this.requestCounter = Math.max(0, this.requestCounter - 1);
    this._loadingCount.next(this.requestCounter);
    this._loading.next(this.requestCounter > 0);
  }

  /**
   * 将请求添加到队列
   */
  private enqueue<T>(url: string, config: AxiosRequestConfig): Observable<T> {
    // 记录最终的请求配置，用于调试
    logService.debug(`最终的请求配置 [${config.method?.toUpperCase() || 'UNKNOWN'}] ${url}:`, {
      headers: config.headers ? Object.keys(config.headers) : [],
      hasSatoken: config.headers?.satoken ? '是' : '否',
      dataType: typeof config.data
    });

    return new Observable<T>((observer) => {
      this.requestIndex++;
      const id = this.requestIndex;

      // 创建队列项
      const queueItem: QueueItem = {
        id,
        url,
        config,
        onComplete: (response: any) => {
          observer.next(response);
          observer.complete();
        },
        onError: (error: any) => {
          observer.error(error);
          observer.complete();
        },
      };

      // 添加到队列
      if (!this.requestQueue.has(url)) {
        this.requestQueue.set(url, []);
      }
      this.requestQueue.get(url)?.push(queueItem);

      // 增加加载计数
      this.incrementLoading();

      // 触发队列处理
      this.processQueue();

      // 返回取消订阅函数
      return () => {
        // 如果请求还在队列中，则移除
        const queue = this.requestQueue.get(url);
        if (queue) {
          const index = queue.findIndex(item => item.id === id);
          if (index !== -1) {
            queue.splice(index, 1);
            this.decrementLoading();
          }
        }
      };
    });
  }

  /**
   * 发送 GET 请求
   */
  public get<T = any>(url: string, params?: any, config: AxiosRequestConfig = {}): Observable<T> {
    logService.debug(`HTTP GET: ${url}`);

    return this.enqueue<T>(url, {
      ...config,
      method: 'GET',
      url,
      params,
    });
  }

  /**
   * 发送 POST 请求
   */
  public post<T = any>(url: string, data?: any, config: AxiosRequestConfig = {}, encrypt = true): Observable<T> {
    let requestData = data;
    // 记录初始headers
    logService.debug(`POST初始headers ${url}:`, {
      hasHeaders: !!config.headers,
      headerKeys: config.headers ? Object.keys(config.headers) : [],
      hasSatoken: config.headers?.satoken ? 'yes' : 'no'
    });

    if (!config.headers) {
      config.headers = {} as any;
    }
    const deviceData = deviceService.getDeviceDataHeader();
    const headers: Record<string, any> = {
      ...config.headers,
      'deviceData': encodeURIComponent(JSON.stringify(deviceData)),
      'x-aile-siguare': CryptoUtil.sha256(requestData)
    };
    // 僅當 requestData 不是 FormData 時才設置 Content-Type
    // 如果需要加密数据
    if (encrypt && data) {
      requestData = CryptoUtil.encryptApiRequest(data);
    }
    const isFormData = (typeof FormData !== 'undefined') && data instanceof FormData;
    if (!isFormData) {
      headers['Content-Type'] = 'application/json;charset=utf-8';
    } else {
      // 若有 Content-Type，移除
      if ('Content-Type' in headers) delete headers['Content-Type'];
      headers['Content-Type'] = 'multipart/form-data;';
    }
    config.headers = headers;
    logService.debug(`POST encrypt后headers ${url}:`, {
      headerKeys: Object.keys(config.headers),
      hasSatoken: config.headers?.satoken ? 'yes' : 'no'
    });

    logService.debug(`HTTP POST: ${url}`, {
      requestData: requestData,
      dataType: typeof requestData,
      dataLength: typeof requestData === 'string' ? requestData.length : 'N/A',
      encrypt: encrypt
    });
    // 如果是加密數據（字符串），禁用 Axios 的數據轉換
    const finalConfig = {
      ...config,
      method: 'POST',
      url,
      data: requestData,
    };
    // 不再重複設置satoken，統一由攔截器處理
    // 如果是加密字符串數據，設置轉換器以避免雙重序列化
    if (encrypt && data && typeof requestData === 'string') {
      finalConfig.transformRequest = [(data) => data]; // 直接返回原始數據，不進行轉換
    }
    return this.enqueue<T>(url, finalConfig);
  }

  /**
   * 发送文件上传请求（带进度）
   */
  public uploadFile<T = any>(
      url: string,
      file: File,
      data?: any,
      config: AxiosRequestConfig = {},
      cancelSignal?: Subject<void>,
      encrypt = true
  ): Observable<RequestResult<T>> {
    this.incrementLoading();

    const formData = new FormData();

    // 添加文件
    formData.append('file', file);

    // 添加其他数据
    let dataToSign = '';
    if (data) {
      if (encrypt) {
        const encryptedData = CryptoUtil.encryptApiRequest(data);
        formData.append('data', encryptedData);
        dataToSign = encryptedData;
      } else {
        Object.keys(data).forEach(key => {
          formData.append(key, data[key]);
        });
        dataToSign = JSON.stringify(data);
      }
    }

    // 创建请求头
    if (!config.headers) {
      config.headers = {} as any;
    }

    // 添加设备信息
    const deviceData = deviceService.getDeviceDataHeader();


    // 按照参考代码设置请求头
    config.headers = {
      ...config.headers,
      'deviceData': encodeURIComponent(JSON.stringify(deviceData)),
    };

    // 添加签名
    if (encrypt && data) {
      config.headers['x-aile-siguare'] = CryptoUtil.sha256(dataToSign);
    }

    // 设置上传进度跟踪
    const uploadConfig: AxiosRequestConfig = {
      ...config,
      method: 'POST',
      url,
      data: formData,
      onUploadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
        progressSubject.next({
          status: RequestStatus.UP_PROGRESS,
          percent,
        });
      },
      onDownloadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
        progressSubject.next({
          status: RequestStatus.DOWN_PROGRESS,
          percent,
        });
      },
    };

    logService.debug(`HTTP 文件上传: ${url}`, { fileName: file.name, fileSize: file.size });

    // 创建进度 Subject
    const progressSubject = new Subject<RequestResult<T>>();
    // 发送已开始上传状态
    progressSubject.next({ status: RequestStatus.SENT });

    // 创建 Observable
    const request$ = from(this.axiosInstance.request<T>(uploadConfig)).pipe(
        map(response => ({
          status: RequestStatus.DONE,
          data: response.data
        })),
        catchError(error => {
          logService.error('文件上传失败:', error);
          return of({
            status: RequestStatus.ERROR,
            message: error.message
          });
        }),
        finalize(() => {
          this.decrementLoading();
          progressSubject.complete();
        })
    );

    // 如果提供了取消信号
    let result$ = request$;
    if (cancelSignal) {
      result$ = request$.pipe(
          takeUntil(
              cancelSignal.pipe(
                  tap(() => {
                    progressSubject.next({
                      status: RequestStatus.CANCELED,
                      message: 'Request canceled'
                    });
                  })
              )
          )
      );
    }

    // 合并进度和响应
    return progressSubject.pipe(
        switchMap(progress => {
          if (progress.status === RequestStatus.DONE ||
              progress.status === RequestStatus.ERROR ||
              progress.status === RequestStatus.CANCELED) {
            return of(progress);
          }
          return of(progress).pipe(takeUntil(result$));
        }),
        throttleTime(100) // 限制进度通知频率
    );
  }

  /**
   * 发送文件下载请求（带进度）
   */
  public downloadFile(
      url: string,
      data?: any,
      config: AxiosRequestConfig = {},
      cancelSignal?: Subject<void>,
      encrypt = true
  ): Observable<RequestResult<Blob>> {
    this.incrementLoading();

    // 准备请求数据
    let requestData = data;
    let dataToSign = '';

    if (encrypt && data) {
      const encryptedData = CryptoUtil.encryptApiRequest(data);
      // 直接使用加密後的字符串作為請求數據
      requestData = encryptedData;
      dataToSign = encryptedData;
    }

    // 创建请求头
    if (!config.headers) {
      config.headers = {} as any;
    }

    // 添加设备信息
    const deviceData = deviceService.getDeviceDataHeader();


    // 按照参考代码设置请求头
    config.headers = {
      ...config.headers,
      'Content-Type': 'application/json;charset=utf-8',
      'deviceData': encodeURIComponent(JSON.stringify(deviceData)),

    };

    // 添加签名
    if (encrypt && data) {
      config.headers['x-aile-siguare'] = CryptoUtil.sha256(dataToSign);
    }

    // 设置下载配置
    const downloadConfig: AxiosRequestConfig = {
      ...config,
      method: 'POST',
      url,
      data: requestData,
      responseType: 'blob',
      onDownloadProgress: (progressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
        progressSubject.next({
          status: RequestStatus.DOWN_PROGRESS,
          percent,
        });
      },
    };

    logService.debug(`HTTP 文件下载: ${url}`);

    // 创建进度 Subject
    const progressSubject = new Subject<RequestResult<Blob>>();
    // 发送已开始下载状态
    progressSubject.next({ status: RequestStatus.SENT });

    // 创建 Observable
    const request$ = from(this.axiosInstance.request<Blob>(downloadConfig)).pipe(
        map(response => ({
          status: RequestStatus.DONE,
          data: response.data
        })),
        catchError(error => {
          logService.error('文件下载失败:', error);
          return of({
            status: RequestStatus.ERROR,
            message: error.message
          });
        }),
        finalize(() => {
          this.decrementLoading();
          progressSubject.complete();
        })
    );

    // 如果提供了取消信号
    let result$ = request$;
    if (cancelSignal) {
      result$ = request$.pipe(
          takeUntil(
              cancelSignal.pipe(
                  tap(() => {
                    progressSubject.next({
                      status: RequestStatus.CANCELED,
                      message: 'Request canceled'
                    });
                  })
              )
          )
      );
    }

    // 合并进度和响应
    return progressSubject.pipe(
        switchMap(progress => {
          if (progress.status === RequestStatus.DONE ||
              progress.status === RequestStatus.ERROR ||
              progress.status === RequestStatus.CANCELED) {
            return of(progress);
          }
          return of(progress).pipe(takeUntil(result$));
        }),
        throttleTime(100) // 限制进度通知频率
    );
  }

  /**
   * 发送 PUT 请求
   */
  public put<T = any>(url: string, data?: any, config: AxiosRequestConfig = {}, encrypt = true): Observable<T> {
    let requestData = data;

    // 如果需要加密数据
    if (encrypt && data) {
      // 使用 ECB 模式加密 API 請求數據
      requestData = CryptoUtil.encryptApiRequest(data);

      // 确保 headers 存在
      if (!config.headers) {
        config.headers = {} as any;
      }

      const headers = config.headers as any;
      headers['x-aile-signature'] = CryptoUtil.sha256(requestData);
    }

    logService.debug(`HTTP PUT: ${url}`);

    // 如果是加密數據（字符串），禁用 Axios 的數據轉換
    const finalConfig = {
      ...config,
      method: 'PUT',
      url,
      data: requestData,
    };

    // 如果是加密字符串數據，設置轉換器以避免雙重序列化
    if (encrypt && data && typeof requestData === 'string') {
      finalConfig.transformRequest = [(data) => data]; // 直接返回原始數據，不進行轉換
    }

    return this.enqueue<T>(url, finalConfig);
  }

  /**
   * 发送 DELETE 请求
   */
  public delete<T = any>(url: string, params?: any, config: AxiosRequestConfig = {}): Observable<T> {
    logService.debug(`HTTP DELETE: ${url}`);

    return this.enqueue<T>(url, {
      ...config,
      method: 'DELETE',
      url,
      params,
    });
  }

  /**
   * 取消所有请求
   */
  public cancelAll(): void {
    // 清空所有队列
    this.requestQueue.clear();

    // 重置计数器
    this.requestCounter = 0;
    this._loadingCount.next(0);
    this._loading.next(false);

    logService.info('已取消所有 HTTP 请求');
  }


}

// 导出 HTTP 服务单例
export const httpService = HttpService.getInstance();
export default httpService; 