import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import AccountTab from './AccountTab';
import authReducer from '../../../app/slices/authSlice';

// Mock 日誌服務
jest.mock('../../../services/logService', () => ({
  logService: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// 建立測試用的store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    } as any,
    preloadedState: {
      auth: {
        isAuthenticated: true,
        account: {
          accountId: '1',
          name: '測試用戶',
          countryCode: '+886',
          mobile: '**********',
          type: '3',
          onlineId: 'test-online-id',
          loginStatus: '1',
          email: '<EMAIL>'
        },
        authToken: 'test-token',
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false,
        ...initialState,
      },
    },
  });
};

// 測試組件包裝器
const TestWrapper: React.FC<{ 
  children: React.ReactNode; 
  store: ReturnType<typeof createTestStore>;
}> = ({ children, store }) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('AccountTab', () => {
  const mockOnScroll = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('應該顯示用戶資訊', () => {
    const store = createTestStore();

    render(
      <TestWrapper store={store}>
        <AccountTab onScroll={mockOnScroll} />
      </TestWrapper>
    );

    // 應該顯示用戶名稱
    expect(screen.getByText('測試用戶')).toBeInTheDocument();
    
    // 應該顯示用戶手機號（包含國家代碼）
    expect(screen.getByText('+886 **********')).toBeInTheDocument();
    
    // 應該顯示線上狀態
    expect(screen.getByText('線上')).toBeInTheDocument();
  });

  it('應該顯示功能列表', () => {
    const store = createTestStore();

    render(
      <TestWrapper store={store}>
        <AccountTab onScroll={mockOnScroll} />
      </TestWrapper>
    );

    // 應該顯示各種功能選項
    expect(screen.getByText('個人資料')).toBeInTheDocument();
    expect(screen.getByText('設定')).toBeInTheDocument();
    expect(screen.getByText('幫助與支援')).toBeInTheDocument();
    expect(screen.getByText('關於我們')).toBeInTheDocument();
  });

  it('應該顯示登出按鈕', () => {
    const store = createTestStore();

    render(
      <TestWrapper store={store}>
        <AccountTab onScroll={mockOnScroll} />
      </TestWrapper>
    );

    // 應該顯示登出按鈕
    expect(screen.getByText('登出')).toBeInTheDocument();
  });

  it('點擊個人資料應該顯示用戶詳細資訊', async () => {
    const store = createTestStore();

    render(
      <TestWrapper store={store}>
        <AccountTab onScroll={mockOnScroll} />
      </TestWrapper>
    );

    // 點擊個人資料
    fireEvent.click(screen.getByText('個人資料'));
    
    // 應該顯示用戶詳細資訊彈窗
    await waitFor(() => {
      expect(screen.getByText('用戶詳細資訊')).toBeInTheDocument();
      expect(screen.getByText('用戶ID: 1')).toBeInTheDocument();
      expect(screen.getByText('姓名: 測試用戶')).toBeInTheDocument();
      expect(screen.getByText('手機: +886 **********')).toBeInTheDocument();
    });
  });

  it('點擊其他功能選項應該顯示開發中提示', async () => {
    const store = createTestStore();

    render(
      <TestWrapper store={store}>
        <AccountTab onScroll={mockOnScroll} />
      </TestWrapper>
    );

    // 點擊設定
    fireEvent.click(screen.getByText('設定'));
    
    // 應該顯示功能開發中的提示
    await waitFor(() => {
      expect(screen.getByText('功能開發中...')).toBeInTheDocument();
    });
  });

  it('應該正確處理沒有用戶資訊的情況', () => {
    const store = createTestStore({
      user: null,
    });

    render(
      <TestWrapper store={store}>
        <AccountTab onScroll={mockOnScroll} />
      </TestWrapper>
    );

    // 應該顯示預設用戶名
    expect(screen.getByText('用戶')).toBeInTheDocument();
    
    // 應該顯示ID資訊
    expect(screen.getByText('ID: unknown')).toBeInTheDocument();
  });

  it('應該正確調用onScroll回調', () => {
    const store = createTestStore();

    render(
      <TestWrapper store={store}>
        <AccountTab onScroll={mockOnScroll} />
      </TestWrapper>
    );

    // 模擬滾動事件
    const scrollContainer = screen.getByText('測試用戶').closest('.tab-content');
    const scrollEvent = { target: { scrollTop: 100 } };
    
    fireEvent.scroll(scrollContainer!, scrollEvent);

    // 應該調用onScroll回調
    expect(mockOnScroll).toHaveBeenCalled();
  });
}); 