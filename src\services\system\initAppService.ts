import { logService } from './logService';
import syncInitService from '../syncInitService';
import stateService from '../stateService';
import { getLocalStorage } from '../../utils/storage';
import { ConstantUtil } from '../../utils/constantUtil';
import aileDBService from '../db/aileDBService';
import authService from '../core/auth/authService';
import { fetchRoomItem } from '../core/chat/roomService';
import { applyTenantToken } from '@/app/slices/tenantSlice';

import { LogInState } from '../../types/aile.enum';
import pushService from '../pushService';
import { logout } from '../../app/slices/authSlice';
import { clearTenantState } from '../../app/slices/tenantSlice';
import { userService } from '../core/tenant';


// 定義必要的類型
export interface CoreInitOptions {
  dispatch: Function;
  navigate?: Function;
  loadPersistedState?: Function;
}

export interface InitResult {
  success: boolean;
  accountData: any | null;
  userData: any | null;
  tenantId: string | null;
  tenantInfo: any | null;
  error?: Error;
}

/**
 * App 啟動後的初始化服務
 * 負責核心啟動流程（狀態恢復、身份驗證、數據庫初始化）
 */
class InitAppService {
  private static instance: InitAppService;
  private initialized = false;
  private _initializeCoreCallCount: number = 0;


  private constructor() {}

  public static getInstance(): InitAppService {
    if (!InitAppService.instance) {
      InitAppService.instance = new InitAppService();
    }
    return InitAppService.instance;
  }

  /**
   * 應用核心初始化流程
   * 集成 App.tsx 中的初始化邏輯
   */
  public async initializeCore(options: CoreInitOptions): Promise<InitResult> {
    this._initializeCoreCallCount++;
    logService.info(`initializeCore 被調用，第 ${this._initializeCoreCallCount} 次`, { timestamp: new Date().toISOString() });

    const { dispatch, loadPersistedState } = options;
    let accountData = null;
    let userData = null;
    let tenantId = null;
    let tenantInfo = null;
    
    try {
      logService.info('開始核心應用初始化');

      // 1. 從 localStorage 恢復 Redux 狀態
      if (loadPersistedState) {
        loadPersistedState();
        logService.info('恢復了 Redux 持久化狀態');
      }

      // 2. 加載 stateService 數據
      await stateService.loadFromCache();
      logService.info('已從快取載入 stateService 數據');

      // 3. 優先檢查 token 有效性
      const localToken = getLocalStorage<string | null>(ConstantUtil.TOKEN_KEY, null);
      if (!localToken) {
        logService.warn('未找到本地令牌，初始化無法繼續');

        return { 
          success: false, 
          accountData: null, 
          userData: null, 
          tenantId: null, 
          tenantInfo: null,
          error: new Error('no_token_found') 
        };
      }

      // 設置令牌 (httpService 將在請求攔截器中自動加入令牌)
      logService.info('已確認本地存在有效令牌');

      // 4. 驗證令牌有效性
      logService.info('驗證令牌有效性');
      let isTokenValid = false;
      try {
        // 直接使用返回的 boolean 值
        isTokenValid = await authService.checkAccountToken() as boolean;
        logService.info('令牌驗證結果', { isValid: isTokenValid });
      } catch (error) {
        logService.error('令牌驗證過程出現異常', { error });
        isTokenValid = false;
      }

      if (!isTokenValid) {
        // Token 無效，清除身份驗證信息
        dispatch(logout());
        dispatch(clearTenantState());
        
        logService.warn('令牌無效，已清除身份驗證信息');

        
        return { 
          success: false, 
          accountData: null, 
          userData: null, 
          tenantId: null, 
          tenantInfo: null,
          error: new Error('invalid_token') 
        };
      }

      // 5. 獲取必要的狀態信息
      accountData = stateService.loginAccount();
      userData = stateService.loginUser();
      tenantId = stateService.tenantId();
      tenantInfo = stateService.loginTenant();

      // 6. 確保租戶 ID 已設置
      if (!tenantId && userData?.tenantId) {
        tenantId = userData.tenantId;
        stateService.setTenantId(tenantId);
        logService.info('從用戶信息中設置租戶 ID', { tenantId });
      }

      // 7. 檢查租戶 ID 是否有效
      if (!tenantId) {
        logService.warn('未找到有效的租戶 ID，初始化無法繼續');

        return { 
          success: false, 
          accountData, 
          userData, 
          tenantId: null, 
          tenantInfo,
          error: new Error('no_tenant_id') 
        };
      }
      // 9. 初始化數據庫
      if (!aileDBService.isInitialized()) {
        logService.info('開始初始化數據庫', { tenantId });
        
        // 使用正確的 accountId 參數名稱
        const accountId = userData?.accountId || accountData?.accountId;
        if (!accountId) {
          throw new Error('無法獲取有效的 accountId 進行數據庫初始化');
        }
        
        //同步用戶數據到本地
        userService.fetchUserList({
          pageSize: 100,
          refreshTime: 0,
          tenantId: tenantId
        });
        
        // 同步全局登入狀態和初始化推送服务
        stateService.notifyLoginChanged(LogInState.LoggedIn, accountData);
        pushService.initServices();

        logService.info('已通知登录状态变化并初始化推送服务', { accountId });


        await aileDBService.initForAccount(accountId);
        
        logService.info('數據庫初始化完成');
      } else {
        logService.info('數據庫已初始化，跳過此步驟');
      }   
      // 8. 調用 applyToken 設置租戶信息
      try {
        logService.info('應用租戶令牌', { tenantId });
        
        // 使用 Redux thunk 應用租戶令牌
        const resultAction = await options.dispatch(applyTenantToken(tenantId));
        const applyTokenResponse = resultAction.payload;
        
        if (resultAction.meta.requestStatus === 'fulfilled' && applyTokenResponse) {
          logService.info('應用初始化時成功獲取並應用租戶信息', {
            tenantId,
            tenantName: applyTokenResponse.tenantInfo?.name
          });
          
          // 確保獲取房間信息
          if (accountData) {
            const roomIds = [
              accountData.personRoomId, 
              accountData.systemRoomId
            ].filter(Boolean);
            
            if (roomIds.length > 0) {
              logService.info('初始化獲取房間信息', { roomIds });
              await Promise.all(roomIds.map(roomId => 
                roomId ? fetchRoomItem({ roomId }) : Promise.resolve()
              ));
            }
          }
        } else {
          const error = resultAction.payload || '應用租戶令牌失敗';
          logService.warn('應用初始化時未能獲取完整租戶信息', { 
            tenantId, 
            error
          });
        }
      } catch (error) {
        logService.error('應用初始化中租戶狀態設置失敗', { error: error as Error, tenantId });
        // 繼續執行，不中斷流程
      }

      

      // 10. 成功完成初始化

      logService.info('應用核心初始化成功', {
        hasAccountData: !!accountData,
        hasUserData: !!userData,
        hasTenantId: !!tenantId,
        hasTenantInfo: !!tenantInfo
      });

      return { 
        success: true, 
        accountData, 
        userData, 
        tenantId, 
        tenantInfo 
      };
    } catch (error) {
      logService.error('應用核心初始化過程出現異常', { error });

      return { 
        success: false, 
        accountData, 
        userData, 
        tenantId, 
        tenantInfo,
        error: error as Error
      };
    }
  }

  /**
   * 執行所有初始化任務（僅執行一次）
   */
  public async runOnce(): Promise<void> {
    if (this.initialized) {
      logService.info('App 已初始化過，跳過重複初始化');
      return;
    }
    this.initialized = true;
    
    try {
      logService.info('執行 App 啟動後初始化任務');
      
      // 後續執行同步初始化
      syncInitService.init().catch(e => {
        logService.error('後台同步任務失敗', { error: e });
      });
      
      // 如果有特殊的僅初始化一次的任務，可以放在這裡
      await this.initDefaultSettings();
      
      logService.info('App 初始化任務執行完成');
    } catch (e) {
      logService.error('App 初始化服務執行失敗', { error: e as Error });
    }
  }

  /**
   * 初始化默認設置
   */
  private async initDefaultSettings() {
    // 如有需要，在這裡添加初始化默認設置的邏輯
    // 與 syncInitService 不同，這裡應該僅執行一次性的初始化配置
  }

  /**
   * 檢查SQLite元素是否準備就緒
   * 注意：此方法保持不變，以不影響jeepSqliteElement相關邏輯
   */
  public async waitForSqliteReady(): Promise<void> {
    return new Promise<void>((resolve) => {
      const checkJeepSqlite = () => {
        const jeepSqliteElement = document.querySelector('jeep-sqlite');
        if (jeepSqliteElement && customElements.get('jeep-sqlite')) {
          resolve();
        } else {
          setTimeout(checkJeepSqlite, 100);
        }
      };
      checkJeepSqlite();
    });
  }

  /**
   * 測試SQLite元素功能
   * 注意：此方法保持不變，以不影響jeepSqliteElement相關邏輯
   */
  public async testSqliteElement(): Promise<boolean> {
    try {
      const jeepSqliteElement = document.querySelector('jeep-sqlite') as any;
      if (typeof jeepSqliteElement?.echo === 'function') {
        const result = await jeepSqliteElement.echo('test');
        logService.info('jeep-sqlite 元素響應測試', { result });
        return true;
      }
      return false;
    } catch (error) {
      logService.warn('jeep-sqlite 元素測試失敗，但繼續初始化', error);
      return false;
    }
  }
}

const initAppService = InitAppService.getInstance();
export default initAppService; 