import messageDao from './MessageDao';
import aileDBService from '../aileDBService';
import tenantService from '../../core/tenant/tenantService';
import { logService } from '../../system/logService';
import { getLocalStorage } from '@/utils/storage';
import { ConstantUtil } from '@/utils/constantUtil';

// Mock 依赖
jest.mock('../aileDBService', () => ({
  all: jest.fn()
}));

jest.mock('../../tenant/tenantService', () => ({
  getCurrentTenantId: jest.fn()
}));

jest.mock('../../system/logService', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

jest.mock('@/utils/storage', () => ({
  getLocalStorage: jest.fn()
}));

jest.mock('@/utils/constantUtil', () => ({
  CURRENT_TENANT_ID_KEY: 'CURRENT_TENANT_ID'
}));

describe('MessageDao', () => {
  // 每个测试之前重置所有 mock
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchMessagesFromDB', () => {
    it('should fetch messages with correct SQL when all parameters are provided', async () => {
      // 模拟 tenantService.getCurrentTenantId 返回值
      const mockTenantId = 'tenant-123';
      (tenantService.getCurrentTenantId as jest.Mock).mockReturnValue(mockTenantId);
      
      // 模拟数据库返回的消息
      const mockMessages = [
        { id: 'msg-1', roomId: 'room-1', senderId: 'user-1', content: 'Hello', sequence: 1, sendTime: 1640995200000 },
        { id: 'msg-2', roomId: 'room-1', senderId: 'user-2', content: 'Hi', sequence: 2, sendTime: 1640995201000 }
      ];
      (aileDBService.all as jest.Mock).mockResolvedValue(mockMessages);
      
      // 调用方法
      const result = await messageDao.fetchMessagesFromDB('room-1', 0, 20, 0, 'desc');
      
      // 验证结果
      expect(result).toEqual(mockMessages);
      expect(aileDBService.all).toHaveBeenCalledWith(
        `SELECT * FROM Messages WHERE roomId = ? AND tenantId = ? AND sequence < ? ORDER BY sequence DESC LIMIT ? OFFSET ?`,
        ['room-1', mockTenantId, 0, 20, 0]
      );
      expect(logService.info).toHaveBeenCalledWith(
        '從數據庫獲取消息列表',
        expect.objectContaining({
          roomId: 'room-1',
          page: 0,
          pageSize: 20,
          sequence: 0,
          sort: 'desc',
          count: 2
        })
      );
    });

    it('should fetch messages with correct SQL when sorting in ascending order', async () => {
      // 模拟 tenantService.getCurrentTenantId 返回值
      const mockTenantId = 'tenant-123';
      (tenantService.getCurrentTenantId as jest.Mock).mockReturnValue(mockTenantId);
      
      // 模拟数据库返回的消息
      const mockMessages = [
        { id: 'msg-1', roomId: 'room-1', senderId: 'user-1', content: 'Hello', sequence: 1, sendTime: 1640995200000 },
        { id: 'msg-2', roomId: 'room-1', senderId: 'user-2', content: 'Hi', sequence: 2, sendTime: 1640995201000 }
      ];
      (aileDBService.all as jest.Mock).mockResolvedValue(mockMessages);
      
      // 调用方法
      const result = await messageDao.fetchMessagesFromDB('room-1', 0, 20, 0, 'asc');
      
      // 验证结果
      expect(result).toEqual(mockMessages);
      expect(aileDBService.all).toHaveBeenCalledWith(
        `SELECT * FROM Messages WHERE roomId = ? AND tenantId = ? AND sequence > ? ORDER BY sequence ASC LIMIT ? OFFSET ?`,
        ['room-1', mockTenantId, 0, 20, 0]
      );
    });

    it('should fetch messages without sequence filter when sequence is undefined', async () => {
      // 模拟 tenantService.getCurrentTenantId 返回值
      const mockTenantId = 'tenant-123';
      (tenantService.getCurrentTenantId as jest.Mock).mockReturnValue(mockTenantId);
      
      // 模拟数据库返回的消息
      const mockMessages = [
        { id: 'msg-1', roomId: 'room-1', senderId: 'user-1', content: 'Hello', sequence: 1, sendTime: 1640995200000 },
        { id: 'msg-2', roomId: 'room-1', senderId: 'user-2', content: 'Hi', sequence: 2, sendTime: 1640995201000 }
      ];
      (aileDBService.all as jest.Mock).mockResolvedValue(mockMessages);
      
      // 调用方法，不传 sequence 参数
      const result = await messageDao.fetchMessagesFromDB('room-1', 0, 20);
      
      // 验证结果
      expect(result).toEqual(mockMessages);
      expect(aileDBService.all).toHaveBeenCalledWith(
        `SELECT * FROM Messages WHERE roomId = ? AND tenantId = ? ORDER BY sequence DESC LIMIT ? OFFSET ?`,
        ['room-1', mockTenantId, 20, 0]
      );
    });

    it('should use localStorage for tenantId when tenantService returns null', async () => {
      // 模拟 tenantService.getCurrentTenantId 返回 null
      (tenantService.getCurrentTenantId as jest.Mock).mockReturnValue(null);
      
      // 模拟 localStorage 返回的租户 ID
      const mockTenantId = 'tenant-456';
      (getLocalStorage as jest.Mock).mockReturnValue(mockTenantId);
      
      // 模拟数据库返回的消息
      const mockMessages = [
        { id: 'msg-1', roomId: 'room-1', senderId: 'user-1', content: 'Hello', sequence: 1, sendTime: 1640995200000 }
      ];
      (aileDBService.all as jest.Mock).mockResolvedValue(mockMessages);
      
      // 调用方法
      const result = await messageDao.fetchMessagesFromDB('room-1');
      
      // 验证结果
      expect(result).toEqual(mockMessages);
      expect(getLocalStorage).toHaveBeenCalledWith(ConstantUtil.CURRENT_TENANT_ID_KEY, '');
      expect(aileDBService.all).toHaveBeenCalledWith(
        `SELECT * FROM Messages WHERE roomId = ? AND tenantId = ? ORDER BY sequence DESC LIMIT ? OFFSET ?`,
        ['room-1', mockTenantId, 20, 0]
      );
    });

    it('should handle database errors and return empty array', async () => {
      // 模拟 tenantService.getCurrentTenantId 返回值
      const mockTenantId = 'tenant-123';
      (tenantService.getCurrentTenantId as jest.Mock).mockReturnValue(mockTenantId);
      
      // 模拟数据库抛出错误
      const mockError = new Error('Database error');
      (aileDBService.all as jest.Mock).mockRejectedValue(mockError);
      
      // 调用方法
      const result = await messageDao.fetchMessagesFromDB('room-1');
      
      // 验证结果
      expect(result).toEqual([]);
      expect(logService.error).toHaveBeenCalledWith(
        '從數據庫獲取消息列表失敗',
        expect.objectContaining({
          error: mockError,
          roomId: 'room-1'
        })
      );
    });

    it('should warn when no tenantId is available', async () => {
      // 模拟 tenantService.getCurrentTenantId 和 localStorage 都返回 null
      (tenantService.getCurrentTenantId as jest.Mock).mockReturnValue(null);
      (getLocalStorage as jest.Mock).mockReturnValue('');
      
      // 模拟数据库返回的消息
      const mockMessages = [
        { id: 'msg-1', roomId: 'room-1', senderId: 'user-1', content: 'Hello', sequence: 1, sendTime: 1640995200000 }
      ];
      (aileDBService.all as jest.Mock).mockResolvedValue(mockMessages);
      
      // 调用方法
      await messageDao.fetchMessagesFromDB('room-1');
      
      // 验证警告已记录
      expect(logService.warn).toHaveBeenCalledWith('fetchMessagesFromDB: 未獲取到當前租戶Id');
      expect(aileDBService.all).toHaveBeenCalledWith(
        `SELECT * FROM Messages WHERE roomId = ? ORDER BY sequence DESC LIMIT ? OFFSET ?`,
        ['room-1', 20, 0]
      );
    });
  });
}); 