/* 导入 Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
:root {
  /* 颜色变量 */
  --color-primary: #1677FF;
  --color-success: #00B578;
  --color-warning: #FF8F1F;
  --color-danger: #FF3141;
  --color-text-primary: #333333;
  --color-text-secondary: #666666;
  --color-text-tertiary: #999999;
  --color-border: #EEEEEE;
  --color-background: #F5F5F5;
  --color-line-green: #06C755;
}

/* 全局重置 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  color: var(--color-text-primary);
}

/* 添加安全区域支持 */
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}



/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 自定义 Antd Mobile 默认样式 */
.adm-button-primary {
  --adm-color-primary: var(--color-primary);
}

.adm-toast {
  --adm-color-white: #fff;
  --adm-font-size-7: 14px;
}

/* 确保页面铺满整个视口高度 */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 加载组件样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 加载动画 */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 应用容器样式 */
.app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 安全区域应用容器 */
.safe-area-app {
  /* 使用 env() 函数获取安全区域插入值 */
  padding-top: env(safe-area-inset-top, 0px);
  padding-bottom: env(safe-area-inset-bottom, 0px);
  padding-left: env(safe-area-inset-left, 0px);
  padding-right: env(safe-area-inset-right, 0px);

  /* 确保应用占满整个视口 */
  min-height: 100vh;
  min-height: calc(100vh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px));

  /* 支持刘海屏和圆角屏幕 */
  box-sizing: border-box;
}

/* 针对不同设备的安全区域优化 */
@supports (padding: max(0px)) {
  /* 移动端设备的安全区域优化 */
  @media (max-width: 768px) and (pointer: coarse) {
    .safe-area-app {
      /* 使用 max() 确保最小安全距离 */
      padding-top: max(env(safe-area-inset-top, 0px), 20px);
      padding-bottom: max(env(safe-area-inset-bottom, 0px), 0px);
      padding-left: max(env(safe-area-inset-left, 0px), 0px);
      padding-right: max(env(safe-area-inset-right, 0px), 0px);
    }
  }

  /* 桌面端设备不需要额外的安全距离 */
  @media (min-width: 769px) or (pointer: fine) {
    .safe-area-app {
      padding-top: env(safe-area-inset-top, 0px);
      padding-bottom: env(safe-area-inset-bottom, 0px);
      padding-left: env(safe-area-inset-left, 0px);
      padding-right: env(safe-area-inset-right, 0px);
    }
  }
}

/* 状态栏区域样式 */
.status-bar-safe-area {
  height: env(safe-area-inset-top, 0px);
  background-color: var(--status-bar-background, #ffffff);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  transition: background-color 0.3s ease;
}

/* 动态状态栏颜色变量 */
:root {
  --status-bar-background: #ffffff;
  --current-page-color: #ffffff;
}

/* 底部安全区域样式 */
.bottom-safe-area {
  height: env(safe-area-inset-bottom, 0px);
  background-color: var(--bottom-safe-area-background, #ffffff);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
}

/* jeep-sqlite元素样式 - 确保完全隐藏 */
jeep-sqlite {
  display: none !important;
  visibility: hidden !important;
  width: 0 !important;
  height: 0 !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
  opacity: 0 !important;
  pointer-events: none !important;
  overflow: hidden !important;
} 

/* 确保jeep-sqlite的子元素也隐藏 */
jeep-sqlite * {
  display: none !important;
  visibility: hidden !important;
}

/* 移動端下拉刷新指示器 */
.pull-refresh-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 20px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 25px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  font-size: 13px;
  color: #555;
  z-index: 1000;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: 500;
  white-space: nowrap;
}

.pull-refresh-spinner {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #1677ff;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: bold;
}

.pull-refresh-spinner.spinning {
  border: 2px solid #e8f4ff;
  border-top: 2px solid #1677ff;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  font-size: 0;
  background: rgba(22, 119, 255, 0.05);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 下拉刷新时的内容容器动画 */
.chat-content-pulling {
  transition: transform 0.1s ease-out;
}

.chat-content-releasing {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}