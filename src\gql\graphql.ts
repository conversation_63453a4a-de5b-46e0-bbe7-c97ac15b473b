/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  JSON: { input: any; output: any; }
  /** The `Upload` scalar type represents a file upload. */
  Upload: { input: any; output: any; }
};

export type AccountProfileData = {
  __typename?: 'AccountProfileData';
  accountId?: Maybe<Scalars['String']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  mobile?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['Float']['output']>;
};

export type AccountProfileResponse = {
  __typename?: 'AccountProfileResponse';
  code: Scalars['String']['output'];
  data?: Maybe<AccountProfileData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export type AgentStartRequest = {
  roomId: Scalars['String']['input'];
};

export type AgentStartResponse = {
  __typename?: 'AgentStartResponse';
  code: Scalars['String']['output'];
  data?: Maybe<SessionData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type AgentStopRequest = {
  roomId: Scalars['String']['input'];
};

export type AgentStopResponse = {
  __typename?: 'AgentStopResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Scalars['Boolean']['output']>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type AvatarBase64Response = {
  __typename?: 'AvatarBase64Response';
  code: Scalars['String']['output'];
  data?: Maybe<Scalars['String']['output']>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type BaseListMessageData = {
  __typename?: 'BaseListMessageData';
  count: Scalars['Int']['output'];
  hasNextPage: Scalars['Boolean']['output'];
  items: Array<Maybe<Message>>;
  lastSequence: Scalars['Int']['output'];
};

export type BaseListRoomData = {
  __typename?: 'BaseListRoomData';
  count?: Maybe<Scalars['Int']['output']>;
  hasNextPage?: Maybe<Scalars['Boolean']['output']>;
  items?: Maybe<Array<Maybe<RoomData>>>;
  refreshTime?: Maybe<Scalars['Float']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type BaseListRoomMember = {
  __typename?: 'BaseListRoomMember';
  count?: Maybe<Scalars['Int']['output']>;
  hasNextPage?: Maybe<Scalars['Boolean']['output']>;
  items?: Maybe<Array<Maybe<RoomMember>>>;
  refreshTime?: Maybe<Scalars['Float']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type BaseListTenantContact = {
  __typename?: 'BaseListTenantContact';
  count?: Maybe<Scalars['Int']['output']>;
  hasNextPage?: Maybe<Scalars['Boolean']['output']>;
  items?: Maybe<Array<TenantContact>>;
  refreshTime?: Maybe<Scalars['Float']['output']>;
  totalCount?: Maybe<Scalars['Int']['output']>;
};

export type BasePaginationInput = {
  direction: SortDirection;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy: Scalars['String']['input'];
  pageIndex: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  refreshTime?: InputMaybe<Scalars['Float']['input']>;
};

export type BetweenMessageRequest = {
  maxSequence: Scalars['Int']['input'];
  minSequence: Scalars['Int']['input'];
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  roomId: Scalars['String']['input'];
};

export enum BloodType {
  A = 'A',
  Ab = 'AB',
  B = 'B',
  O = 'O'
}

export enum Channel {
  Admin = 'Admin',
  Ai = 'Ai',
  Aile = 'Aile',
  AileLite = 'AileLite',
  Aiwow = 'Aiwow',
  Anonymous = 'Anonymous',
  Cbm = 'Cbm',
  Facebook = 'Facebook',
  Google = 'Google',
  Instagram = 'Instagram',
  Internal = 'Internal',
  Line = 'Line',
  Webchat = 'Webchat'
}

export type CheckLoginOtpResponse = {
  __typename?: 'CheckLoginOtpResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type CreateInvitationRequest = {
  serviceNumberId?: InputMaybe<Scalars['String']['input']>;
  tenantId?: InputMaybe<Scalars['String']['input']>;
  type?: InvitationType;
};

export type CreateInvitationResponse = {
  __typename?: 'CreateInvitationResponse';
  code: Scalars['String']['output'];
  data?: Maybe<InvitationData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type CreatePersonalTenantInput = {
  file?: InputMaybe<Scalars['Upload']['input']>;
  name: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreatePersonalTenantResponse = {
  __typename?: 'CreatePersonalTenantResponse';
  code: Scalars['String']['output'];
  data?: Maybe<TenantInfo>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export type DeviceCheckRequestInput = {
  onceToken: Scalars['String']['input'];
};

export type EmployeeProfileResponse = {
  __typename?: 'EmployeeProfileResponse';
  code: Scalars['String']['output'];
  data?: Maybe<TenantEmployee>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export type FindMessageRequest = {
  id: Scalars['String']['input'];
  roomId: Scalars['String']['input'];
};

export enum Gender {
  Female = 'Female',
  Male = 'Male',
  Other = 'Other',
  Unknown = 'Unknown'
}

export type GetAvatarBase64Input = {
  avatarId: Scalars['String']['input'];
  size: Scalars['String']['input'];
};

export type IdentityData = {
  __typename?: 'IdentityData';
  avatarId: Scalars['String']['output'];
  createTime: Scalars['Float']['output'];
  fbIdentityId?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isDefault: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  serviceNumberId: Scalars['String']['output'];
  tenantId: Scalars['String']['output'];
  type: ServiceIdentityType;
  updateTime: Scalars['Float']['output'];
};

export type IdentityListRequest = {
  serviceNumberId: Scalars['String']['input'];
};

export type IdentityListResponse = {
  __typename?: 'IdentityListResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Array<Maybe<IdentityData>>>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type InvitationData = {
  __typename?: 'InvitationData';
  code?: Maybe<Scalars['String']['output']>;
  text?: Maybe<Scalars['String']['output']>;
  type?: Maybe<InvitationType>;
};

export enum InvitationType {
  JoinServiceNumber = 'JoinServiceNumber',
  JoinTenant = 'JoinTenant'
}

export enum JoinType {
  Guarantor = 'Guarantor',
  Invitation = 'Invitation'
}

export enum Language {
  EnUs = 'EN_US',
  ZhCn = 'ZH_CN',
  ZhTw = 'ZH_TW'
}

export type LoginData = {
  __typename?: 'LoginData';
  accountId: Scalars['String']['output'];
  accountType: Scalars['String']['output'];
  countryCode?: Maybe<Scalars['String']['output']>;
  currentEmployeeInfo?: Maybe<TenantEmployee>;
  currentTenantInfo?: Maybe<TenantInfo>;
  isInitial: Scalars['Boolean']['output'];
  isMute?: Maybe<Scalars['Boolean']['output']>;
  lastTenantId?: Maybe<Scalars['String']['output']>;
  loginStatus?: Maybe<Scalars['String']['output']>;
  loginType: Scalars['String']['output'];
  mobile?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onlineId?: Maybe<Scalars['String']['output']>;
  personRoomId: Scalars['String']['output'];
  systemAccountId: Scalars['String']['output'];
  systemRoomId?: Maybe<Scalars['String']['output']>;
  tenantRelations?: Maybe<Array<TenantRelation>>;
  tokenId: Scalars['String']['output'];
};

export type LoginRequestInput = {
  accountId?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  checkCode?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  loginType: Scalars['String']['input'];
  mobile?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  onceToken?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  scopeId?: InputMaybe<Scalars['String']['input']>;
  thirdChannel?: InputMaybe<Channel>;
  useNonMobile?: InputMaybe<Scalars['Boolean']['input']>;
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<LoginData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type LoginWithOtpRequest = {
  checkCode: Scalars['String']['input'];
  onceToken: Scalars['String']['input'];
};

export type LoginWithThirdRequest = {
  scopeId: Scalars['String']['input'];
  thirdChannel: Channel;
};

export enum MaritalStatus {
  Divorced = 'Divorced',
  Married = 'Married',
  Single = 'Single',
  Widowed = 'Widowed'
}

export type MemberItemRequest = {
  memberId: Scalars['String']['input'];
  roomId: Scalars['String']['input'];
};

export type MemberItemResponse = {
  __typename?: 'MemberItemResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<RoomMember>;
  msg?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
  timeCost?: Maybe<Scalars['Float']['output']>;
};

export type MemberListRequest = {
  direction?: InputMaybe<Scalars['String']['input']>;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  pageIndex: Scalars['Int']['input'];
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime: Scalars['Float']['input'];
  roomId: Scalars['String']['input'];
};

export type MemberListResponse = {
  __typename?: 'MemberListResponse';
  code: Scalars['String']['output'];
  data?: Maybe<BaseListRoomMember>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type Message = {
  __typename?: 'Message';
  accountId: Scalars['String']['output'];
  appointChannel?: Maybe<Channel>;
  body: Scalars['String']['output'];
  channel: Channel;
  channelMessageId?: Maybe<Scalars['String']['output']>;
  content: Scalars['JSON']['output'];
  excludeMemberIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  flag?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  nearMessageId?: Maybe<Scalars['String']['output']>;
  osType?: Maybe<OsType>;
  recipientAccountId?: Maybe<Scalars['String']['output']>;
  recipientId: Scalars['String']['output'];
  roomId: Scalars['String']['output'];
  sendTime: Scalars['Float']['output'];
  senderId: Scalars['String']['output'];
  senderName?: Maybe<Scalars['String']['output']>;
  sequence: Scalars['Int']['output'];
  sessionId?: Maybe<Scalars['String']['output']>;
  sourceType: SourceType;
  tag?: Maybe<Scalars['JSON']['output']>;
  tenantId: Scalars['String']['output'];
  themeId?: Maybe<Scalars['String']['output']>;
  time: Scalars['String']['output'];
  title: Scalars['String']['output'];
  type: MessageType;
};

export type MessageListResponse = {
  __typename?: 'MessageListResponse';
  code: Scalars['String']['output'];
  data?: Maybe<BaseListMessageData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type MessageRequest = {
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  roomId: Scalars['String']['input'];
  sequence: Scalars['Int']['input'];
};

export type MessageResponse = {
  __typename?: 'MessageResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Message>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export enum MessageType {
  Action = 'Action',
  At = 'At',
  Audio = 'Audio',
  Event = 'Event',
  File = 'File',
  Image = 'Image',
  Json = 'Json',
  Location = 'Location',
  Sticker = 'Sticker',
  Template = 'Template',
  Text = 'Text',
  Video = 'Video',
  Voice = 'Voice'
}

export type Mutation = {
  __typename?: 'Mutation';
  _empty?: Maybe<Scalars['String']['output']>;
  agentStart: AgentStartResponse;
  agentStop: AgentStopResponse;
  checkLoginOtp: CheckLoginOtpResponse;
  createPersonalTenant: CreatePersonalTenantResponse;
  deviceCheck: LoginResponse;
  login: LoginResponse;
  loginWithOtp: LoginResponse;
  loginWithThird: LoginResponse;
  requestLoginOtp: OtpResponse;
  roomItem: RoomItemResponse;
  sendMessage: SendMessageResponse;
  switchIdentity: SwitchIdentityResponse;
  switchTenant: SwitchTenantResponse;
  transferCancel: TransferResponse;
  transferComplete: TransferResponse;
  transferMake: TransferResponse;
  transferRobotMake: TransferResponse;
  transferStart: TransferResponse;
  updateAccountProfile: AccountProfileResponse;
};


export type MutationAgentStartArgs = {
  request: AgentStartRequest;
};


export type MutationAgentStopArgs = {
  request: AgentStopRequest;
};


export type MutationCheckLoginOtpArgs = {
  request?: InputMaybe<CheckLoginOtpRequestInput>;
};


export type MutationCreatePersonalTenantArgs = {
  request: CreatePersonalTenantInput;
};


export type MutationDeviceCheckArgs = {
  request?: InputMaybe<DeviceCheckRequestInput>;
};


export type MutationLoginArgs = {
  request?: InputMaybe<LoginRequestInput>;
};


export type MutationLoginWithOtpArgs = {
  request: LoginWithOtpRequest;
};


export type MutationLoginWithThirdArgs = {
  request: LoginWithThirdRequest;
};


export type MutationRequestLoginOtpArgs = {
  request?: InputMaybe<OtpInput>;
};


export type MutationRoomItemArgs = {
  request: RoomItemRequest;
};


export type MutationSendMessageArgs = {
  request: SendMessageRequest;
};


export type MutationSwitchIdentityArgs = {
  request: SwitchIdentityRequest;
};


export type MutationSwitchTenantArgs = {
  tenantId: Scalars['String']['input'];
};


export type MutationTransferCancelArgs = {
  request: TransferCancelRequest;
};


export type MutationTransferCompleteArgs = {
  request: TransferCompleteRequest;
};


export type MutationTransferMakeArgs = {
  request: TransferMakeRequest;
};


export type MutationTransferRobotMakeArgs = {
  request: TransferRobotMakeRequest;
};


export type MutationTransferStartArgs = {
  request: TransferStartRequest;
};


export type MutationUpdateAccountProfileArgs = {
  input: UpdateAccountProfileInput;
};

export type OptionalPaginationInput = {
  direction?: InputMaybe<SortDirection>;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  pageIndex?: InputMaybe<Scalars['Int']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime?: InputMaybe<Scalars['Float']['input']>;
};

export enum OsType {
  Android = 'android',
  Ios = 'ios',
  Mac = 'mac',
  Pc = 'pc',
  Web = 'web',
  Windows = 'windows'
}

export type OtpData = {
  __typename?: 'OtpData';
  onceToken: Scalars['String']['output'];
  validSecond: Scalars['Int']['output'];
};

export type OtpInput = {
  countryCode: Scalars['String']['input'];
  mobile: Scalars['String']['input'];
};

export type OtpResponse = {
  __typename?: 'OtpResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<OtpData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export enum Privilege {
  Common = 'Common',
  Manager = 'Manager',
  Owner = 'Owner',
  Provisional = 'Provisional'
}

export type Query = {
  __typename?: 'Query';
  createInvitation: CreateInvitationResponse;
  employeeProfile: EmployeeProfileResponse;
  endRoomList: RoomListResponse;
  findMessage: MessageResponse;
  getAvatarBase64: AvatarBase64Response;
  getBetweenSquenceMessages: MessageListResponse;
  getLessThanSquenceMessages: MessageListResponse;
  getMoreThanSquenceMessages: MessageListResponse;
  identityList: IdentityListResponse;
  memberItem: MemberItemResponse;
  memberList: MemberListResponse;
  robotRoomList: RoomListResponse;
  tenantContactDetail: TenantContactDetailResponse;
  tenantContactList: TenantContactListResponse;
  tenantRelations: TenantRelationsResponse;
  transferList: TransferListResponse;
  verifyInvitationCode: VerifyInvitationCodeResponse;
};


export type QueryCreateInvitationArgs = {
  request: CreateInvitationRequest;
};


export type QueryEndRoomListArgs = {
  request: RoomListRequest;
};


export type QueryFindMessageArgs = {
  request: FindMessageRequest;
};


export type QueryGetAvatarBase64Args = {
  request: GetAvatarBase64Input;
};


export type QueryGetBetweenSquenceMessagesArgs = {
  request?: InputMaybe<BetweenMessageRequest>;
};


export type QueryGetLessThanSquenceMessagesArgs = {
  request?: InputMaybe<MessageRequest>;
};


export type QueryGetMoreThanSquenceMessagesArgs = {
  request?: InputMaybe<MessageRequest>;
};


export type QueryIdentityListArgs = {
  request: IdentityListRequest;
};


export type QueryMemberItemArgs = {
  request: MemberItemRequest;
};


export type QueryMemberListArgs = {
  request: MemberListRequest;
};


export type QueryRobotRoomListArgs = {
  request: RoomListRequest;
};


export type QueryTenantContactDetailArgs = {
  request: TenantContactDetailRequest;
};


export type QueryTenantContactListArgs = {
  request: TenantContactListRequest;
};


export type QueryTenantRelationsArgs = {
  request?: InputMaybe<TenantRelationsRequest>;
};


export type QueryTransferListArgs = {
  request: TransferListRequest;
};


export type QueryVerifyInvitationCodeArgs = {
  request: VerifyInvitationCodeRequest;
};

export type RoomData = {
  __typename?: 'RoomData';
  accountId?: Maybe<Scalars['String']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  businessDescription?: Maybe<Scalars['String']['output']>;
  businessEndTime?: Maybe<Scalars['Float']['output']>;
  businessId?: Maybe<Scalars['String']['output']>;
  businessName?: Maybe<Scalars['String']['output']>;
  businessStatus?: Maybe<Scalars['String']['output']>;
  createTime?: Maybe<Scalars['Float']['output']>;
  homePagePicId?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  isCustomName?: Maybe<Scalars['Boolean']['output']>;
  isExternal?: Maybe<Scalars['Boolean']['output']>;
  lastMessage?: Maybe<Scalars['JSON']['output']>;
  lastSequence?: Maybe<Scalars['Int']['output']>;
  mainRoomId?: Maybe<Scalars['String']['output']>;
  members?: Maybe<Array<Maybe<RoomMember>>>;
  name?: Maybe<Scalars['String']['output']>;
  ownerId?: Maybe<Scalars['String']['output']>;
  serviceNumberId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<RoomStatus>;
  tenantId?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['Float']['output']>;
};

export type RoomItemRequest = {
  roomId: Scalars['String']['input'];
};

export type RoomItemResponse = {
  __typename?: 'RoomItemResponse';
  code: Scalars['String']['output'];
  data?: Maybe<RoomData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type RoomListRequest = {
  pageIndex: Scalars['Int']['input'];
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime: Scalars['Float']['input'];
  serviceNumberId?: InputMaybe<Scalars['String']['input']>;
};

export type RoomListResponse = {
  __typename?: 'RoomListResponse';
  code: Scalars['String']['output'];
  data?: Maybe<BaseListRoomData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type RoomMember = {
  __typename?: 'RoomMember';
  accountId?: Maybe<Scalars['String']['output']>;
  createTime?: Maybe<Scalars['Float']['output']>;
  dfrTime?: Maybe<Scalars['Float']['output']>;
  firstSequence?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  lastMessage?: Maybe<Message>;
  lastReadSequence?: Maybe<Scalars['Int']['output']>;
  lastReceivedSequence?: Maybe<Scalars['Int']['output']>;
  mainRoomId?: Maybe<Scalars['String']['output']>;
  memberId?: Maybe<Scalars['String']['output']>;
  mute?: Maybe<Scalars['Boolean']['output']>;
  privilege?: Maybe<Privilege>;
  roomId?: Maybe<Scalars['String']['output']>;
  roomType?: Maybe<Scalars['String']['output']>;
  serviceNumberId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<RoomMemberStatus>;
  tenantId?: Maybe<Scalars['String']['output']>;
  top?: Maybe<Scalars['Boolean']['output']>;
  topTime?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<RoomMemberType>;
  updateTime?: Maybe<Scalars['Float']['output']>;
  userInfo?: Maybe<UserInfo>;
};

export enum RoomMemberStatus {
  Block = 'Block',
  Complaint = 'Complaint',
  Danger = 'Danger',
  Delete = 'Delete',
  Disable = 'Disable',
  Enable = 'Enable',
  Forbid = 'Forbid'
}

export enum RoomMemberType {
  Robot = 'Robot',
  ServiceNumber = 'ServiceNumber',
  User = 'User'
}

export enum RoomStatus {
  Block = 'Block',
  Complaint = 'Complaint',
  Danger = 'Danger',
  Delete = 'Delete',
  Disable = 'Disable',
  Enable = 'Enable',
  Forbid = 'Forbid'
}

export type SendMessageRequest = {
  content: Scalars['JSON']['input'];
  id?: InputMaybe<Scalars['String']['input']>;
  roomId: Scalars['String']['input'];
  tag?: InputMaybe<Scalars['JSON']['input']>;
  type: MessageType;
};

export type SendMessageResponse = {
  __typename?: 'SendMessageResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Message>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export enum ServiceIdentityType {
  Assistant = 'Assistant',
  Boss = 'Boss',
  Robot = 'Robot',
  ServiceNumber = 'ServiceNumber'
}

export type ServiceNumberSimpleData = {
  __typename?: 'ServiceNumberSimpleData';
  accountId: Scalars['String']['output'];
  avatarId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  ownerId: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export enum ServiceNumberType {
  Boss = 'Boss',
  Manage = 'Manage',
  Normal = 'Normal',
  Official = 'Official',
  Professional = 'Professional'
}

export type SessionData = {
  __typename?: 'SessionData';
  accountId?: Maybe<Scalars['String']['output']>;
  activeTime?: Maybe<Scalars['Float']['output']>;
  agentFirstMessageTime?: Maybe<Scalars['Float']['output']>;
  agentId?: Maybe<Scalars['String']['output']>;
  agentLastMessageTime?: Maybe<Scalars['Float']['output']>;
  appointChannel?: Maybe<Scalars['String']['output']>;
  channel?: Maybe<Channel>;
  code?: Maybe<Scalars['String']['output']>;
  createTime?: Maybe<Scalars['Float']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  customerLastMessageChannel?: Maybe<Channel>;
  customerLastMessageTime?: Maybe<Scalars['Float']['output']>;
  customerName?: Maybe<Scalars['String']['output']>;
  distributeTime?: Maybe<Scalars['Float']['output']>;
  endTime?: Maybe<Scalars['Float']['output']>;
  gwSessionId?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  idleTime?: Maybe<Scalars['Int']['output']>;
  isOwnerStop?: Maybe<Scalars['Boolean']['output']>;
  ownerAutoStopTime?: Maybe<Scalars['Float']['output']>;
  ownerAutoTransferTime?: Maybe<Scalars['Int']['output']>;
  ownerId?: Maybe<Scalars['String']['output']>;
  ownerStopTime?: Maybe<Scalars['Float']['output']>;
  robotEndTime?: Maybe<Scalars['Float']['output']>;
  robotTimeoutTime?: Maybe<Scalars['Int']['output']>;
  roomId?: Maybe<Scalars['String']['output']>;
  scopeId?: Maybe<Scalars['String']['output']>;
  serviceNumberId?: Maybe<Scalars['String']['output']>;
  serviceNumberType?: Maybe<ServiceNumberType>;
  serviceTime?: Maybe<Scalars['Float']['output']>;
  startTime?: Maybe<Scalars['Float']['output']>;
  status?: Maybe<SessionStatus>;
  tenantId?: Maybe<Scalars['String']['output']>;
  timeoutTime?: Maybe<Scalars['Int']['output']>;
  updateTime?: Maybe<Scalars['Float']['output']>;
  warned?: Maybe<Scalars['Boolean']['output']>;
  warningType?: Maybe<Scalars['String']['output']>;
};

export enum SessionStatus {
  AgentActive = 'AgentActive',
  AgentStop = 'AgentStop',
  CustomerStop = 'CustomerStop',
  DistributeActive = 'DistributeActive',
  RobotActive = 'RobotActive',
  RobotStop = 'RobotStop',
  Timeout = 'Timeout'
}

export enum SortDirection {
  Asc = 'ASC',
  Desc = 'DESC'
}

export enum SourceType {
  Broadcast = 'Broadcast',
  Consult = 'Consult',
  System = 'System',
  User = 'User'
}

export enum Status {
  Audit = 'Audit',
  Delete = 'Delete',
  Disable = 'Disable',
  Enable = 'Enable',
  New = 'New'
}

export type Subscription = {
  __typename?: 'Subscription';
  message?: Maybe<Message>;
};

export type SwitchIdentityRequest = {
  identityId: Scalars['String']['input'];
  serviceNumberId: Scalars['String']['input'];
};

export type SwitchIdentityResponse = {
  __typename?: 'SwitchIdentityResponse';
  code: Scalars['String']['output'];
  data?: Maybe<IdentityData>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type SwitchTenantResponse = {
  __typename?: 'SwitchTenantResponse';
  code: Scalars['String']['output'];
  data?: Maybe<TenantInfo>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type TenantContact = {
  __typename?: 'TenantContact';
  accountId?: Maybe<Scalars['String']['output']>;
  age?: Maybe<Scalars['Int']['output']>;
  alias?: Maybe<Scalars['String']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  birthday?: Maybe<Scalars['String']['output']>;
  bloodType?: Maybe<BloodType>;
  businessCardId?: Maybe<Scalars['String']['output']>;
  channel?: Maybe<Channel>;
  company?: Maybe<Scalars['String']['output']>;
  companyAddress?: Maybe<Scalars['String']['output']>;
  companyDepartment?: Maybe<Scalars['String']['output']>;
  companyDuty?: Maybe<Scalars['String']['output']>;
  companyEmail?: Maybe<Scalars['String']['output']>;
  companyPhone?: Maybe<Scalars['String']['output']>;
  createTime?: Maybe<Scalars['Float']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Gender>;
  id?: Maybe<Scalars['String']['output']>;
  interests?: Maybe<Scalars['String']['output']>;
  isAileCompany?: Maybe<Scalars['Boolean']['output']>;
  isBindAile?: Maybe<Scalars['Boolean']['output']>;
  isCollectInfo?: Maybe<Scalars['Boolean']['output']>;
  isJoinAile?: Maybe<Scalars['Boolean']['output']>;
  languages?: Maybe<Array<Language>>;
  maritalStatus?: Maybe<MaritalStatus>;
  name?: Maybe<Scalars['String']['output']>;
  openId?: Maybe<Scalars['String']['output']>;
  parentAddressBookId?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Status>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['Float']['output']>;
};

export type TenantContactDetailRequest = {
  id: Scalars['String']['input'];
};

export type TenantContactDetailResponse = {
  __typename?: 'TenantContactDetailResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<TenantContact>;
  msg?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type TenantContactListRequest = {
  direction?: InputMaybe<Scalars['String']['input']>;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime: Scalars['Float']['input'];
  serviceNumberId?: InputMaybe<Scalars['String']['input']>;
};

export type TenantContactListResponse = {
  __typename?: 'TenantContactListResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<BaseListTenantContact>;
  msg?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type TenantEmployee = {
  __typename?: 'TenantEmployee';
  accountId: Scalars['String']['output'];
  age?: Maybe<Scalars['Int']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  birthday?: Maybe<Scalars['Float']['output']>;
  channel: Channel;
  createTime: Scalars['Float']['output'];
  gender?: Maybe<Gender>;
  homePagePicId?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isBindAile?: Maybe<Scalars['Boolean']['output']>;
  isCollectInfo?: Maybe<Scalars['Boolean']['output']>;
  isJoinAile?: Maybe<Scalars['Boolean']['output']>;
  joinType: JoinType;
  mood?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  openId?: Maybe<Scalars['String']['output']>;
  personRoomId: Scalars['String']['output'];
  status: Status;
  tenantId: Scalars['String']['output'];
  updateTime: Scalars['Float']['output'];
};

export type TenantInfo = {
  __typename?: 'TenantInfo';
  accountId: Scalars['String']['output'];
  address?: Maybe<Scalars['String']['output']>;
  avatarId: Scalars['String']['output'];
  certificateFailReason?: Maybe<Scalars['String']['output']>;
  certificateFileId?: Maybe<Scalars['String']['output']>;
  certificateStatus?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  createTime: Scalars['Float']['output'];
  description?: Maybe<Scalars['String']['output']>;
  employeeInfo?: Maybe<TenantEmployee>;
  endTime?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  industry?: Maybe<Scalars['String']['output']>;
  industrySub?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  representativeNumber?: Maybe<Scalars['String']['output']>;
  scale?: Maybe<TenantScale>;
  shortName: Scalars['String']['output'];
  startTime?: Maybe<Scalars['Float']['output']>;
  switchTime: Scalars['Float']['output'];
  type: TenantType;
  unifiedNumber?: Maybe<Scalars['String']['output']>;
  updateTime: Scalars['Float']['output'];
  upgrade?: Maybe<Scalars['String']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type TenantRelation = {
  __typename?: 'TenantRelation';
  accountId: Scalars['String']['output'];
  avatarId: Scalars['String']['output'];
  bossServiceNumber?: Maybe<ServiceNumberSimpleData>;
  code: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  industry?: Maybe<Scalars['String']['output']>;
  industrySub?: Maybe<Scalars['String']['output']>;
  isLastTenant?: Maybe<Scalars['Boolean']['output']>;
  joinTime: Scalars['Float']['output'];
  manageServiceNumber: ServiceNumberSimpleData;
  manageServiceNumberId: Scalars['String']['output'];
  name: Scalars['String']['output'];
  officialServiceNumber: ServiceNumberSimpleData;
  officialServiceNumberId: Scalars['String']['output'];
  openId: Scalars['String']['output'];
  relationId: Scalars['String']['output'];
  scale?: Maybe<TenantScale>;
  shortName: Scalars['String']['output'];
  type: TenantType;
  unReadCount: Scalars['Int']['output'];
};

export type TenantRelationsRequest = {
  accountId?: InputMaybe<Scalars['String']['input']>;
  channel?: InputMaybe<Channel>;
  direction?: InputMaybe<SortDirection>;
  key?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Scalars['String']['input']>;
  pageIndex?: InputMaybe<Scalars['Int']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  refreshTime?: InputMaybe<Scalars['Float']['input']>;
};

export type TenantRelationsResponse = {
  __typename?: 'TenantRelationsResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Array<TenantRelation>>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost: Scalars['Int']['output'];
};

export enum TenantScale {
  Enterprise = 'Enterprise',
  Middle = 'Middle',
  Small = 'Small'
}

export enum TenantType {
  Common = 'Common',
  Official = 'Official',
  Person = 'Person',
  Public = 'Public',
  Service = 'Service'
}

export type TransferCancelRequest = {
  roomId: Scalars['String']['input'];
};

export type TransferCompleteRequest = {
  roomId: Scalars['String']['input'];
};

export type TransferItem = {
  __typename?: 'TransferItem';
  acceptorId?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  roomId?: Maybe<Scalars['String']['output']>;
  sponsorId?: Maybe<Scalars['String']['output']>;
};

export type TransferListRequest = {
  serviceNumberId: Scalars['String']['input'];
};

export type TransferListResponse = {
  __typename?: 'TransferListResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Array<Maybe<TransferItem>>>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type TransferMakeRequest = {
  roomId: Scalars['String']['input'];
};

export type TransferResponse = {
  __typename?: 'TransferResponse';
  code: Scalars['String']['output'];
  data?: Maybe<Scalars['Boolean']['output']>;
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
  timeCost?: Maybe<Scalars['Int']['output']>;
};

export type TransferRobotMakeRequest = {
  roomId: Scalars['String']['input'];
};

export type TransferStartRequest = {
  reason: Scalars['String']['input'];
  roomId: Scalars['String']['input'];
};

export type UpdateAccountProfileInput = {
  file?: InputMaybe<Scalars['Upload']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UserInfo = {
  __typename?: 'UserInfo';
  accountId?: Maybe<Scalars['String']['output']>;
  avatarId?: Maybe<Scalars['String']['output']>;
  channel?: Maybe<Channel>;
  createTime?: Maybe<Scalars['Float']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  isBindAile?: Maybe<Scalars['Boolean']['output']>;
  isCollectInfo?: Maybe<Scalars['Boolean']['output']>;
  isJoinAile?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  openId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Status>;
  tenantId?: Maybe<Scalars['String']['output']>;
  updateTime?: Maybe<Scalars['Float']['output']>;
};

export type VerifyInvitationCodeRequest = {
  code: Scalars['String']['input'];
};

export type VerifyInvitationCodeResponse = {
  __typename?: 'VerifyInvitationCodeResponse';
  code: Scalars['String']['output'];
  data: Scalars['Boolean']['output'];
  msg: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  success: Scalars['Boolean']['output'];
};

export type CheckLoginOtpRequestInput = {
  checkCode: Scalars['String']['input'];
  onceToken: Scalars['String']['input'];
};

export type RequestLoginOtpMutationVariables = Exact<{
  requestLoginOtpRequest2?: InputMaybe<OtpInput>;
}>;


export type RequestLoginOtpMutation = { __typename?: 'Mutation', requestLoginOtp: { __typename?: 'OtpResponse', code?: string | null, status: number, msg: string, success: boolean, timeCost?: number | null, data?: { __typename?: 'OtpData', onceToken: string, validSecond: number } | null } };

export type LoginWithOtpMutationVariables = Exact<{
  request: LoginWithOtpRequest;
}>;


export type LoginWithOtpMutation = { __typename?: 'Mutation', loginWithOtp: { __typename?: 'LoginResponse', code?: string | null, msg: string, status: number, success: boolean, timeCost?: number | null, data?: { __typename?: 'LoginData', name?: string | null, accountId: string, countryCode?: string | null, mobile?: string | null, tokenId: string, loginType: string, onlineId?: string | null, systemRoomId?: string | null, personRoomId: string, lastTenantId?: string | null, systemAccountId: string, loginStatus?: string | null, isMute?: boolean | null, accountType: string, isInitial: boolean, tenantRelations?: Array<{ __typename?: 'TenantRelation', type: TenantType, id: string, description?: string | null, industry?: string | null, industrySub?: string | null, scale?: TenantScale | null, code: string, name: string, shortName: string, avatarId: string, isLastTenant?: boolean | null, accountId: string, relationId: string, openId: string, unReadCount: number, officialServiceNumberId: string, manageServiceNumberId: string, joinTime: number, manageServiceNumber: { __typename?: 'ServiceNumberSimpleData', id: string, name: string, avatarId: string, type: string, ownerId: string, accountId: string }, officialServiceNumber: { __typename?: 'ServiceNumberSimpleData', id: string, name: string, avatarId: string, type: string, ownerId: string, accountId: string } }> | null, currentTenantInfo?: { __typename?: 'TenantInfo', id: string, createTime: number, updateTime: number, name: string, shortName: string, code: string, description?: string | null, type: TenantType, startTime?: number | null, endTime?: number | null, industry?: string | null, industrySub?: string | null, scale?: TenantScale | null, city?: string | null, address?: string | null, phone?: string | null, website?: string | null, avatarId: string, accountId: string, upgrade?: string | null, unifiedNumber?: string | null, representativeNumber?: string | null, certificateFileId?: string | null, certificateStatus?: string | null, certificateFailReason?: string | null, switchTime: number, employeeInfo?: { __typename?: 'TenantEmployee', id: string, createTime: number, updateTime: number, name: string, avatarId?: string | null, mood?: string | null, age?: number | null, gender?: Gender | null, birthday?: number | null, status: Status, accountId: string, tenantId: string, channel: Channel, personRoomId: string, joinType: JoinType, openId?: string | null, isJoinAile?: boolean | null, isBindAile?: boolean | null, isCollectInfo?: boolean | null, homePagePicId?: string | null } | null } | null, currentEmployeeInfo?: { __typename?: 'TenantEmployee', id: string, createTime: number, updateTime: number, name: string, avatarId?: string | null, mood?: string | null, age?: number | null, gender?: Gender | null, birthday?: number | null, status: Status, accountId: string, tenantId: string, channel: Channel, personRoomId: string, joinType: JoinType, openId?: string | null, isJoinAile?: boolean | null, isBindAile?: boolean | null, isCollectInfo?: boolean | null, homePagePicId?: string | null } | null } | null } };


export const RequestLoginOtpDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RequestLoginOtp"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"requestLoginOtpRequest2"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"OtpInput"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"requestLoginOtp"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"request"},"value":{"kind":"Variable","name":{"kind":"Name","value":"requestLoginOtpRequest2"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"onceToken"}},{"kind":"Field","name":{"kind":"Name","value":"validSecond"}}]}},{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"msg"}},{"kind":"Field","name":{"kind":"Name","value":"success"}},{"kind":"Field","name":{"kind":"Name","value":"timeCost"}}]}}]}}]} as unknown as DocumentNode<RequestLoginOtpMutation, RequestLoginOtpMutationVariables>;
export const LoginWithOtpDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"LoginWithOtp"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"request"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"LoginWithOtpRequest"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"loginWithOtp"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"request"},"value":{"kind":"Variable","name":{"kind":"Name","value":"request"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"accountId"}},{"kind":"Field","name":{"kind":"Name","value":"countryCode"}},{"kind":"Field","name":{"kind":"Name","value":"mobile"}},{"kind":"Field","name":{"kind":"Name","value":"tokenId"}},{"kind":"Field","name":{"kind":"Name","value":"loginType"}},{"kind":"Field","name":{"kind":"Name","value":"onlineId"}},{"kind":"Field","name":{"kind":"Name","value":"systemRoomId"}},{"kind":"Field","name":{"kind":"Name","value":"personRoomId"}},{"kind":"Field","name":{"kind":"Name","value":"lastTenantId"}},{"kind":"Field","name":{"kind":"Name","value":"systemAccountId"}},{"kind":"Field","name":{"kind":"Name","value":"loginStatus"}},{"kind":"Field","name":{"kind":"Name","value":"isMute"}},{"kind":"Field","name":{"kind":"Name","value":"accountType"}},{"kind":"Field","name":{"kind":"Name","value":"isInitial"}},{"kind":"Field","name":{"kind":"Name","value":"tenantRelations"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"industry"}},{"kind":"Field","name":{"kind":"Name","value":"industrySub"}},{"kind":"Field","name":{"kind":"Name","value":"scale"}},{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"shortName"}},{"kind":"Field","name":{"kind":"Name","value":"avatarId"}},{"kind":"Field","name":{"kind":"Name","value":"isLastTenant"}},{"kind":"Field","name":{"kind":"Name","value":"accountId"}},{"kind":"Field","name":{"kind":"Name","value":"relationId"}},{"kind":"Field","name":{"kind":"Name","value":"openId"}},{"kind":"Field","name":{"kind":"Name","value":"unReadCount"}},{"kind":"Field","name":{"kind":"Name","value":"officialServiceNumberId"}},{"kind":"Field","name":{"kind":"Name","value":"manageServiceNumberId"}},{"kind":"Field","name":{"kind":"Name","value":"joinTime"}},{"kind":"Field","name":{"kind":"Name","value":"manageServiceNumber"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"avatarId"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"ownerId"}},{"kind":"Field","name":{"kind":"Name","value":"accountId"}}]}},{"kind":"Field","name":{"kind":"Name","value":"officialServiceNumber"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"avatarId"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"ownerId"}},{"kind":"Field","name":{"kind":"Name","value":"accountId"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"currentTenantInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"createTime"}},{"kind":"Field","name":{"kind":"Name","value":"updateTime"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"shortName"}},{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"startTime"}},{"kind":"Field","name":{"kind":"Name","value":"endTime"}},{"kind":"Field","name":{"kind":"Name","value":"industry"}},{"kind":"Field","name":{"kind":"Name","value":"industrySub"}},{"kind":"Field","name":{"kind":"Name","value":"scale"}},{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone"}},{"kind":"Field","name":{"kind":"Name","value":"website"}},{"kind":"Field","name":{"kind":"Name","value":"avatarId"}},{"kind":"Field","name":{"kind":"Name","value":"accountId"}},{"kind":"Field","name":{"kind":"Name","value":"upgrade"}},{"kind":"Field","name":{"kind":"Name","value":"unifiedNumber"}},{"kind":"Field","name":{"kind":"Name","value":"representativeNumber"}},{"kind":"Field","name":{"kind":"Name","value":"certificateFileId"}},{"kind":"Field","name":{"kind":"Name","value":"certificateStatus"}},{"kind":"Field","name":{"kind":"Name","value":"certificateFailReason"}},{"kind":"Field","name":{"kind":"Name","value":"switchTime"}},{"kind":"Field","name":{"kind":"Name","value":"employeeInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"createTime"}},{"kind":"Field","name":{"kind":"Name","value":"updateTime"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"avatarId"}},{"kind":"Field","name":{"kind":"Name","value":"mood"}},{"kind":"Field","name":{"kind":"Name","value":"age"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"birthday"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"accountId"}},{"kind":"Field","name":{"kind":"Name","value":"tenantId"}},{"kind":"Field","name":{"kind":"Name","value":"channel"}},{"kind":"Field","name":{"kind":"Name","value":"personRoomId"}},{"kind":"Field","name":{"kind":"Name","value":"joinType"}},{"kind":"Field","name":{"kind":"Name","value":"openId"}},{"kind":"Field","name":{"kind":"Name","value":"isJoinAile"}},{"kind":"Field","name":{"kind":"Name","value":"isBindAile"}},{"kind":"Field","name":{"kind":"Name","value":"isCollectInfo"}},{"kind":"Field","name":{"kind":"Name","value":"homePagePicId"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"currentEmployeeInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"createTime"}},{"kind":"Field","name":{"kind":"Name","value":"updateTime"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"avatarId"}},{"kind":"Field","name":{"kind":"Name","value":"mood"}},{"kind":"Field","name":{"kind":"Name","value":"age"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"birthday"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"accountId"}},{"kind":"Field","name":{"kind":"Name","value":"tenantId"}},{"kind":"Field","name":{"kind":"Name","value":"channel"}},{"kind":"Field","name":{"kind":"Name","value":"personRoomId"}},{"kind":"Field","name":{"kind":"Name","value":"joinType"}},{"kind":"Field","name":{"kind":"Name","value":"openId"}},{"kind":"Field","name":{"kind":"Name","value":"isJoinAile"}},{"kind":"Field","name":{"kind":"Name","value":"isBindAile"}},{"kind":"Field","name":{"kind":"Name","value":"isCollectInfo"}},{"kind":"Field","name":{"kind":"Name","value":"homePagePicId"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"msg"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"success"}},{"kind":"Field","name":{"kind":"Name","value":"timeCost"}}]}}]}}]} as unknown as DocumentNode<LoginWithOtpMutation, LoginWithOtpMutationVariables>;