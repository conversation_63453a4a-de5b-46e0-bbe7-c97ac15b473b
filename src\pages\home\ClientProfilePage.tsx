import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Tag } from 'antd-mobile';
import './ClientProfilePage.css';

// Import icons and images
import closeIcon from '../../assets/images/client/close-icon.svg';
import editIcon from '../../assets/images/client/edit-icon.svg';
import starIcon from '../../assets/images/client/star-icon.svg';
import addressBookIcon from '../../assets/images/client/address-book-icon.svg';
import messageIcon from '../../assets/images/client/message-icon.svg';


// Import avatar images
import aliceAvatar from '../../assets/images/avatars/alice_avatar.png';
import johnAvatar from '../../assets/images/avatars/john_avatar.png';
import jamesAvatar from '../../assets/images/avatars/james_avatar.png';
import graceAvatar from '../../assets/images/avatars/grace-avatar.png';
import peterAvatar from '../../assets/images/avatars/peter-avatar.png';
import jackAvatar from '../../assets/images/avatars/jack-avatar.png';
import alexAvatar from '../../assets/images/avatars/alex-avatar.png';
import userAvatar from '../../assets/images/avatars/user-avatar.png';

interface ClientAvatarMap {
  [key: string]: string;
}

const ClientProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { clientId } = useParams<{ clientId: string }>();
  
  // Mock client data for demonstration
  const clientAvatars: ClientAvatarMap = {
    '1': aliceAvatar,
    '2': johnAvatar,
    '3': jamesAvatar,
    '19': graceAvatar,
    '25': jackAvatar,
    '38': peterAvatar,
    '6': alexAvatar
  };
  
  const clientNames: { [key: string]: string } = {
    '1': 'Alice Chang',
    '2': 'John Dukes',
    '3': 'James Hall',
    '19': 'Grace Chen',
    '25': 'Jack Hsu',
    '38': 'Peter Wang',
    '6': 'Alex Hamilton'
  };
  
  const handleClose = () => {
    navigate(-1);
  };
  
  // Default to first client if ID not found
  const clientName = clientId ? clientNames[clientId] || 'User' : 'User';
  const avatarSrc = clientId ? clientAvatars[clientId] || userAvatar : userAvatar;
  
  return (
    <div className="client-profile-page">
      <div className="client-profile-header">
        <div className="client-profile-navbar">
          <div className="client-profile-navbar-right">
            <img 
              src={closeIcon} 
              alt="Close" 
              className="client-profile-close-icon" 
              onClick={handleClose}
            />
          </div>
        </div>
        
        <div className="client-profile-avatar-container">
          <div className="client-profile-avatar">
            <img src={avatarSrc} alt={clientName} />
          </div>
          
          <div className="client-profile-name-container">
            <span className="client-profile-name">{clientName}</span>
            <img src={editIcon} alt="Edit" className="client-profile-edit-icon" />
          </div>
        </div>
        
        <div className="client-profile-action-buttons">
          <div className="action-button">
            <img src={starIcon} alt="Favorite" />
          </div>
          <div className="action-button">
            <img src={addressBookIcon} alt="Address Book" />
          </div>
          <div className="action-button">
            <img src={messageIcon} alt="Message" />
          </div>
        </div>
      </div>
      
      <div className="client-profile-form">
        <div className="client-profile-form-item">
          <div className="form-item-content">
            <div className="form-item-main">
              <div className="form-item-title">標籤</div>
              <div className="form-item-selector">
                <Tag color="primary" fill="solid" className="client-tag">回頭客</Tag>
                <Tag color="primary" fill="solid" className="client-tag">VIP</Tag>
                <Tag color="primary" fill="solid" className="client-tag">APP購物</Tag>
              </div>
            </div>
          </div>
        </div>
        
        <div className="client-profile-form-item">
          <div className="form-item-content">
            <div className="form-item-main">
              <div className="form-item-title">備註</div>
              <div className="form-item-note">
                2000/06/21
              </div>
            </div>
          </div>
        </div>
        
        <div className="client-profile-dropdown">
          <div className="dropdown-main">
            <div className="dropdown-title">關聯任務</div>
            <div className="dropdown-arrow">
              <svg width="12.67" height="7.48" viewBox="0 0 12.67 7.48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.5 1.5L6.33 6.33L11.17 1.5" stroke="#CCCCCC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
        
        <div className="client-profile-dropdown">
          <div className="dropdown-main">
            <div className="dropdown-title">備忘錄</div>
            <div className="dropdown-arrow">
              <svg width="12.67" height="7.48" viewBox="0 0 12.67 7.48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.5 1.5L6.33 6.33L11.17 1.5" stroke="#CCCCCC" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientProfilePage; 