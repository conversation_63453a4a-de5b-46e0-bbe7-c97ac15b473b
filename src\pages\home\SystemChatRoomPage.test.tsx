
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import SystemChatRoomPage from './SystemChatRoomPage';

// Mock the navigate function
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

describe('SystemChatRoomPage', () => {
  it('renders correctly', () => {
    render(
      <BrowserRouter>
        <SystemChatRoomPage />
      </BrowserRouter>
    );
    
    // Check for main elements
    expect(screen.getByText('系統聊天室')).toBeInTheDocument();
    
    // Check for date headers
    expect(screen.getByText('04/07')).toBeInTheDocument();
    expect(screen.getByText('06/03')).toBeInTheDocument();
    expect(screen.getByText('06/09')).toBeInTheDocument();
    
    // Check for messages
    expect(screen.getByText('您的帳號在Mac上登入')).toBeInTheDocument();
    expect(screen.getByText('您的帳號在iPhone上登入')).toBeInTheDocument();
    expect(screen.getByText('您的任務「標題標題標題標題」即將到期')).toBeInTheDocument();
    expect(screen.getByText('您的備忘錄「標題標題標題標題標題標題標題」即將到期')).toBeInTheDocument();
    
    // Check for timestamps
    expect(screen.getAllByText('13:41').length).toBe(3);
    expect(screen.getByText('13:45')).toBeInTheDocument();
    
    // Check for input
    expect(screen.getByPlaceholderText('Aa')).toBeInTheDocument();
    
    // Check for system avatars
    const avatarLetters = screen.getAllByText('系統');
    expect(avatarLetters.length).toBe(4);
  });
  
  it('allows typing in the input field', () => {
    render(
      <BrowserRouter>
        <SystemChatRoomPage />
      </BrowserRouter>
    );
    
    const input = screen.getByPlaceholderText('Aa');
    fireEvent.change(input, { target: { value: 'Test message' } });
    
    // Check if the input value has changed
    expect(input).toHaveValue('Test message');
  });
  
  it('has working back button', () => {
    const navigateMock = jest.fn();
    jest.spyOn(require('react-router-dom'), 'useNavigate').mockImplementation(() => navigateMock);
    
    render(
      <BrowserRouter>
        <SystemChatRoomPage />
      </BrowserRouter>
    );
    
    // Find and click the back button
    const backButton = screen.getByAltText('Back');
    fireEvent.click(backButton);
    
    // Check if navigate was called with -1 (navigate back)
    expect(navigateMock).toHaveBeenCalledWith(-1);
  });
}); 