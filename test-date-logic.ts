// 測試日期邏輯
const formatChatTime = (timestamp) => {
  if (timestamp === undefined || isNaN(Number(timestamp))) {
    return '';
  }

  const messageDate = new Date(timestamp);
  const now = new Date();
  
  // 檢查是否是今天
  if (messageDate.toDateString() === now.toDateString()) {
    // 今天，顯示時間 HH:mm
    return messageDate.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit', hour12: false });
  }
  
  // 檢查是否是昨天
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (messageDate.toDateString() === yesterday.toDateString()) {
    // 昨天
    return '昨天';
  }
  
  // 檢查是否是本週內（從週一開始計算）
  const nowWeekDay = now.getDay(); // 0是週日，1-6是週一至週六
  const messageWeekDay = messageDate.getDay();
  
  // 計算本週週一的日期
  const thisWeekMonday = new Date(now);
  const daysFromMonday = nowWeekDay === 0 ? 6 : nowWeekDay - 1; // 週日算作上週
  thisWeekMonday.setDate(now.getDate() - daysFromMonday);
  thisWeekMonday.setHours(0, 0, 0, 0);
  
  // 檢查消息日期是否在本週內（從週一開始）
  const messageDateOnly = new Date(messageDate);
  messageDateOnly.setHours(0, 0, 0, 0);
  
  if (messageDateOnly >= thisWeekMonday) {
    // 本週內且不是今天和昨天，顯示星期
    const weekDayNames = ['週日', '週一', '週二', '週三', '週四', '週五', '週六'];
    return weekDayNames[messageWeekDay];
  }
  
  // 超過本週，顯示 MM-DD
  return `${String(messageDate.getMonth() + 1).padStart(2, '0')}-${String(messageDate.getDate()).padStart(2, '0')}`;
};

// 測試案例
const now = new Date('2024-12-16T10:00:00'); // 假設今天是週一
console.log('今天是:', now.toLocaleDateString('zh-TW', { weekday: 'long' }));

// 測試不同日期
const testCases = [
  { date: '2024-12-16T09:00:00', expected: '09:00', desc: '今天早上' },
  { date: '2024-12-15T15:00:00', expected: '昨天', desc: '昨天' },
  { date: '2024-12-14T15:00:00', expected: '週六', desc: '本週六' },
  { date: '2024-12-13T15:00:00', expected: '週五', desc: '本週五' },
  { date: '2024-12-12T15:00:00', expected: '週四', desc: '本週四' },
  { date: '2024-12-11T15:00:00', expected: '週三', desc: '本週三' },
  { date: '2024-12-10T15:00:00', expected: '週二', desc: '本週二' },
  { date: '2024-12-09T15:00:00', expected: '週一', desc: '本週一' },
  { date: '2024-12-08T15:00:00', expected: '12-08', desc: '上週日（應該顯示日期）' },
  { date: '2024-12-07T15:00:00', expected: '12-07', desc: '上週六（應該顯示日期）' },
];

testCases.forEach(testCase => {
  const result = formatChatTime(new Date(testCase.date).getTime());
  console.log(`${testCase.desc}: ${result} (期望: ${testCase.expected}) ${result === testCase.expected ? '✓' : '✗'}`);
});
