import { ConstantUtil } from './constantUtil';
import { t } from 'i18next';
import { IChatMessage } from '@/types/chat.types';
import userService from '@/services/core/user/userService';
import {SystemMessageEventCode } from '@/services';

// lastMessage 可能為 undefined/null/string/object
export function getLastMessageSummary(lastMessage: any, senderInfoMap?: Record<string, { avatarId: string | null, name: string }>): string {
  // 如果為空或未定義，返回空字符串
  if (!lastMessage) return '';

  let messageObj = lastMessage;

  // 如果是字符串，嘗試解析為 JSON
  if (typeof lastMessage === 'string') {
    try {
      messageObj = JSON.parse(lastMessage);
    } catch (e) {
      // 如果解析失敗，確保返回字符串
      return typeof lastMessage === 'string' ? lastMessage : '';
    }
  }

  // 如果不是對象，轉換為字符串返回
  if (!messageObj || typeof messageObj !== 'object') {
    return String(messageObj || '');
  }

  // 特殊處理：如果對象只有 text 屬性（來自 MessageTab 的錯誤處理）
  if (messageObj.text && Object.keys(messageObj).length === 1) {
    return String(messageObj.text);
  }

  const { type, content } = messageObj;
  
  let parsed: any = {};
  try {
    parsed = content ? JSON.parse(content) : {};
  } catch {
    // ignore
  }
  
  let messageContent = '';
  
  switch (type) {
    case ConstantUtil.MESSAGE_TYPE_TEXT:
      messageContent = parsed.text || '';
      break;
    case ConstantUtil.MESSAGE_TYPE_IMAGE:
      messageContent = t('message.image'); // ex: [圖片]
      break;
    case ConstantUtil.MESSAGE_TYPE_AUDIO:
      messageContent = t('message.audio'); // ex: [語音]
      break;
    case ConstantUtil.MESSAGE_TYPE_VIDEO:
      messageContent = t('message.video'); // ex: [影片]
      break;
    case ConstantUtil.MESSAGE_TYPE_FILE:
      messageContent = t('message.file'); // ex: [檔案]
      break;
    case ConstantUtil.MESSAGE_TYPE_EVENT:
      messageContent = parseEventStatus(parsed.content,senderInfoMap) || '';
      break;
    default:
      messageContent = t('message.unknown'); // ex: [未知消息]
  }

  // 如果有發送者姓名且非系統消息類型，則添加發送者姓名前綴
  // if (senderName && type !== ConstantUtil.MESSAGE_TYPE_EVENT) {
  //   return `${senderName}: ${messageContent}`;
  // }
  
  return messageContent;
}

/**
 * 計算服務時長，從指定的開始時間到當前時間或結束時間
 * @param startTime 服務開始時間（毫秒時間戳）
 * @param endTime 可選的服務結束時間（毫秒時間戳），如果不提供則使用當前時間
 * @returns 格式化的服務時長，如 "00:03"（分鐘:秒）
 */
export function calculateServiceDuration(startTime: number, endTime?: number): string {
  if (!startTime) return '00:00';
  
  // 計算當前時間與開始時間的差值（毫秒）
  const currentTime = endTime || Date.now();
  const durationMs = currentTime - startTime;
  
  // 轉換為秒
  const totalSeconds = Math.floor(durationMs / 1000);
  
  // 計算分鐘和秒
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  
  // 格式化為 "00:03" 格式
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');
  
  return `${formattedMinutes}:${formattedSeconds}`;
}

/**
 * 通用消息內容解析函數，可在各個組件中重用
 * @param message 消息對象
 * @param senderInfoCache 發送者信息緩存
 * @param bossServiceNumberOwnerId 服務號老闆ID（可選）
 * @returns 解析後的消息，包含text和isSystem屬性
 */
export function parseMessageContent(
  message: IChatMessage,
  senderInfoCache?: Record<string, { avatarId: string | null, name: string }>,
  bossServiceNumberOwnerId?: string | null
): { text: string; isSystem: boolean } {
  const content = message.content;
  try {
    // 嘗試解析JSON字符串
    const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;
    const senderId = parsedContent?.content?.agentId || parsedContent?.content?.memberId;
    
    // 獲取發送者名稱（從緩存或消息對象）
    let agentName = '';
    if (senderInfoCache && senderId && senderInfoCache[senderId]) {
      agentName = senderInfoCache[senderId].name;
    } else if (message.sender?.name) {
      agentName = message.sender.name;
    } else {
      agentName = '客服';
    }
    // 處理特定事件類型
    if (parsedContent.event === SystemMessageEventCode.SessionStart && parsedContent.content) {
      const { channel } = parsedContent.content;
      return {
        text: `${channel || '未知渠道'}進線`,
        isSystem: true
      };
    }
    else if (parsedContent.event === SystemMessageEventCode.AgentStart && parsedContent.content) {
      return {
        text: `${agentName}開始服務`,
        isSystem: true
      };
    }
    else if (parsedContent.event === SystemMessageEventCode.AgentStop && parsedContent.content) {
      const baseText = `${agentName}結束服務`;
      
      return {
        text: senderId === bossServiceNumberOwnerId
          ? baseText
          : `${baseText} 服務時長 ${calculateServiceDuration(parsedContent.content.serviceTime, parsedContent.content.endTime)}`,
        isSystem: true
      };
    }
    else if (parsedContent.event === SystemMessageEventCode.Timeout && parsedContent.content) {
      return {
        text: `服務超時 結束服務`,
        isSystem: true
      };
    }
    else if (parsedContent.event === SystemMessageEventCode.SessionAutoTransfer && parsedContent.content) { 
      return {
        text: `當前客戶已自動分配給${agentName}秘書`,
        isSystem: true
      };
    }
    else if (parsedContent.event === SystemMessageEventCode.RoomMemberAdd && parsedContent.content) {
      const inviteId = parsedContent?.content?.memberId;
      let inviteName = '';
      if (senderInfoCache && inviteId && senderInfoCache[inviteId]) {
        inviteName = senderInfoCache[inviteId].name;
      } else {
        const inviteUser = userService.getUserByIdSync(inviteId);
        inviteName = inviteUser?.name || inviteUser?.id || inviteId;
      }

      const invitedIds = parsedContent?.content?.memberIds;

      // 遍歷被邀請者ID，獲取用戶名並用空格連接
      let invitedMemberNames = '';
      if (invitedIds && Array.isArray(invitedIds)) {
        const names = invitedIds
          .map(id => {
            if (senderInfoCache && id && senderInfoCache[id]) {
              return senderInfoCache[id].name;
            } else {
              const user = userService.getUserByIdSync(id);
              return user?.name || id; // 如果找不到用戶名則使用ID
            }
          })
          .filter(name => name); // 過濾掉空值

        invitedMemberNames = names.join(' ');
      }

      return {
        text: `${inviteName}邀请${invitedMemberNames}加入聊天`,
        isSystem: true
      };
    }
    // 處理純文本消息
    else if (parsedContent.text) {
      return {
        text: parsedContent.text,
        isSystem: false
      };
    }
    
    // 如果無法識別格式，則返回原始內容字符串
    return {
      text: content,
      isSystem: false
    };
  } catch (error) {
    // JSON解析錯誤，直接返回原始內容
    return {
      text: content,
      isSystem: false
    };
  }
}

/**
 * 解析事件狀態並返回相應的描述文本
 * @param status 事件狀態
 * @returns 格式化的狀態描述文本
 */
export function parseEventStatus(content: any,senderInfoMap?: Record<string, { avatarId: string | null, name: string }>): string {
  // return JSON.stringify(content);
  // 獲取發送者名稱
  const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;
  const inviteId = parsedContent?.memberId;
  if(inviteId){
      let inviteName = '';
      if (senderInfoMap && inviteId && senderInfoMap[inviteId]) {
        inviteName = senderInfoMap[inviteId].name;
      } else {
        const inviteUser = userService.getUserByIdSync(inviteId);
        inviteName = inviteUser?.name || inviteUser?.id || inviteId;
      }
      const invitedIds = parsedContent?.memberIds;
      let invitedMemberNames = '';
      if (invitedIds && Array.isArray(invitedIds)) {
        const names = invitedIds
          .map(id => {
            if (senderInfoMap && id && senderInfoMap[id]) {
              return senderInfoMap[id].name;
            } else {
              const user = userService.getUserByIdSync(id);
              return user?.name || id; // 如果找不到用戶名則使用ID
            }
          })
          .filter(name => name); // 過濾掉空值

        invitedMemberNames = names.join(' ');
      }
      return `${inviteName}邀请${invitedMemberNames}加入聊天`; // 客服邀请加入聊天
  }
  let senderName = '';
  if (senderInfoMap && parsedContent.agentId && senderInfoMap[parsedContent.agentId]) {
    senderName = senderInfoMap[parsedContent.agentId].name;
  }
  const {channel, status }= content;
  switch (status) {
    case SystemMessageEventCode.AgentStart:
      return `${senderName}開始服務`; // 客服在線
    case SystemMessageEventCode.AgentStop:
      return `${senderName}結束服務`; // 客服結束服務
    case SystemMessageEventCode.Timeout:
      return `服務超時 結束服務`; // 服務超時
    case SystemMessageEventCode.SessionStart:
      return `${channel || '未知渠道'}進線`; // 客服在線
    default:
      return status || t('event.status.unknown'); // 未知狀態
  }
}