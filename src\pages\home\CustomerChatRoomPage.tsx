import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import './CustomerChatRoomPage.css';
import { ChatRoom } from './chat';
import { ChatRoomType } from '../../types/chat.types';
import { getRoomInfoOptimized } from '@/services/core/chat/roomService';
import { logService } from '@/services/system/logService';
import snService from '@/services/core/tenant/snService';
import roomDao from '@/services/db/dao/RoomDao';
import { RoomType } from '@/types/room.types';
import { useFixedStatusBarColor } from '../../hooks/useStatusBarColor';
import { PAGE_COLORS } from '../../config/app/pageColors';
import { useTranslation } from 'react-i18next';
import { contactService } from '@/services';
import { ChatRoomSkeleton } from '../../components/common';
import { ROUTE_TEAM_CHAT_ROOM } from '@/config/app/routes';

const CustomerChatRoomPage: React.FC = () => {
  const navigate = useNavigate();
  const { roomId } = useParams<{ roomId: string }>();
  const { t } = useTranslation();
  const [roomInfo, setRoomInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [teamUnreadCount, setTeamUnreadCount] = useState<number>(0);
  const [displayName, setDisplayName] = useState<string>('聊天室');

  // 设置客户聊天室状态栏颜色
  useFixedStatusBarColor(PAGE_COLORS.DEFAULT);


  const handleBack = () => {
    navigate(-1);
  };

  const handleTeamChatClick = () => {
    navigate(ROUTE_TEAM_CHAT_ROOM);
  };

  // 清理聊天室名稱，移除末尾的 " 0"、"(0)" 等模式
  const cleanRoomName = (name: string): string => {
    if (!name) return name;
    // 移除末尾的 " 0"、"(0)"、" (0)"、" 0 " 等模式
    return name.replace(/\s*\(?0\)?\s*$/, '').trim();
  };

  // 根據 type 返回顯示名稱
  const getRoomDisplayName = (room: any, customerContactName?: string | null): string => {
    if (!room.type) return t('未定義聊天室');

    if (room.type === RoomType.Person || room.type === RoomType.AccountPerson) return t('我的聊天室');
    if (room.type === RoomType.AileSystem) return t('系統聊天室');
    if (room.type === RoomType.ServiceMember) return t('團隊聊天室');
    if (room.type === RoomType.Services) {
      // 如果是服務類型的聊天室，優先使用客戶聯絡人名稱
      if (customerContactName) {
        return cleanRoomName(customerContactName);
      }
      // 沒有客戶聯絡人名稱，顯示未命名
      return t('未命名聊天室');
    }
    return cleanRoomName(room.name || t('未命名聊天室'));
  };

  useEffect(() => {
    if (!roomId) return;
    setError(null);

    const fetchRoomData = async () => {
      try {
        logService.info('開始獲取客戶聊天室資訊', { roomId });

        // 使用優化的獲取方法：優先從DB獲取，沒有則調用API
        const result = await getRoomInfoOptimized(roomId);

        if (!result.success || !result.data) {
          setError(result.error || '房間資訊獲取失敗');
          logService.error('房間資訊獲取失敗', {
            roomId,
            error: result.error,
            source: result.source
          });
          return;
        }

        const roomData = result.data;
        logService.info('客戶聊天室資訊獲取成功', {
          roomId,
          source: result.source, // 'database' 或 'api'
          hasData: !!roomData,
          lastSequence: roomData?.lastSequence,
          ownerId: roomData?.ownerId,
          type: roomData?.type
        });

        setRoomInfo(roomData);

        // 如果是Services類型的聊天室，嘗試獲取客戶聯絡人名稱
        let customerContactName: string | null = null;
        if (roomData && roomData.type === RoomType.Services && roomData.ownerId) {
          try {
            const contact = await contactService.getContactById(roomData.ownerId);
            if (contact && contact.name) {
              customerContactName = contact.name;

              logService.info('客戶聯絡人名稱獲取成功', {
                roomId,
                ownerId: roomData.ownerId,
                contactName: customerContactName
              });
            }
          } catch (contactError) {
            logService.warn('獲取客戶聯絡人名稱失敗', {
              error: contactError,
              roomId,
              ownerId: roomData.ownerId
            });
          }
        }

        // 設置顯示名稱
        if (roomData) {
          const name = getRoomDisplayName(roomData, customerContactName);
          setDisplayName(name);
          logService.info('聊天室顯示名稱設置', {
            roomId,
            originalName: roomData.name,
            contactName: customerContactName,
            displayName: name,
            dataSource: result.source
          });
        }

      } catch (error) {
        logService.error('房間資訊獲取過程中發生錯誤', { error, roomId });
        setError('房間資訊獲取失敗');
      }
    };

    fetchRoomData();
  }, [roomId]);

  // 獲取團隊聊天室未讀數
  useEffect(() => {
    const fetchTeamUnreadCount = async () => {
      if (!roomInfo?.serviceNumberId) return;

      try {
        // 獲取服務號信息，包含 memberRoomId
        const serviceNumber = await snService.getServiceNumberById(roomInfo.serviceNumberId);
        if (serviceNumber?.memberRoomId) {
          // 獲取團隊聊天室信息
          const teamRoom = await roomDao.getRoomById(serviceNumber.memberRoomId);
          if (teamRoom) {
            setTeamUnreadCount(teamRoom.unreadCount || 0);
            logService.info('團隊聊天室未讀數獲取成功', {
              memberRoomId: serviceNumber.memberRoomId,
              unreadCount: teamRoom.unreadCount
            });
          }
        }
      } catch (error) {
        logService.error('獲取團隊聊天室未讀數失敗', { error, serviceNumberId: roomInfo.serviceNumberId });
      }
    };

    fetchTeamUnreadCount();
  }, [roomInfo?.serviceNumberId]);

  return (
    <div className="customer-chat-room-page">
      {error ? (
        <div className="page-error">
          <span>{error}</span>
          <button onClick={() => window.location.reload()}>重新載入</button>
        </div>
      ) : roomInfo ? (
        <ChatRoom
          type={ChatRoomType.CUSTOMER}
          title={displayName}
          dateGroups={[]} // 不再傳入消息，由 ChatRoom 自行獲取
          roomId={roomId || ''}
          roomInfo={roomInfo} // 將 roomInfo 傳遞給 ChatRoom 組件
          showTasksBar={true}
          taskCount={4}
          showTeamSelector={true}
          teamUnreadCount={teamUnreadCount}
          teamAvatarSrc={roomInfo?.avatarId}
          onBackClick={handleBack}
          onTeamChatClick={handleTeamChatClick}
        />
      ) : (
        <ChatRoomSkeleton
          chatType="customer"
          showTasksBar={true}
        />
      )}
    </div>
  );
};

export default CustomerChatRoomPage; 