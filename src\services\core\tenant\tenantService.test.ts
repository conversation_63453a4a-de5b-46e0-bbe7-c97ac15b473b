import tenantService from './tenantService';

import { logService } from '../../system/logService';

import httpService from '../../system/httpService';
import { CryptoUtil } from '../../../utils/cryptoUtil';
import { firstValueFrom, of } from 'rxjs';

// 模擬 aileDBService
jest.mock('../db/aileDBService', () => ({
  __esModule: true,
  default: {
    all: jest.fn(),
    get: jest.fn(),
    run: jest.fn(),
    saveToStore: jest.fn().mockResolvedValue(undefined),
  }
}));

// 模擬 logService
jest.mock('../system/logService', () => ({
  __esModule: true,
  logService: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }
}));

// 模擬 httpService
jest.mock('../system/httpService', () => ({
  __esModule: true,
  default: {
    post: jest.fn(),
  }
}));

// 模擬 CryptoUtil
jest.mock('../../utils/cryptoUtil', () => ({
  __esModule: true,
  CryptoUtil: {
    encryptApiRequest: jest.fn(data => JSON.stringify(data)),
    decryptApiResponse: jest.fn(data => data),
  }
}));

// 模擬 firstValueFrom
jest.mock('rxjs', () => {
  const original = jest.requireActual('rxjs');
  return {
    ...original,
    firstValueFrom: jest.fn(),
    of: jest.fn(val => ({ pipe: jest.fn(() => val), subscribe: jest.fn() })),
  };
});

describe('TenantService', () => {
  // 在每個測試之前重置模擬
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // 略過原有的測試，只測試新增的 applyTokenForTenant 函數
  describe('applyToken', () => {
    it('應成功切換租戶', async () => {
      // 模擬成功的 API 響應
      const mockResponse = {
        status: 0,
        success: true,
        data: {
          user: {
            id: 'user1',
            name: '測試用戶',
            tenantId: 'tenant1',
            accountId: 'account1',
          },
          tenantInfo: {
            id: 'tenant1',
            name: '測試租戶',
            shortName: '測試',
          },
          bossServiceNumberId: 'service1',
        },
        msg: '切換成功',
        code: '0',
      };
      
      // 設置模擬
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockResponse);

      // 測試調用
      const result = await tenantService.applyToken('tenant1');

      // 驗證 HTTP 請求
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('/openapi/token/apply'),
        { tenantId: 'tenant1' },
        {},
        true
      );

      // 驗證結果
      expect(result.success).toBe(true);
      expect(result.status).toBe(0);
      expect(result.data?.tenantInfo?.id).toBe('tenant1');
      expect(result.data?.user?.id).toBe('user1');
      expect(logService.info).toHaveBeenCalledWith(
        '租戶切換成功',
        expect.objectContaining({ 
          tenantId: 'tenant1',
          tenantName: '測試租戶',
          userName: '測試用戶',
        })
      );
    });

    it('應處理租戶切換失敗', async () => {
      // 模擬失敗的 API 響應
      const mockResponse = {
        status: 400,
        success: false,
        data: null,
        msg: '無效的租戶ID',
        code: '400',
      };
      
      // 設置模擬
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockResponse);

      // 測試調用
      const result = await tenantService.applyToken('invalid_tenant');

      // 驗證結果
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.msg).toBe('無效的租戶ID');
      expect(logService.warn).toHaveBeenCalledWith(
        '租戶切換失敗',
        expect.objectContaining({ 
          tenantId: 'invalid_tenant',
          status: 400,
          msg: '無效的租戶ID',
        })
      );
    });

    it('應處理空租戶ID的情況', async () => {
      // 測試調用
      const result = await tenantService.applyToken('');

      // 驗證結果
      expect(result.success).toBe(false);
      expect(result.status).toBe(-1);
      expect(result.msg).toBe('未提供有效的租戶ID');
      expect(logService.warn).toHaveBeenCalledWith(
        '嘗試切換租戶但未提供有效的租戶ID'
      );
      expect(httpService.post).not.toHaveBeenCalled();
    });

    it('應處理 API 請求錯誤', async () => {
      // 模擬 API 錯誤
      const mockError = new Error('網絡錯誤');
      (firstValueFrom as jest.Mock).mockRejectedValue(mockError);
      (httpService.post as jest.Mock).mockReturnValue(of({}));

      // 測試調用
      const result = await tenantService.applyToken('tenant1');

      // 驗證結果
      expect(result.success).toBe(false);
      expect(result.status).toBe(-1);
      expect(result.msg).toBe('網絡錯誤');
      expect(logService.error).toHaveBeenCalledWith(
        '切換租戶時發生錯誤',
        expect.objectContaining({ 
          error: mockError,
          tenantId: 'tenant1',
        })
      );
    });

    it('應處理響應解密失敗', async () => {
      // 模擬原始响应
      const mockRawResponse = { encrypted: 'data' };
      // 模拟解密失败
      const mockError = new Error('解密失敗');
      
      // 設置模擬
      (firstValueFrom as jest.Mock).mockResolvedValue(mockRawResponse);
      (httpService.post as jest.Mock).mockReturnValue(of(mockRawResponse));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockImplementation(() => {
        throw mockError;
      });

      // 測試調用
      const result = await tenantService.applyToken('tenant1');

      // 驗證結果
      expect(result).toEqual({
        ...mockRawResponse,
        timeCost: expect.any(Number)
      });
      expect(logService.warn).toHaveBeenCalledWith(
        '解密切換租戶響應失敗，使用原始響應',
        expect.objectContaining({ error: mockError })
      );
    });
  });

  describe('getBossServiceNumberId', () => {
    it('應成功獲取商務號ID', async () => {
      // 模擬成功的 API 響應
      const mockResponse = {
        status: 0,
        success: true,
        data: {
          id: 'boss123',
          name: '商務號',
          type: 'Boss',
          code: 'BN001',
          description: '個人租戶商務號',
          openType: 'External',
          status: 'Enable',
          avatarId: 'avatar123',
          tenantId: 'tenant123',
          memberRoomId: 'room123'
        },
        msg: '查詢成功',
        code: '0',
      };
      
      // 設置模擬
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockResponse);

      // 測試調用
      const result = tenantService.getCurrentBossServiceNumberId();

      // 驗證 HTTP 請求
      expect(httpService.post).toHaveBeenCalledWith(
        expect.stringContaining('/tenant/servicenumber/v1/person/tenant123/boss')
      );

      // 驗證結果
      expect(result).toBe('boss123');
      expect(logService.info).toHaveBeenCalledWith(
        '成功獲取個人租戶的商務號',
        expect.objectContaining({ 
          tenantId: 'tenant123',
          bossServiceNumberId: 'boss123',
        })
      );
    });

    it('應處理獲取商務號失敗', async () => {
      // 模擬失敗的 API 響應
      const mockResponse = {
        status: 404,
        success: false,
        data: null,
        msg: '未找到商務號',
        code: '404',
      };
      
      // 設置模擬
      (firstValueFrom as jest.Mock).mockResolvedValue(mockResponse);
      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockResponse);

      // 測試調用
      const result = tenantService.getCurrentBossServiceNumberId();

      // 驗證結果
      expect(result).toBeNull();
      expect(logService.warn).toHaveBeenCalledWith(
        '獲取個人租戶的商務號失敗',
        expect.objectContaining({ 
          tenantId: 'invalid_tenant',
          status: 404,
          message: '未找到商務號',
        })
      );
    });

    it('應處理空租戶ID的情況', async () => {
      // 測試調用
      const result = tenantService.getCurrentBossServiceNumberId();

      // 驗證結果
      expect(result).toBeNull();
      expect(logService.warn).toHaveBeenCalledWith('獲取商務號ID失敗：租戶ID為空');
      expect(httpService.post).not.toHaveBeenCalled();
    });

    it('應處理 API 請求錯誤', async () => {
      // 模擬 API 錯誤
      const mockError = new Error('網絡錯誤');
      (firstValueFrom as jest.Mock).mockRejectedValue(mockError);
      (httpService.post as jest.Mock).mockReturnValue(of({}));

      // 測試調用
      const result = tenantService.getCurrentBossServiceNumberId();

      // 驗證結果
      expect(result).toBeNull();
      expect(logService.error).toHaveBeenCalledWith(
        '獲取個人租戶的商務號發生異常',
        expect.objectContaining({ 
          error: mockError,
          tenantId: 'tenant123',
        })
      );
    });
  });
}); 