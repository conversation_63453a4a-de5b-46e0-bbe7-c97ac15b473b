import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';

import BusinessSignUpPage from './BusinessSignUpPage';
import { store } from '../../app/store';
import { ROUTE_HOME } from '../../config/app/routes';

// 模拟导航函数
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// 模拟Toast组件
jest.mock('antd-mobile', () => {
  const actual = jest.requireActual('antd-mobile');
  return {
    ...actual,
    Toast: {
      show: jest.fn(),
    },
  };
});

// 模拟LogService
jest.mock('../../services/logService', () => ({
  info: jest.fn(),
  error: jest.fn(),
}));

// 模拟URL.createObjectURL和URL.revokeObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

// 测试包装器
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={store}>
      <MemoryRouter>
        {component}
      </MemoryRouter>
    </Provider>
  );
};

describe('BusinessSignUpPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('renders business register page correctly', () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    // 验证页面标题渲染正确
    expect(screen.getByText('請填寫公司/團隊資訊')).toBeInTheDocument();
    
    // 验证表单字段渲染正确
    expect(screen.getByText('公司/團隊名稱')).toBeInTheDocument();
    expect(screen.getByText('公司/團隊照片')).toBeInTheDocument();
    expect(screen.getByText('公司網址')).toBeInTheDocument();
    expect(screen.getByText('公司電話')).toBeInTheDocument();
    expect(screen.getByText('地址')).toBeInTheDocument();
    
    // 验证输入框渲染正确
    expect(screen.getByPlaceholderText('請輸入公司/團隊名稱')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('請輸入公司網址')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('請輸入公司聯絡電話')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('請輸入公司地址')).toBeInTheDocument();
    
    // 验证确认按钮渲染正确
    expect(screen.getByText('確認')).toBeInTheDocument();
    
    // 验证确认按钮初始状态应该是禁用的
    expect(screen.getByText('確認').closest('button')).toBeDisabled();
  });
  
  test('validates empty company name input', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入公司/團隊名稱');
    
    // 输入空公司名称并失焦
    fireEvent.change(nameInput, { target: { value: '' } });
    fireEvent.blur(nameInput);
    
    // 验证显示错误信息
    expect(screen.getByText('請輸入公司/團隊名稱')).toBeInTheDocument();
  });
  
  test('validates company name length', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入公司/團隊名稱');
    
    // 输入超过18个字符的公司名称
    const longName = '这是一个超级长的公司名称测试这是一个超级长的公司';
    fireEvent.change(nameInput, { target: { value: longName } });
    fireEvent.blur(nameInput);
    
    // 验证显示错误信息
    expect(screen.getByText('公司/團隊名稱字數上限18個字元')).toBeInTheDocument();
    
    // 输入有效长度的名称
    fireEvent.change(nameInput, { target: { value: '王小明的公司' } });
    fireEvent.blur(nameInput);
    
    // 验证错误消失
    expect(screen.queryByText('公司/團隊名稱字數上限18個字元')).not.toBeInTheDocument();
  });
  
  test('validates company logo file size', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    
    // 创建一个大于2MB的文件
    const largeFile = new File(['x'.repeat(3 * 1024 * 1024)], 'large-image.jpg', { type: 'image/jpeg' });
    
    // 模拟文件选择
    fireEvent.change(fileInput, { target: { files: [largeFile] } });
    
    // 验证显示错误信息
    expect(screen.getByText('圖片請選擇小於2M的圖片')).toBeInTheDocument();
    
    // 创建一个小于2MB的文件
    const smallFile = new File(['x'.repeat(1 * 1024 * 1024)], 'small-image.jpg', { type: 'image/jpeg' });
    
    // 模拟文件选择
    fireEvent.change(fileInput, { target: { files: [smallFile] } });
    
    // 验证错误信息消失
    expect(screen.queryByText('圖片請選擇小於2M的圖片')).not.toBeInTheDocument();
  });
  
  test('validates company logo file type', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    
    // 创建一个非图片文件
    const textFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
    
    // 模拟文件选择
    fireEvent.change(fileInput, { target: { files: [textFile] } });
    
    // 验证显示错误信息
    expect(screen.getByText('請上傳圖片格式文件')).toBeInTheDocument();
  });
  
  test('validates URL format', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const urlInput = screen.getByPlaceholderText('請輸入公司網址');
    
    // 输入无效的URL
    fireEvent.change(urlInput, { target: { value: 'invalid-url' } });
    fireEvent.blur(urlInput);
    
    // 验证显示错误信息
    expect(screen.getByText('請確認公司網址格式是否正確')).toBeInTheDocument();
    
    // 输入有效的URL
    fireEvent.change(urlInput, { target: { value: 'https://example.com' } });
    fireEvent.blur(urlInput);
    
    // 验证错误信息消失
    expect(screen.queryByText('請確認公司網址格式是否正確')).not.toBeInTheDocument();
    
    // 验证空URL是有效的（非必填）
    fireEvent.change(urlInput, { target: { value: '' } });
    fireEvent.blur(urlInput);
    expect(screen.queryByText('請確認公司網址格式是否正確')).not.toBeInTheDocument();
  });
  
  test('validates phone number format', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const phoneInput = screen.getByPlaceholderText('請輸入公司聯絡電話');
    
    // 输入无效的电话号码
    fireEvent.change(phoneInput, { target: { value: 'invalid-phone' } });
    fireEvent.blur(phoneInput);
    
    // 验证显示错误信息
    expect(screen.getByText('請確認電話號碼格式')).toBeInTheDocument();
    
    // 输入有效的电话号码
    fireEvent.change(phoneInput, { target: { value: '+886 912345678' } });
    fireEvent.blur(phoneInput);
    
    // 验证错误信息消失
    expect(screen.queryByText('請確認電話號碼格式')).not.toBeInTheDocument();
    
    // 验证空电话是有效的（非必填）
    fireEvent.change(phoneInput, { target: { value: '' } });
    fireEvent.blur(phoneInput);
    expect(screen.queryByText('請確認電話號碼格式')).not.toBeInTheDocument();
  });
  
  test('validates address length', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const addressInput = screen.getByPlaceholderText('請輸入公司地址');
    
    // 创建一个超过100个字符的字符串
    const longAddress = '这是一个超长的地址'.repeat(10);
    
    // 输入超长地址并失焦
    fireEvent.change(addressInput, { target: { value: longAddress } });
    fireEvent.blur(addressInput);
    
    // 验证显示错误信息
    expect(screen.getByText('請確認地址格式是否正確')).toBeInTheDocument();
    
    // 输入有效长度的地址
    fireEvent.change(addressInput, { target: { value: '台北市信義區101大樓' } });
    fireEvent.blur(addressInput);
    
    // 验证错误消失
    expect(screen.queryByText('請確認地址格式是否正確')).not.toBeInTheDocument();
    
    // 验证空地址是有效的（非必填）
    fireEvent.change(addressInput, { target: { value: '' } });
    fireEvent.blur(addressInput);
    expect(screen.queryByText('請確認地址格式是否正確')).not.toBeInTheDocument();
  });
  
  test('enables confirm button when required fields are filled', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入公司/團隊名稱');
    const confirmButton = screen.getByText('確認').closest('button');
    
    // 初始状态按钮应该是禁用的
    expect(confirmButton).toBeDisabled();
    
    // 输入有效的公司名称
    fireEvent.change(nameInput, { target: { value: '王小明的團隊' } });
    
    // 按钮应该启用
    await waitFor(() => {
      expect(confirmButton).not.toBeDisabled();
    });
  });
  
  test('disables confirm button when optional fields have validation errors', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入公司/團隊名稱');
    const urlInput = screen.getByPlaceholderText('請輸入公司網址');
    const confirmButton = screen.getByText('確認').closest('button');
    
    // 输入有效的公司名称
    fireEvent.change(nameInput, { target: { value: '王小明的團隊' } });
    
    // 按钮应该启用
    await waitFor(() => {
      expect(confirmButton).not.toBeDisabled();
    });
    
    // 输入无效的URL
    fireEvent.change(urlInput, { target: { value: 'invalid-url' } });
    fireEvent.blur(urlInput);
    
    // 按钮应该禁用
    await waitFor(() => {
      expect(confirmButton).toBeDisabled();
    });
  });
  
  test('handles confirm button click and navigates to chat list', async () => {
    renderWithProviders(<BusinessSignUpPage />);
    
    const nameInput = screen.getByPlaceholderText('請輸入公司/團隊名稱');
    const confirmButton = screen.getByText('確認').closest('button') as HTMLButtonElement;
    
    // 输入有效的公司名称
    fireEvent.change(nameInput, { target: { value: '王小明的團隊' } });
    
    // 等待按钮启用
    await waitFor(() => {
      expect(confirmButton).not.toBeDisabled();
    });
    
    // 点击确认按钮
    fireEvent.click(confirmButton);
    
    // 验证Toast被调用
    await waitFor(() => {
      expect(require('antd-mobile').Toast.show).toHaveBeenCalledWith(expect.objectContaining({
        content: '註冊成功',
      }));
    });
    
    // 验证导航到首頁
    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_HOME);
  });
}); 