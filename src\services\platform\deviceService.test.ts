import deviceService, { platform } from './deviceService';

// Mock Capacitor 插件
jest.mock('@capacitor/device', () => ({
  Device: {
    getInfo: jest.fn().mockResolvedValue({
      platform: 'web',
      model: 'Test Model',
      manufacturer: 'Test Manufacturer',
      operatingSystem: 'web',
      osVersion: 'test',
      webViewVersion: 'test',
      isVirtual: false,
      memUsed: 1024,
    }),
    getId: jest.fn().mockResolvedValue({
      identifier: 'test-device-id-12345',
    }),
    getBatteryInfo: jest.fn().mockResolvedValue({
      batteryLevel: 0.75,
      isCharging: true,
    }),
    getLanguageCode: jest.fn().mockResolvedValue({
      value: 'zh-CN',
    }),
  },
}));

// Mock Capacitor core
jest.mock('@capacitor/core', () => ({
  Capacitor: {
    getPlatform: jest.fn(() => 'web'),
    isNativePlatform: jest.fn(() => false),
    isPluginAvailable: jest.fn(() => true),
  },
}));

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('deviceService', () => {
  beforeEach(() => {
    localStorageMock.clear();
    jest.clearAllMocks();
    
    // 设置屏幕属性
    Object.defineProperty(window, 'screen', {
      value: {
        width: 1920,
        height: 1080,
      },
      writable: true,
    });
    
    Object.defineProperty(window, 'devicePixelRatio', {
      value: 2,
      writable: true,
    });
    
    Object.defineProperty(window, 'navigator', {
      value: {
        ...window.navigator,
        language: 'zh-CN',
        userAgent: 'jest-test-agent',
        onLine: true,
      },
      writable: true,
    });
  });

  test('应该创建设备服务单例', () => {
    expect(deviceService).toBeDefined();
  });

  test('应该初始化并获取设备信息', async () => {
    const info = await deviceService.initialize();
    
    expect(info).toBeDefined();
    expect(info.platform).toBe('web');
    expect(info.model).toBe('Test Model');
    expect(info.manufacturer).toBe('Test Manufacturer');
    expect(info.batteryLevel).toBe(0.75);
  });

  test('应该生成设备 headers', async () => {
    await deviceService.initialize();
    
    const headers = deviceService.getDeviceHeaders();
    expect(headers).toBeDefined();
    expect(headers['deviceData']).toBeDefined();
    expect(headers['x-client-version']).toBeDefined();
    expect(headers['x-client-platform']).toBe('web');
  });

  test('应该生成正确格式的 deviceData 头部数据', () => {
    const deviceData = deviceService.getDeviceDataHeader();
    
    // 验证必需字段（Aile API 规范）
    expect(deviceData.deviceName).toBeDefined();
    expect(deviceData.deviceType).toBeDefined();
    expect(deviceData.osType).toBeDefined();
    expect(deviceData.channel).toBeDefined();
    expect(deviceData.language).toBeDefined();
    expect(deviceData.uniqueId).toBeDefined();
    expect(deviceData.bundleId).toBeDefined();
    expect(deviceData.version).toBeDefined();
    
    // 验证值的正确性
    expect(deviceData.osType).toBe('web');
    expect(deviceData.channel).toBe('Aile');
    expect(deviceData.language).toBe('zh-cn');
  });

  test('应该获取设备 ID', () => {
    const deviceId = deviceService.getDeviceId();
    expect(deviceId).toBeDefined();
    expect(typeof deviceId).toBe('string');
  });


});

describe('platform 工具', () => {
  test('应该正确获取平台信息', () => {
    expect(platform.get()).toBe('web');
    expect(platform.isWeb()).toBe(true);
    expect(platform.isNative()).toBe(false);
    expect(platform.isIOS()).toBe(false);
    expect(platform.isAndroid()).toBe(false);
  });

  test('应该执行平台特定代码', () => {
    const webHandler = jest.fn(() => 'web result');
    const iosHandler = jest.fn(() => 'ios result');
    const defaultHandler = jest.fn(() => 'default result');

    const result = platform.run({
      web: webHandler,
      ios: iosHandler,
      default: defaultHandler,
    });

    expect(result).toBe('web result');
    expect(webHandler).toHaveBeenCalled();
    expect(iosHandler).not.toHaveBeenCalled();
    expect(defaultHandler).not.toHaveBeenCalled();
  });

  test('应该生成平台 CSS 类', () => {
    const classes = platform.classes();
    
    expect(classes['platform-web']).toBe(true);
    expect(classes['platform-native']).toBe(false);
    expect(classes['platform-ios']).toBe(false);
    expect(classes['platform-android']).toBe(false);
  });

  test('应该获取平台信息摘要', () => {
    const info = platform.info();
    
    expect(info.platform).toBe('web');
    expect(info.isNative).toBe(false);
    expect(info.isWeb).toBe(true);
    expect(info.capabilities).toBeDefined();
  });
}); 