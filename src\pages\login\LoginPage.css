/* LoginPage 页面专属样式 */
.login-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #FFFFFF;
  position: relative; /* 添加相對定位以支持絕對定位的子元素 */
}

/* 環境選擇器觸發區域 */
.env-selector-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 60px;
  height: 60px;
  z-index: 100;
  cursor: default;
  /* 不顯示背景和邊框，保持透明 */
  background-color: transparent;
  border: none;
}

/* ActionSheet 的樣式會由 Ant Design Mobile 自動處理 */

.login-page__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 60px 0px;
}

.login-page__header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 0px 0px 20px;
}

.login-page__logo {
  width: 156px;
  height: 79px;
}

.login-page__tagline {
  color: #333333;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4;
  text-align: center;
}

.login-page__main-image {
  display: flex;
  justify-content: center;
  width: 320px;
  height: 320px;
}

.login-page__main-image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.login-page__buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0px 24px;
}

.login-page__line-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding: 12px;
  background-color: #39CD00;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  width: 100%;
  height: 48px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-page__line-button:hover {
  background-color: #2DB400;
}

.login-page__line-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.login-page__line-button--loading {
  background-color: #2DB400;
}

.login-page__line-icon {
  width: 24px;
  height: 24px;
}

.login-page__line-text {
  color: #FFFFFF;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4;
  text-align: center;
}

.login-page__other-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px;
  background-color: #FFFFFF !important;
  border: 1px solid #EEEEEE !important;
  border-radius: 4px !important;
  width: 100%;
  height: 46px;
}

.login-page__other-text {
  color: #999999;
  font-family: 'SF Pro', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4;
  text-align: center;
} 