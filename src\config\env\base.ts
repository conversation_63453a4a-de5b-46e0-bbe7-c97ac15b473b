/**
 * 基础环境配置
 * 包含所有环境共享的配置项
 */

export interface BaseConfig {
  APP_NAME: string;
  VERSION: string;
  BUILD_TIME: string;
  
  // API配置
  API_TIMEOUT: number;
  API_RETRY_COUNT: number;
  
  // 缓存配置
  CACHE_TTL: number;
  CACHE_MAX_SIZE: number;
  
  // 日志配置
  LOG_LEVEL: string;
  LOG_MAX_FILES: number;
  
  // 数据库配置
  DB_NAME: string;
  DB_VERSION: number;
  
  // Socket配置
  SOCKET_RECONNECT_DELAY: number;
  SOCKET_MAX_RECONNECT_ATTEMPTS: number;
}

export const baseConfig: BaseConfig = {
  APP_NAME: 'Aile App',
  VERSION: '5.0.0',
  BUILD_TIME: new Date().toISOString(),
  
  // API配置
  API_TIMEOUT: 30000,
  API_RETRY_COUNT: 3,
  
  // 缓存配置
  CACHE_TTL: 300000, // 5分钟
  CACHE_MAX_SIZE: 100,
  
  // 日志配置
  LOG_LEVEL: 'info',
  LOG_MAX_FILES: 10,
  
  // 数据库配置
  DB_NAME: 'aile_app.db',
  DB_VERSION: 1,
  
  // Socket配置
  SOCKET_RECONNECT_DELAY: 2000,
  SOCKET_MAX_RECONNECT_ATTEMPTS: 5,
};
