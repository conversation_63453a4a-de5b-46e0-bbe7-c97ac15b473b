mutation RequestLoginOtp($requestLoginOtpRequest2: OtpInput) {
  requestLoginOtp(request: $requestLoginOtpRequest2) {
    data {
      onceToken
      validSecond
    }
    code
    status
    msg
    success
    timeCost
  }
}
mutation LoginWithOtp($request: LoginWithOtpRequest!) {
  loginWithOtp(request: $request) {
    code
    data {
      name
      accountId
      countryCode
      mobile
      tokenId
      loginType
      onlineId
      systemRoomId
      personRoomId
      lastTenantId
      systemAccountId
      loginStatus
      isMute
      accountType
      isInitial
      tenantRelations {
        type
        id
        description
        industry
        industrySub
        scale
        code
        name
        shortName
        avatarId
        isLastTenant
        accountId
        relationId
        openId
        unReadCount
        officialServiceNumberId
        manageServiceNumberId
        joinTime
        manageServiceNumber {
          id
          name
          avatarId
          type
          ownerId
          accountId
        }
        officialServiceNumber {
          id
          name
          avatarId
          type
          ownerId
          accountId
        }
      }
      currentTenantInfo {
        id
        createTime
        updateTime
        name
        shortName
        code
        description
        type
        startTime
        endTime
        industry
        industrySub
        scale
        city
        address
        phone
        website
        avatarId
        accountId
        upgrade
        unifiedNumber
        representativeNumber
        certificateFileId
        certificateStatus
        certificateFailReason
        switchTime
        employeeInfo {
          id
          createTime
          updateTime
          name
          avatarId
          mood
          age
          gender
          birthday
          status
          accountId
          tenantId
          channel
          personRoomId
          joinType
          openId
          isJoinAile
          isBindAile
          isCollectInfo
          homePagePicId
        }
      }
      currentEmployeeInfo {
        id
        createTime
        updateTime
        name
        avatarId
        mood
        age
        gender
        birthday
        status
        accountId
        tenantId
        channel
        personRoomId
        joinType
        openId
        isJoinAile
        isBindAile
        isCollectInfo
        homePagePicId
      }
    }
    msg
    status
    success
    timeCost
  }
}