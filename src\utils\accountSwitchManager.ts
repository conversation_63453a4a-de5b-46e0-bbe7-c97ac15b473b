import { logService } from '../services/system/logService';
import aileDBService from '../services/db/aileDBService';
import { getLocalStorage, setLocalStorage } from './storage';
import { ConstantUtil } from './constantUtil';

/**
 * 账号切换管理器
 * 专门处理多账号切换时的数据库连接管理
 */
export class AccountSwitchManager {
  private static instance: AccountSwitchManager;
  private switchingInProgress: boolean = false;
  private switchQueue: Array<{ accountId: string; resolve: Function; reject: Function }> = [];

  private constructor() {}

  public static getInstance(): AccountSwitchManager {
    if (!AccountSwitchManager.instance) {
      AccountSwitchManager.instance = new AccountSwitchManager();
    }
    return AccountSwitchManager.instance;
  }

  /**
   * 安全的账号切换
   * @param newAccountId 新账号ID
   * @returns 是否切换成功
   */
  public async switchAccount(newAccountId: string): Promise<boolean> {
    if (!newAccountId) {
      logService.error('账号切换失败：新账号ID为空');
      return false;
    }

    // 如果正在切换中，加入队列等待
    if (this.switchingInProgress) {
      logService.info('账号切换正在进行中，加入等待队列', { newAccountId });
      return new Promise((resolve, reject) => {
        this.switchQueue.push({ accountId: newAccountId, resolve, reject });
      });
    }

    return this.performAccountSwitch(newAccountId);
  }

  /**
   * 执行账号切换的核心逻辑
   * @param newAccountId 新账号ID
   * @returns 是否切换成功
   */
  private async performAccountSwitch(newAccountId: string): Promise<boolean> {
    this.switchingInProgress = true;
    const startTime = Date.now();

    try {
      const currentAccountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
      
      // 检查是否真的需要切换
      if (currentAccountId === newAccountId) {
        logService.info('账号未发生变化，跳过切换', { accountId: newAccountId });
        return true;
      }

      logService.info('开始账号切换流程', { 
        from: currentAccountId, 
        to: newAccountId 
      });

      // 步骤1: 保存当前账号的数据
      if (currentAccountId && aileDBService.isInitialized()) {
        logService.info('保存当前账号数据', { accountId: currentAccountId });
        try {
          await aileDBService.saveToStore();
          // await aileDBService.flushWriteQueue();
        } catch (saveError) {
          logService.warn('保存当前账号数据失败，继续切换流程', { 
            error: saveError, 
            accountId: currentAccountId 
          });
        }
      }

      // 步骤2: 关闭旧数据库连接
      if (currentAccountId) {
        try {
          await aileDBService.close();
          logService.info('已关闭旧账号数据库连接', { accountId: currentAccountId });
        } catch (closeError) {
          logService.warn('关闭旧账号数据库连接失败，但继续切换流程', {
            error: closeError,
            accountId: currentAccountId
          });
        }
      }

      // 步骤3: 更新账号ID到本地存储
      setLocalStorage(ConstantUtil.ACCOUNT_ID_KEY, newAccountId);

      // 步骤4: 初始化新账号的数据库
      logService.info('初始化新账号数据库', { accountId: newAccountId });
      const initSuccess = await aileDBService.initForAccount(newAccountId);

      if (!initSuccess) {
        logService.error('新账号数据库初始化失败', { accountId: newAccountId });
        // 回滚账号ID
        if (currentAccountId) {
          setLocalStorage(ConstantUtil.ACCOUNT_ID_KEY, currentAccountId);
        }
        return false;
      }

      // 步骤5: 确保数据能正常存储（简单验证）
      try {
        logService.info('验证新账号数据库存储功能', { accountId: newAccountId });
        const storageOk = await aileDBService.verifyStorageCapability();
        if (storageOk) {
          logService.info('新账号数据库存储功能验证成功', { accountId: newAccountId });
        } else {
          logService.warn('新账号数据库存储功能验证失败，但继续切换流程', { accountId: newAccountId });
        }
      } catch (storageError) {
        logService.warn('新账号数据库存储功能验证失败，但继续切换流程', {
          error: storageError,
          accountId: newAccountId
        });
      }

      const switchTime = Date.now() - startTime;
      logService.info('账号切换完成', { 
        from: currentAccountId, 
        to: newAccountId,
        switchTimeMs: switchTime
      });

      return true;

    } catch (error) {
      logService.error('账号切换过程中发生错误', { 
        error: error as Error, 
        newAccountId 
      });
      return false;
    } finally {
      this.switchingInProgress = false;
      
      // 处理队列中的切换请求
      this.processQueue();
    }
  }

  /**
   * 处理切换队列
   */
  private async processQueue(): Promise<void> {
    if (this.switchQueue.length === 0) {
      return;
    }

    // 取出队列中的最后一个请求（最新的切换请求）
    const lastRequest = this.switchQueue.pop();
    // 清空队列，拒绝其他请求
    const rejectedRequests = this.switchQueue.splice(0);
    
    // 拒绝被跳过的请求
    rejectedRequests.forEach(request => {
      request.reject(new Error('账号切换被更新的请求覆盖'));
    });

    if (lastRequest) {
      try {
        const result = await this.performAccountSwitch(lastRequest.accountId);
        lastRequest.resolve(result);
      } catch (error) {
        lastRequest.reject(error);
      }
    }
  }

  /**
   * 强制登出当前账号
   * @returns 是否登出成功
   */
  public async forceLogout(): Promise<boolean> {
    try {
      const currentAccountId = getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null);
      
      if (!currentAccountId) {
        logService.info('没有当前账号，跳过登出流程');
        return true;
      }

      logService.info('开始强制登出流程', { accountId: currentAccountId });

      // 等待当前切换完成
      while (this.switchingInProgress) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 清空切换队列
      this.switchQueue.forEach(request => {
        request.reject(new Error('账号登出，切换请求被取消'));
      });
      this.switchQueue = [];

      // 保存并强制关闭数据库
      try {
        if (aileDBService.isInitialized()) {
          await aileDBService.saveToStore();
          // await aileDBService.flushWriteQueue();
          await aileDBService.close();
        }
      } catch (dbError) {
        logService.warn('登出时数据库操作失败', { error: dbError });
      }

      // 清除本地存储
      setLocalStorage(ConstantUtil.ACCOUNT_ID_KEY, null);
      
      logService.info('强制登出完成', { accountId: currentAccountId });
      return true;

    } catch (error) {
      logService.error('强制登出失败', { error: error as Error });
      return false;
    }
  }

  /**
   * 获取当前切换状态
   */
  public getSwitchStatus(): {
    switching: boolean;
    queueLength: number;
    currentAccountId: string | null;
  } {
    return {
      switching: this.switchingInProgress,
      queueLength: this.switchQueue.length,
      currentAccountId: getLocalStorage<string | null>(ConstantUtil.ACCOUNT_ID_KEY, null)
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    // 拒绝所有队列中的请求
    this.switchQueue.forEach(request => {
      request.reject(new Error('AccountSwitchManager 已被销毁'));
    });
    this.switchQueue = [];
    this.switchingInProgress = false;
  }
}

// 导出单例
export const accountSwitchManager = AccountSwitchManager.getInstance();
export default accountSwitchManager;
