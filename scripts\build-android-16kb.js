#!/usr/bin/env node

/**
 * 构建支持 16KB 页面大小的 Android APK
 * 
 * 这个脚本确保生成的 APK 符合 Android 15+ 的 16KB 页面大小要求
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建支持 16KB 页面大小的 Android APK...');

try {
  // 1. 清理之前的构建
  console.log('📦 清理之前的构建...');
  execSync('npm run build', { stdio: 'inherit' });
  
  // 2. 同步到 Capacitor
  console.log('🔄 同步到 Capacitor...');
  execSync('npx cap sync android', { stdio: 'inherit' });
  
  // 3. 构建 Android APK
  console.log('🔨 构建 Android APK...');
  process.chdir('android');
  
  // 使用 Gradle 构建，确保使用正确的配置
  execSync('./gradlew clean assembleRelease --stacktrace', { stdio: 'inherit' });
  
  // 4. 验证 APK 是否支持 16KB 页面大小
  console.log('✅ 验证 APK 16KB 页面大小支持...');
  
  const apkPath = 'app/build/outputs/apk/release';
  const apkFiles = fs.readdirSync(apkPath).filter(file => file.endsWith('.apk'));
  
  if (apkFiles.length > 0) {
    const apkFile = path.join(apkPath, apkFiles[0]);
    console.log(`📱 生成的 APK: ${apkFile}`);
    
    // 使用 aapt 检查 APK 信息
    try {
      const aaptOutput = execSync(`aapt dump badging "${apkFile}"`, { encoding: 'utf8' });
      console.log('📋 APK 信息验证完成');
      
      // 检查是否包含 16KB 支持的元数据
      if (aaptOutput.includes('android.supports_16kb_page_size')) {
        console.log('✅ APK 已配置支持 16KB 页面大小');
      } else {
        console.log('⚠️  警告: APK 可能未正确配置 16KB 页面大小支持');
      }
    } catch (error) {
      console.log('⚠️  无法验证 APK 信息 (aapt 不可用)，但构建已完成');
    }
  }
  
  console.log('🎉 Android APK 构建完成！');
  console.log('📍 APK 位置: android/app/build/outputs/apk/release/');
  
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
} finally {
  // 返回到项目根目录
  process.chdir('..');
}
