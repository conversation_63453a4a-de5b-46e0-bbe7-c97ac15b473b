import logService, { LogLevel } from './logService';
import { ConstantUtil } from '../../utils/constantUtil';
import { getLocalStorage } from '../../utils/storage';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string): string | null => store[key] || null,
    setItem: (key: string, value: string): void => { store[key] = value.toString(); },
    removeItem: (key: string): void => { delete store[key]; },
    clear: (): void => { store = {}; },
    _getStore: (): Record<string, string> => store
  };
})();
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock console
global.console = {
  ...global.console,
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  } as Response)
);

// Mock Sentry
jest.mock('@sentry/react', () => ({
  captureException: jest.fn(),
  captureMessage: jest.fn(),
}));
import * as Sentry from '@sentry/react';

describe('logService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    logService.clearLogs();
    logService.configure({
      minLevel: LogLevel.INFO,
      enableConsole: true,
      enableStorage: true,
      enableRemote: false,
      maxLogEntries: 1000,
      applicationName: 'AileApp',
      remoteEndpoint: undefined,
    });
  });

  it('should log info and store in buffer/localStorage', () => {
    logService.info('info message', { foo: 1 });
    const logs = logService.getLogs();
    expect(logs.length).toBe(1);
    expect(logs[0].level).toBe(LogLevel.INFO);
    expect(logs[0].message).toBe('info message');
    expect(logs[0].data).toEqual({ foo: 1 });
    expect(console.info).toHaveBeenCalled();
    expect(getLocalStorage<string>(ConstantUtil.LOGS_STORAGE_KEY, '')).toBe('');
  });

  it('should log error and call Sentry.captureException for Error', () => {
    const err = new Error('fail');
    logService.error('error message', err);
    expect(Sentry.captureException).toHaveBeenCalledWith(err);
    expect(Sentry.captureMessage).not.toHaveBeenCalled();
    expect(console.error).toHaveBeenCalled();
  });

  it('should log error and call Sentry.captureMessage for non-Error', () => {
    logService.error('error message', { foo: 2 });
    expect(Sentry.captureMessage).toHaveBeenCalledWith(
      expect.stringContaining('error message'),
      'error'
    );
    expect(Sentry.captureException).not.toHaveBeenCalled();
    expect(console.error).toHaveBeenCalled();
  });

  it('should log fatal and call Sentry.captureMessage', () => {
    logService.fatal('fatal error', { bar: 3 });
    expect(Sentry.captureMessage).toHaveBeenCalledWith(
      expect.stringContaining('fatal error'),
      'fatal'
    );
    expect(console.error).toHaveBeenCalled();
  });

  it('should not log below minLevel', () => {
    logService.configure({ minLevel: LogLevel.WARN });
    logService.info('info');
    logService.debug('debug');
    logService.warn('warn');
    logService.error('error');
    const logs = logService.getLogs();
    expect(logs.length).toBe(2);
    expect(logs[0].level).toBe(LogLevel.WARN);
    expect(logs[1].level).toBe(LogLevel.ERROR);
  });

  it('should respect maxLogEntries', () => {
    logService.configure({ maxLogEntries: 2 });
    logService.info('1');
    logService.info('2');
    logService.info('3');
    const logs = logService.getLogs();
    expect(logs.length).toBe(2);
    expect(logs[0].message).toBe('2');
    expect(logs[1].message).toBe('3');
  });

  it('should clear logs and localStorage', () => {
    logService.info('to be cleared');
    logService.clearLogs();
    expect(logService.getLogs().length).toBe(0);
    expect(getLocalStorage<string>(ConstantUtil.LOGS_STORAGE_KEY, '')).toBe('');
  });

  it('should set userId and include in logs', () => {
    logService.setUserId('user-abc');
    logService.info('with user');
    const logs = logService.getLogs();
    expect(logs[0].userId).toBe('user-abc');
  });

  it('should flush logs to remote endpoint', async () => {
    logService.configure({ enableRemote: true, remoteEndpoint: 'https://api.example.com/logs' });
    logService.error('remote error', { foo: 1 });
    await logService.flushLogs();
    expect(fetch).toHaveBeenCalledWith(
      'https://api.example.com/logs',
      expect.objectContaining({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
    );
  });
}); 