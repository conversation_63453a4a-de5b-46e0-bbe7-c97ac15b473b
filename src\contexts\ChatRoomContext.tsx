import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ChatRoomType, IChatRoomConfig, IChatMessage } from '@/types/chat.types';
import ChatRoomConfigFactory from '@/config/chatroom/ChatRoomConfigFactory';
import { useAppDispatch, useAppSelector } from '@/app/hooks';
import {
  initializeRoomMessages,
  selectRoomMessages,
  selectRoomLoading,
  selectRoomError,
  selectRoomHasNextPage,
  selectRoomCurrentPage,
} from '@/app/slices/messageSlice';
import { logService } from '@/services/system/logService';

/**
 * 聊天室上下文接口
 */
export interface IChatRoomContext {
  // 基本信息
  roomId: string;
  roomType: ChatRoomType;
  config: IChatRoomConfig;
  roomInfo: any;
  
  // 消息相關
  messages: any[];
  isLoading: boolean;
  error: string | null;
  hasNextPage: boolean;
  currentPage: number;
  
  // 操作方法
  sendMessage: (content: string, isImage?: boolean, fileInfo?: any) => Promise<void>;
  loadMoreMessages: () => Promise<void>;
  retryMessage: (messageId: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  
  // UI 狀態
  showTasksBar: boolean;
  showTeamSelector: boolean;
  teamUnreadCount: number;
  memberCount: number;
  
  // 回調函數
  onBackClick?: () => void;
  onTeamChatClick?: () => void;
}

/**
 * 聊天室上下文
 */
const ChatRoomContext = createContext<IChatRoomContext | null>(null);

/**
 * 聊天室上下文提供者屬性
 */
interface ChatRoomProviderProps {
  children: ReactNode;
  roomId: string;
  roomType: ChatRoomType;
  roomInfo?: any;
  onBackClick?: () => void;
  onTeamChatClick?: () => void;
  teamUnreadCount?: number;
  memberCount?: number;
}

/**
 * 聊天室上下文提供者
 */
export const ChatRoomProvider: React.FC<ChatRoomProviderProps> = ({
  children,
  roomId,
  roomType,
  roomInfo,
  onBackClick,
  onTeamChatClick,
  teamUnreadCount = 0,
  memberCount = 0,
}) => {
  const dispatch = useAppDispatch();
  const [config, setConfig] = useState<IChatRoomConfig>(() => 
    ChatRoomConfigFactory.getConfig(roomType)
  );

  // Redux 狀態
  const messages = useAppSelector(selectRoomMessages(roomId));
  const isLoading = useAppSelector(selectRoomLoading(roomId));
  const error = useAppSelector(selectRoomError(roomId));
  const hasNextPage = useAppSelector(selectRoomHasNextPage(roomId));
  const currentPage = useAppSelector(selectRoomCurrentPage(roomId));

  // 初始化消息
  useEffect(() => {
    if (roomId) {
      logService.info('ChatRoomProvider: 初始化房間消息', { roomId, roomType });
      dispatch(initializeRoomMessages({ roomId }));
    }
  }, [roomId, dispatch]);

  // 更新配置（當類型改變時）
  useEffect(() => {
    const newConfig = ChatRoomConfigFactory.getConfig(roomType);
    setConfig(newConfig);
  }, [roomType]);

  // 發送消息
  const sendMessage = async (content: string, isImage = false, fileInfo?: any) => {
    try {
      logService.info('ChatRoomProvider: 發送消息', { roomId, content, isImage });
      // 這裡會調用具體的發送邏輯
      // 暫時使用現有的邏輯，後續會重構
    } catch (error) {
      logService.error('ChatRoomProvider: 發送消息失敗', { error, roomId });
      throw error;
    }
  };

  // 載入更多消息
  const loadMoreMessages = async () => {
    try {
      logService.info('ChatRoomProvider: 載入更多消息', { roomId, currentPage });
      // 實現載入更多消息的邏輯
    } catch (error) {
      logService.error('ChatRoomProvider: 載入更多消息失敗', { error, roomId });
      throw error;
    }
  };

  // 重試消息
  const retryMessage = async (messageId: string) => {
    try {
      logService.info('ChatRoomProvider: 重試消息', { roomId, messageId });
      // 實現重試消息的邏輯
    } catch (error) {
      logService.error('ChatRoomProvider: 重試消息失敗', { error, roomId, messageId });
      throw error;
    }
  };

  // 刪除消息
  const deleteMessage = async (messageId: string) => {
    try {
      logService.info('ChatRoomProvider: 刪除消息', { roomId, messageId });
      // 實現刪除消息的邏輯
    } catch (error) {
      logService.error('ChatRoomProvider: 刪除消息失敗', { error, roomId, messageId });
      throw error;
    }
  };

  const contextValue: IChatRoomContext = {
    // 基本信息
    roomId,
    roomType,
    config,
    roomInfo,
    
    // 消息相關
    messages,
    isLoading,
    error,
    hasNextPage,
    currentPage,
    
    // 操作方法
    sendMessage,
    loadMoreMessages,
    retryMessage,
    deleteMessage,
    
    // UI 狀態
    showTasksBar: config.showTasksBar,
    showTeamSelector: config.showTeamSelector,
    teamUnreadCount,
    memberCount,
    
    // 回調函數
    onBackClick,
    onTeamChatClick,
  };

  return (
    <ChatRoomContext.Provider value={contextValue}>
      {children}
    </ChatRoomContext.Provider>
  );
};

/**
 * 使用聊天室上下文的 Hook
 */
export const useChatRoom = (): IChatRoomContext => {
  const context = useContext(ChatRoomContext);
  if (!context) {
    throw new Error('useChatRoom 必須在 ChatRoomProvider 內部使用');
  }
  return context;
};

export default ChatRoomContext;
