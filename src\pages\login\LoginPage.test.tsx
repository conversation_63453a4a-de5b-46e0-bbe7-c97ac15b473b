import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import LoginPage from './LoginPage';
import authReducer from '../../app/slices/authSlice';
import { ROUTE_OTP_LOGIN } from '../../config/app/routes';

// Mock dependencies
jest.mock('../../services/logService', () => ({
  logService: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

jest.mock('../../services/httpService', () => ({
  default: {
    post: jest.fn(),
  },
}));

// Mock Toast
jest.mock('antd-mobile', () => ({
  ...jest.requireActual('antd-mobile'),
  Toast: {
    show: jest.fn(),
  },
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// 创建模拟 store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    } as any,
    preloadedState: {
      auth: {
        isAuthenticated: false,
        account: null,
        authToken: null,
        sendingOtp: false,
        otpSent: false,
        sendOtpError: null,
        onceToken: null,
        otpValidSeconds: 0,
        loggingIn: false,
        loginError: null,
        loading: false,
        ...initialState,
      },
    },
  });
};

// 渲染测试组件的辅助函数
const renderWithProviders = (ui: React.ReactElement, { initialState = {} } = {}) => {
  const store = createMockStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        <BrowserRouter>
          {ui}
        </BrowserRouter>
      </Provider>
    ),
    store,
  };
};

describe('LoginPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('正確渲染登錄頁面', () => {
    renderWithProviders(<LoginPage />);
    
    // 验证 Aile Logo 存在
    expect(screen.getByAltText('Aile Logo')).toBeInTheDocument();
    
    // 验证标语存在
    expect(screen.getByText('集中沟通窗口，让客服应对更即时')).toBeInTheDocument();
    
    // 验证主图存在
    expect(screen.getByAltText('Aile Platform')).toBeInTheDocument();
    
    // 验证 LINE 登录按钮存在
    expect(screen.getByText('通过 LINE 继续')).toBeInTheDocument();
    
    // 验证其他方式登录按钮存在
    expect(screen.getByText('使用其他方式继续')).toBeInTheDocument();
  });

  test('LINE 登錄按鈕可以點擊', async () => {
    renderWithProviders(<LoginPage />);
    
    const lineButton = screen.getByText('通过 LINE 继续');
    
    // 确保按钮可点击
    expect(lineButton).toBeEnabled();
    
    // 点击按钮
    fireEvent.click(lineButton.closest('button')!);
    
    // 验证按钮被点击（这里可以验证是否调用了相应的 dispatch）
    await waitFor(() => {
      // 这里可以验证 dispatch 是否被调用
      // 或者验证页面状态的变化
    });
  });

  test('其他方式登錄按鈕導航到 OTP 登錄頁面', () => {
    renderWithProviders(<LoginPage />);
    
    const otherButton = screen.getByText('使用其他方式继续');
    fireEvent.click(otherButton);
    
    // 验证导航函数被调用
    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_OTP_LOGIN);
  });

  test('LINE 登錄進行中時顯示載入狀態', () => {
    renderWithProviders(<LoginPage />, {
      initialState: {
        lineLogging: true,
      },
    });
    
    // 验证按钮显示载入状态
    expect(screen.getByText('正在启动 LINE 登录...')).toBeInTheDocument();
    
    // 验证按钮被禁用
    const lineButton = screen.getByText('正在启动 LINE 登录...').closest('button');
    expect(lineButton).toBeDisabled();
    
    // 验证其他方式登录按钮也被禁用
    const otherButton = screen.getByText('使用其他方式继续').closest('button');
    expect(otherButton).toBeDisabled();
  });

  test('顯示 LINE 登錄錯誤時調用 Toast', () => {
    const { Toast } = require('antd-mobile');
    
    renderWithProviders(<LoginPage />, {
      initialState: {
        lineLoginError: 'LINE 登錄失敗',
      },
    });
    
    // 验证 Toast.show 被调用
    expect(Toast.show).toHaveBeenCalledWith({
      icon: 'fail',
      content: 'LINE 登錄失敗',
      duration: 3000,
    });
  });

  test('用戶已認證時不自動跳轉（註釋狀態）', () => {
    renderWithProviders(<LoginPage />, {
      initialState: {
        isAuthenticated: true,
        user: { id: '1', name: 'Test User' },
        token: 'test-token',
      },
    });
    
    // 验证页面仍然渲染（因为跳转代码被注释）
    expect(screen.getByText('通过 LINE 继续')).toBeInTheDocument();
    
    // 这里可以验证 logService.info 被调用
    // 但由于跳转被注释，不验证 navigate 调用
  });

  test('按鈕樣式正確應用', () => {
    renderWithProviders(<LoginPage />);
    
    const lineButton = screen.getByText('通过 LINE 继续').closest('button');
    const otherButton = screen.getByText('使用其他方式继续').closest('button');
    
    // 验证 LINE 按钮样式
    expect(lineButton).toHaveClass('login-page__line-button');
    
    // 验证其他方式按钮样式
    expect(otherButton).toHaveClass('login-page__other-button');
  });

  test('載入狀態時按鈕樣式更新', () => {
    renderWithProviders(<LoginPage />, {
      initialState: {
        lineLogging: true,
      },
    });
    
    const lineButton = screen.getByText('正在启动 LINE 登录...').closest('button');
    
    // 验证载入状态样式被应用
    expect(lineButton).toHaveClass('login-page__line-button--loading');
  });
}); 