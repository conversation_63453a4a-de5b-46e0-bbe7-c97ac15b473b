import { messageService, SendMessageRequest, SyncMessageResponse, MessageData, Type } from './messageService';
import httpService from '../../system/httpService';
import { CryptoUtil } from '../../../utils/cryptoUtil';
import { of } from 'rxjs';
import { ConstantUtil } from '../../../utils/constantUtil';
import aileDBService from '../../db/aileDBService';
import * as storage from '../../../utils/storage';

// Mock dependencies
jest.mock('../../system/httpService', () => ({
  post: jest.fn()
}));

jest.mock('../../system/logService', () => ({
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

jest.mock('../../../utils/cryptoUtil', () => ({
  CryptoUtil: {
    decryptApiResponse: jest.fn()
  }
}));

jest.mock('../../db/aileDBService', () => ({
  isInitialized: jest.fn(),
  initForAccount: jest.fn(),
  get: jest.fn(),
  run: jest.fn()
}));

jest.mock('../../../utils/storage', () => ({
  getLocalStorage: jest.fn()
}));

describe('MessageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchChatMessageSend', () => {
    it('should send a message successfully', async () => {
      // Mock API response
      const mockResponse = {
        success: true,
        data: {
          messageId: 'msg123',
          sendTime: '2023-06-01T12:00:00Z',
          status: 'sent'
        }
      };

      // Setup mocks
      (httpService.post as jest.Mock).mockReturnValue(of('encrypted-response'));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockResponse);

      // Test request
      const request: SendMessageRequest = {
        roomId: 'room123',
        content: 'Hello world',
        type: Type.Text
      };

      // Execute
      const result = await messageService.fetchChatMessageSend(request);

      // Verify
      expect(httpService.post).toHaveBeenCalledWith(
        ConstantUtil.API_CHAT_MESSAGE_SEND,
        {
          roomId: 'room123',
          content: 'Hello world',
          type: 'text',
          metadata: {},
          replyToId: undefined
        },
        {},
        true
      );
      expect(CryptoUtil.decryptApiResponse).toHaveBeenCalledWith('encrypted-response');
      expect(result).toEqual(mockResponse);
    });

    it('should handle API error', async () => {
      // Mock API error
      (httpService.post as jest.Mock).mockReturnValue(of('encrypted-error'));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue({
        success: false,
        msg: 'Failed to send message',
        code: 'ERROR_CODE'
      });

      // Test request
      const request: SendMessageRequest = {
        roomId: 'room123',
        content: 'Hello world'
      };

      // Execute
      const result = await messageService.fetchChatMessageSend(request);

      // Verify
      expect(result).toEqual({
        success: false,
        msg: 'Failed to send message',
        code: 'ERROR_CODE'
      });
    });

    it('should handle decryption error', async () => {
      // Mock API response with decryption error
      (httpService.post as jest.Mock).mockReturnValue(of({ success: false, msg: 'Raw error' }));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockImplementation(() => {
        throw new Error('Decryption failed');
      });

      // Test request
      const request: SendMessageRequest = {
        roomId: 'room123',
        content: 'Hello world'
      };

      // Execute
      const result = await messageService.fetchChatMessageSend(request);

      // Verify
      expect(result).toEqual({
        success: false,
        msg: 'Raw error'
      });
    });

    it('should handle network error', async () => {
      // Mock network error
      const networkError = new Error('Network error');
      (httpService.post as jest.Mock).mockImplementation(() => {
        throw networkError;
      });

      // Test request
      const request: SendMessageRequest = {
        roomId: 'room123',
        content: 'Hello world'
      };

      // Execute
      const result = await messageService.fetchChatMessageSend(request);

      // Verify
      expect(result).toEqual({
        success: false,
        msg: 'Network error',
        code: '-1'
      });
    });
  });

  describe('fetchBaseSyncMessage', () => {
    it('should fetch messages successfully', async () => {
      // Mock API response
      const mockResponse: SyncMessageResponse = {
        success: true,
        data: {
          items: [
            {
              id: 'msg1',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello',
              type: 'text',
              sendTime: '2023-06-01T12:00:00Z',
              status: 'sent',
              sequence: 1
            }
          ],
          total: 1,
          hasMore: false,
          hasNextPage: false,
          refreshTime: *************
        }
      };

      // Setup mocks
      (httpService.post as jest.Mock).mockReturnValue(of('encrypted-response'));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue(mockResponse);

      // Test request
      const request = {
        roomId: 'room123',
        pageIndex: 0,
        pageSize: 20
      };

      // Execute
      const result = await messageService.fetchBaseSyncMessage(request);

      // Verify
      expect(httpService.post).toHaveBeenCalledWith(
        ConstantUtil.API_BASE_SYNC_MESSAGE,
        {
          roomId: 'room123',
          pageIndex: 0,
          pageSize: 20,
          lastMessageId: undefined,
          direction: 'desc',
          refreshTime: undefined
        },
        {},
        true
      );
      expect(CryptoUtil.decryptApiResponse).toHaveBeenCalledWith('encrypted-response');
      expect(result).toEqual(mockResponse);
    });

    it('should handle API error', async () => {
      // Mock API error
      (httpService.post as jest.Mock).mockReturnValue(of('encrypted-error'));
      (CryptoUtil.decryptApiResponse as jest.Mock).mockReturnValue({
        success: false,
        msg: 'Failed to fetch messages',
        code: 'ERROR_CODE'
      });

      // Test request
      const request = {
        roomId: 'room123'
      };

      // Execute
      const result = await messageService.fetchBaseSyncMessage(request);

      // Verify
      expect(result).toEqual({
        success: false,
        msg: 'Failed to fetch messages',
        code: 'ERROR_CODE'
      });
    });
  });

  describe('fetchAllMessages', () => {
    it('should fetch all messages across multiple pages', async () => {
      // Mock responses for two pages
      const mockResponsePage1: SyncMessageResponse = {
        success: true,
        data: {
          items: [
            {
              id: 'msg1',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello',
              type: 'text',
              sendTime: '2023-06-01T12:00:00Z',
              status: 'sent',
              updateTime: 1000,
              sequence: 1
            },
            {
              id: 'msg2',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello 2',
              type: 'text',
              sendTime: '2023-06-01T12:01:00Z',
              status: 'sent',
              updateTime: 2000,
              sequence: 2
            }
          ],
          total: 4,
          hasMore: true,
          hasNextPage: true,
          refreshTime: 2000
        }
      };

      const mockResponsePage2: SyncMessageResponse = {
        success: true,
        data: {
          items: [
            {
              id: 'msg3',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello 3',
              type: 'text',
              sendTime: '2023-06-01T12:02:00Z',
              status: 'sent',
              updateTime: 3000,
              sequence: 3
            },
            {
              id: 'msg4',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello 4',
              type: 'text',
              sendTime: '2023-06-01T12:03:00Z',
              status: 'sent',
              updateTime: 4000,
              sequence: 4
            }
          ],
          total: 4,
          hasMore: false,
          hasNextPage: false,
          refreshTime: 4000
        }
      };

      // Setup spy on fetchBaseSyncMessage
      jest.spyOn(messageService, 'fetchBaseSyncMessage')
        .mockResolvedValueOnce(mockResponsePage1)
        .mockResolvedValueOnce(mockResponsePage2);

      // Execute
      const result = await messageService.fetchAllMessages('room123', 0, 2, 5);

      // Verify
      expect(messageService.fetchBaseSyncMessage).toHaveBeenCalledTimes(2);
      expect(messageService.fetchBaseSyncMessage).toHaveBeenCalledWith({
        roomId: 'room123',
        pageIndex: 0,
        pageSize: 2,
        refreshTime: 0,
        direction: 'desc'
      });
      expect(messageService.fetchBaseSyncMessage).toHaveBeenCalledWith({
        roomId: 'room123',
        pageIndex: 1,
        pageSize: 2,
        refreshTime: 2000,
        direction: 'desc'
      });

      expect(result).toEqual({
        allMessages: [
          expect.objectContaining({ id: 'msg1', updateTime: 1000 }),
          expect.objectContaining({ id: 'msg2', updateTime: 2000 }),
          expect.objectContaining({ id: 'msg3', updateTime: 3000 }),
          expect.objectContaining({ id: 'msg4', updateTime: 4000 })
        ],
        latestRefreshTime: 4000,
        success: true,
        errorMsg: undefined,
        savedCount: undefined
      });
    });

    it('should stop fetching when API returns error', async () => {
      // Mock responses with error on second page
      const mockResponsePage1: SyncMessageResponse = {
        success: true,
        data: {
          items: [
            {
              id: 'msg1',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello',
              type: 'text',
              sendTime: '2023-06-01T12:00:00Z',
              status: 'sent',
              updateTime: 1000,
              sequence: 1
            },
            {
              id: 'msg2',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello 2',
              type: 'text',
              sendTime: '2023-06-01T12:01:00Z',
              status: 'sent',
              updateTime: 2000,
              sequence: 2
            }
          ],
          total: 4,
          hasMore: true,
          hasNextPage: true,
          refreshTime: 2000
        }
      };

      const mockResponsePage2: SyncMessageResponse = {
        success: false,
        msg: 'API error',
        code: 'ERROR_CODE'
      };

      // Setup spy on fetchBaseSyncMessage
      jest.spyOn(messageService, 'fetchBaseSyncMessage')
        .mockResolvedValueOnce(mockResponsePage1)
        .mockResolvedValueOnce(mockResponsePage2);

      // Execute
      const result = await messageService.fetchAllMessages('room123', 0, 2, 5);

      // Verify
      expect(messageService.fetchBaseSyncMessage).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        allMessages: [
          expect.objectContaining({ id: 'msg1', updateTime: 1000 }),
          expect.objectContaining({ id: 'msg2', updateTime: 2000 })
        ],
        latestRefreshTime: 2000,
        success: false,
        errorMsg: 'API error',
        savedCount: undefined
      });
    });

    it('should stop fetching when max pages reached', async () => {
      // Mock responses for all pages returning hasNextPage=true
      const mockResponse: SyncMessageResponse = {
        success: true,
        data: {
          items: [
            {
              id: 'msg',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello',
              type: 'text',
              sendTime: '2023-06-01T12:00:00Z',
              status: 'sent',
              updateTime: 1000,
              sequence: 1
            }
          ],
          total: 100,
          hasMore: true,
          hasNextPage: true,
          refreshTime: 1000
        }
      };

      // Setup spy on fetchBaseSyncMessage
      jest.spyOn(messageService, 'fetchBaseSyncMessage')
        .mockResolvedValue(mockResponse);

      // Execute with maxPages=3
      const result = await messageService.fetchAllMessages('room123', 0, 1, 3);

      // Verify
      expect(messageService.fetchBaseSyncMessage).toHaveBeenCalledTimes(3);
      expect(result.allMessages.length).toBe(3);
      expect(result.success).toBe(true);
    });

    it('should save messages to database when saveToDb is true', async () => {
      // Mock responses
      const mockResponse: SyncMessageResponse = {
        success: true,
        data: {
          items: [
            {
              id: 'msg1',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello',
              type: 'text',
              sendTime: '2023-06-01T12:00:00Z',
              status: 'sent',
              updateTime: 1000,
              sequence: 1
            },
            {
              id: 'msg2',
              roomId: 'room123',
              senderId: 'user1',
              senderName: 'User One',
              content: 'Hello 2',
              type: 'text',
              sendTime: '2023-06-01T12:01:00Z',
              status: 'sent',
              updateTime: 2000,
              sequence: 2
            }
          ],
          total: 2,
          hasMore: false,
          hasNextPage: false,
          refreshTime: 2000
        }
      };

      // Setup spies
      jest.spyOn(messageService, 'fetchBaseSyncMessage')
        .mockResolvedValue(mockResponse);
      
      jest.spyOn(messageService, 'saveMessagesToDb')
        .mockResolvedValue({ successCount: 2, failedCount: 0, errors: [] });

      // Execute with saveToDb=true
      const result = await messageService.fetchAllMessages('room123', 0, 2, 5, true);

      // Verify
      expect(messageService.fetchBaseSyncMessage).toHaveBeenCalledTimes(1);
      expect(messageService.saveMessagesToDb).toHaveBeenCalledTimes(1);
      
      // Make sure we're not accessing mockResponse.data if it might be undefined
      if (mockResponse.data) {
        expect(messageService.saveMessagesToDb).toHaveBeenCalledWith(mockResponse.data.items);
        expect(result).toEqual({
          allMessages: mockResponse.data.items,
          latestRefreshTime: 2000,
          success: true,
          errorMsg: undefined,
          savedCount: 2
        });
      }
    });
  });

  describe('saveMessagesToDb', () => {
    const mockMessages: MessageData[] = [
      {
        id: 'msg1',
        roomId: 'room123',
        senderId: 'user1',
        senderName: 'User One',
        content: 'Hello',
        type: 'text',
        sendTime: '2023-06-01T12:00:00Z',
        status: 'sent',
        updateTime: *************,
        sequence: 1
      },
      {
        id: 'msg2',
        roomId: 'room123',
        senderId: 'user2',
        senderName: 'User Two',
        content: 'Hi there',
        type: 'text',
        sendTime: '2023-06-01T12:01:00Z',
        status: 'sent',
        updateTime: *************,
        sequence: 2
      }
    ];

    beforeEach(() => {
      // Setup storage mocks
      (storage.getLocalStorage as jest.Mock).mockImplementation((key) => {
        if (key === ConstantUtil.ACCOUNT_ID_KEY) return 'account123';
        if (key === ConstantUtil.CURRENT_TENANT_ID_KEY) return 'tenant123';
        return null;
      });

      // Setup DB mocks
      (aileDBService.isInitialized as jest.Mock).mockReturnValue(true);
    });

    it('should save new messages to database', async () => {
      // Mock DB responses
      (aileDBService.get as jest.Mock).mockResolvedValue(null); // Message doesn't exist
      (aileDBService.run as jest.Mock).mockResolvedValue(1); // 1 row affected

      // Execute
      const result = await messageService.saveMessagesToDb(mockMessages);

      // Verify
      expect(storage.getLocalStorage).toHaveBeenCalledWith(ConstantUtil.ACCOUNT_ID_KEY, null);
      expect(storage.getLocalStorage).toHaveBeenCalledWith(ConstantUtil.CURRENT_TENANT_ID_KEY, null);
      expect(aileDBService.get).toHaveBeenCalledTimes(2);
      expect(aileDBService.run).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        successCount: 2,
        failedCount: 0,
        errors: []
      });
    });

    it('should update existing messages in database', async () => {
      // Mock DB responses
      (aileDBService.get as jest.Mock).mockResolvedValue({ id: 'msg1' }); // Message exists
      (aileDBService.run as jest.Mock).mockResolvedValue(1); // 1 row affected

      // Execute
      const result = await messageService.saveMessagesToDb([mockMessages[0]]);

      // Verify
      expect(aileDBService.get).toHaveBeenCalledTimes(1);
      expect(aileDBService.run).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        successCount: 1,
        failedCount: 0,
        errors: []
      });
    });

    it('should handle database errors', async () => {
      // Mock DB error
      const dbError = new Error('Database error');
      (aileDBService.get as jest.Mock).mockRejectedValue(dbError);

      // Execute
      const result = await messageService.saveMessagesToDb(mockMessages);

      // Verify
      expect(result).toEqual({
        successCount: 0,
        failedCount: 2,
        errors: [dbError, dbError]
      });
    });

    it('should handle missing account or tenant ID', async () => {
      // Mock missing account ID
      (storage.getLocalStorage as jest.Mock).mockReturnValue(null);

      // Execute
      const result = await messageService.saveMessagesToDb(mockMessages);

      // Verify
      expect(result).toEqual({
        successCount: 0,
        failedCount: 2,
        errors: [expect.any(Error)]
      });
      expect(result.errors[0].message).toContain('缺少賬號ID或租戶ID');
    });

    it('should initialize database if not initialized', async () => {
      // Mock DB not initialized
      (aileDBService.isInitialized as jest.Mock).mockReturnValue(false);
      (aileDBService.initForAccount as jest.Mock).mockResolvedValue(true);
      (aileDBService.get as jest.Mock).mockResolvedValue(null);
      (aileDBService.run as jest.Mock).mockResolvedValue(1);

      // Execute
      const result = await messageService.saveMessagesToDb(mockMessages);

      // Verify
      expect(aileDBService.initForAccount).toHaveBeenCalledWith('account123');
      expect(result).toEqual({
        successCount: 2,
        failedCount: 0,
        errors: []
      });
    });

    it('should handle database initialization failure', async () => {
      // Mock DB initialization failure
      (aileDBService.isInitialized as jest.Mock).mockReturnValue(false);
      (aileDBService.initForAccount as jest.Mock).mockRejectedValue(new Error('Init failed'));

      // Execute
      const result = await messageService.saveMessagesToDb(mockMessages);

      // Verify
      expect(result).toEqual({
        successCount: 0,
        failedCount: 2,
        errors: [expect.any(Error)]
      });
    });
  });

  describe('syncMessagesToDb', () => {
    it('should sync messages to database successfully', async () => {
      // Mock fetchAllMessages response
      const mockFetchResult = {
        allMessages: [
          { 
            id: 'msg1',
            roomId: 'room123',
            senderId: 'user1',
            senderName: 'User One',
            content: 'Hello',
            type: 'text',
            sendTime: '2023-06-01T12:00:00Z',
            status: 'sent'
          }, 
          { 
            id: 'msg2',
            roomId: 'room123',
            senderId: 'user2',
            senderName: 'User Two',
            content: 'Hi there',
            type: 'text',
            sendTime: '2023-06-01T12:01:00Z',
            status: 'sent'
          }
        ],
        latestRefreshTime: *************,
        success: true,
        savedCount: 2
      };

      jest.spyOn(messageService, 'fetchAllMessages')
        .mockResolvedValue(mockFetchResult as any);

      // Execute
      const result = await messageService.syncMessagesToDb('room123', 0, 50, 10);

      // Verify
      expect(messageService.fetchAllMessages).toHaveBeenCalledWith(
        'room123',
        0,
        50,
        10,
        true
      );
      expect(result).toEqual({
        success: true,
        savedCount: 2,
        totalMessages: 2,
        latestRefreshTime: *************,
        errorMsg: undefined
      });
    });

    it('should handle sync errors', async () => {
      // Mock fetchAllMessages error
      const mockError = new Error('Sync failed');
      jest.spyOn(messageService, 'fetchAllMessages')
        .mockRejectedValue(mockError);

      // Execute
      const result = await messageService.syncMessagesToDb('room123');

      // Verify
      expect(result).toEqual({
        success: false,
        savedCount: 0,
        totalMessages: 0,
        latestRefreshTime: 0,
        errorMsg: 'Sync failed'
      });
    });

    it('should handle partial sync success', async () => {
      // Mock fetchAllMessages partial success
      const mockFetchResult = {
        allMessages: [
          { 
            id: 'msg1',
            roomId: 'room123',
            senderId: 'user1',
            senderName: 'User One',
            content: 'Hello',
            type: 'text',
            sendTime: '2023-06-01T12:00:00Z',
            status: 'sent'
          }, 
          { 
            id: 'msg2',
            roomId: 'room123',
            senderId: 'user2',
            senderName: 'User Two',
            content: 'Hi there',
            type: 'text',
            sendTime: '2023-06-01T12:01:00Z',
            status: 'sent'
          }
        ],
        latestRefreshTime: *************,
        success: false,
        errorMsg: 'Partial failure',
        savedCount: 1
      };

      jest.spyOn(messageService, 'fetchAllMessages')
        .mockResolvedValue(mockFetchResult as any);

      // Execute
      const result = await messageService.syncMessagesToDb('room123');

      // Verify
      expect(result).toEqual({
        success: false,
        savedCount: 1,
        totalMessages: 2,
        latestRefreshTime: *************,
        errorMsg: 'Partial failure'
      });
    });
  });
}); 