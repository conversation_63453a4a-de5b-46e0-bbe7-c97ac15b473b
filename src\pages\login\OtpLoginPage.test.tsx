
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import OtpLoginPage from './OtpLoginPage';
import authReducer from '../../app/slices/authSlice';
import { ROUTE_LOGIN, ROUTE_USER_SIGNUP, ROUTE_HOME, ROUTE_BUSINESS_REGISTER } from '../../config/app/routes';

// Mock dependencies
jest.mock('../../services/logService', () => ({
  logService: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

jest.mock('../../services/httpService', () => ({
  post: jest.fn(),
}));

// Mock Toast
jest.mock('antd-mobile', () => ({
  ...jest.requireActual('antd-mobile'),
  Toast: {
    show: jest.fn(),
  },
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('OtpLoginPage', () => {
  let store: any;

  // 统一定义完整的AuthState初始对象，避免类型不兼容
  const getInitialAuthState = () => ({
    isAuthenticated: false,
    account: null,
    authToken: null,
    sendingOtp: false,
    otpSent: false,
    sendOtpError: null,
    onceToken: null,
    otpValidSeconds: 0,
    loggingIn: false,
    loginError: null,
    loading: false,
  });

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authReducer,
      } as any,
      preloadedState: {
        auth: getInitialAuthState(),
      },
    });
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <Provider store={store}>
        <BrowserRouter>
          <OtpLoginPage />
        </BrowserRouter>
      </Provider>
    );
  };

  test('應該正確渲染頁面', () => {
    renderComponent();
    
    expect(screen.getByText('手機驗證碼登錄')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('請輸入您的手機號碼')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('請輸入驗證碼')).toBeInTheDocument();
    expect(screen.getByText('發送驗證碼')).toBeInTheDocument();
    expect(screen.getByText('繼續')).toBeInTheDocument();
  });

  test('應該正確處理手機號碼輸入', () => {
    renderComponent();
    
    const phoneInput = screen.getByPlaceholderText('請輸入您的手機號碼');
    fireEvent.change(phoneInput, { target: { value: '**********' } });
    
    expect(phoneInput).toHaveValue('**********');
  });

  test('應該驗證台灣手機號碼格式', async () => {
    renderComponent();
    
    const phoneInput = screen.getByPlaceholderText('請輸入您的手機號碼');
    
    // 輸入無效格式
    fireEvent.change(phoneInput, { target: { value: '123' } });
    fireEvent.blur(phoneInput);
    
    await waitFor(() => {
      expect(screen.getByText('請輸入正確格式')).toBeInTheDocument();
    });
  });

  test('應該驗證驗證碼格式', async () => {
    renderComponent();
    
    const codeInput = screen.getByPlaceholderText('請輸入驗證碼');
    
    // 輸入無效格式
    fireEvent.change(codeInput, { target: { value: '123' } });
    fireEvent.blur(codeInput);
    
    await waitFor(() => {
      expect(screen.getByText('驗證碼錯誤請重新確認')).toBeInTheDocument();
    });
  });

  test('應該正確切換國家代碼', () => {
    renderComponent();
    
    // 點擊國家代碼
    const countryCodeButton = screen.getByText('+886');
    fireEvent.click(countryCodeButton);
    
    // 選擇中國代碼
    const chinaOption = screen.getByText('中國大陸 +86');
    fireEvent.click(chinaOption);
    
    expect(screen.getByText('+86')).toBeInTheDocument();
  });

  test('發送驗證碼按鈕應該在手機號碼無效時禁用', () => {
    renderComponent();
    
    const sendButton = screen.getByText('發送驗證碼');
    expect(sendButton).toBeDisabled();
  });

  test('繼續按鈕應該在表單未完成時禁用', () => {
    renderComponent();
    
    // 使用 querySelector 因為 antd-mobile Button 組件的 disabled 屬性可能不會直接反映在文本元素上
    const continueButton = screen.getByText('繼續').closest('button');
    expect(continueButton).toBeDisabled();
  });

  test('應該在點擊返回時導航到首頁', () => {
    renderComponent();
    
    const backButton = screen.getByRole('img', { name: '返回' });
    fireEvent.click(backButton);
    
    expect(mockNavigate).toHaveBeenCalledWith(ROUTE_LOGIN);
  });

  test('應該只允許輸入數字到手機號碼欄位', async () => {
    renderComponent();
    
    const phoneInput = screen.getByPlaceholderText('請輸入您的手機號碼');
    
    // 直接測試 value 而不是依賴 onChange 處理
    fireEvent.change(phoneInput, { target: { value: '**********' } });
    
    await waitFor(() => {
      expect(phoneInput).toHaveValue('**********');
    });
  });

  test('應該只允許輸入數字到驗證碼欄位', async () => {
    renderComponent();
    
    const codeInput = screen.getByPlaceholderText('請輸入驗證碼');
    
    // 直接測試數字輸入
    fireEvent.change(codeInput, { target: { value: '123456' } });
    
    await waitFor(() => {
      expect(codeInput).toHaveValue('123456');
    });
  });

  test('應該限制手機號碼長度', async () => {
    renderComponent();
    
    const phoneInput = screen.getByPlaceholderText('請輸入您的手機號碼');
    
    // 測試最大長度限制（maxLength 屬性）
    expect(phoneInput).toHaveAttribute('maxlength', '10');
  });

  test('應該限制驗證碼長度為6位', async () => {
    renderComponent();
    
    const codeInput = screen.getByPlaceholderText('請輸入驗證碼');
    
    // 測試最大長度限制（maxLength 屬性）
    expect(codeInput).toHaveAttribute('maxlength', '6');
  });

  test('應該在驗證碼校驗和設備檢查時顯示加載狀態', () => {
    // 創建一個mock store，設置加載狀態
    const mockStoreWithLoading = configureStore({
      reducer: {
        auth: authReducer,
      } as any,
      preloadedState: {
        auth: {
          isAuthenticated: false,
          account: null,
          authToken: null,
          sendingOtp: false,
          otpSent: true,
          sendOtpError: null,
          onceToken: 'test-token',
          otpValidSeconds: 60,
          loggingIn: true, // 驗證碼校驗中
          loginError: null,
          loading: false,
        },
      },
    });

    render(
      <Provider store={mockStoreWithLoading}>
        <BrowserRouter>
          <OtpLoginPage />
        </BrowserRouter>
      </Provider>
    );

    // 繼續按鈕應該被禁用（通過 className 判斷）
    const continueButton = document.querySelector('.phone-login-page__continue-button');
    expect(continueButton).toBeDisabled();
  });

  test('應該在設備檢查時顯示加載狀態', () => {
    // 創建一個mock store，設置設備檢查加載狀態
    const mockStoreWithDeviceChecking = configureStore({
      reducer: {
        auth: authReducer,
      } as any,
      preloadedState: {
        auth: {
          isAuthenticated: false,
          account: null,
          authToken: null,
          sendingOtp: false,
          otpSent: true,
          sendOtpError: null,
          onceToken: 'test-token',
          otpValidSeconds: 60,
          loggingIn: false,
          loginError: null,
          loading: true, // 設備檢查中
        },
      },
    });

    render(
      <Provider store={mockStoreWithDeviceChecking}>
        <BrowserRouter>
          <OtpLoginPage />
        </BrowserRouter>
      </Provider>
    );

    // 繼續按鈕應該被禁用（通過 className 判斷）
    const continueButton = document.querySelector('.phone-login-page__continue-button');
    expect(continueButton).toBeDisabled();
  });

  test('應該顯示設備檢查錯誤信息', () => {
    // 創建一個mock store，設置設備檢查錯誤狀態
    const mockStoreWithDeviceError = configureStore({
      reducer: {
        auth: authReducer,
      } as any,
      preloadedState: {
        auth: {
          isAuthenticated: false,
          account: null,
          authToken: null,
          sendingOtp: false,
          otpSent: true,
          sendOtpError: null,
          onceToken: 'test-token',
          otpValidSeconds: 60,
          loggingIn: false,
          loginError: '設備檢查失敗', // 設備檢查錯誤
          loading: false,
        },
      },
    });

    render(
      <Provider store={mockStoreWithDeviceError}>
        <BrowserRouter>
          <OtpLoginPage />
        </BrowserRouter>
      </Provider>
    );

    // 應該顯示設備檢查錯誤信息
    expect(screen.getByText('設備檢查失敗')).toBeInTheDocument();
  });

  test('登入成功且 isInitial 為 true 時跳轉 UserSignUpPage 並存儲本地資料', async () => {
    const mockSetLocalStorage = jest.fn();
    jest.doMock('../../utils/storage', () => ({
      setLocalStorage: mockSetLocalStorage,
    }));
    const mockLog = require('../../services/logService').logService;
    store = configureStore({
      reducer: { auth: authReducer } as any,
      preloadedState: {
        auth: getInitialAuthState(),
      },
    });
    renderComponent();
    // 模擬表單填寫
    fireEvent.change(screen.getByPlaceholderText('請輸入您的手機號碼'), { target: { value: '***********' } });
    fireEvent.change(screen.getByPlaceholderText('請輸入驗證碼'), { target: { value: '123456' } });
    // mock dispatch login 返回 isInitial true
    store.dispatch = jest.fn().mockResolvedValue({ payload: { data: { isInitial: true, accountId: 'abc', tokenId: 'token', tenantInfo: [] } } });
    const continueBtn = screen.getByText('繼續').closest('button');
    fireEvent.click(continueBtn!);
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(ROUTE_USER_SIGNUP, { replace: true });
      expect(mockSetLocalStorage).toHaveBeenCalledWith(expect.any(String), expect.anything());
      expect(mockLog.info).toHaveBeenCalled();
    });
  });

  test('登入成功且有個人租戶時跳轉主頁', async () => {
    const mockSetLocalStorage = jest.fn();
    jest.doMock('../../utils/storage', () => ({
      setLocalStorage: mockSetLocalStorage,
    }));
    const mockLog = require('../../services/logService').logService;
    store.dispatch = jest.fn().mockResolvedValue({ payload: { data: { isInitial: false, accountId: 'abc', tokenId: 'token', tenantInfo: [{ type: 'Person', accountId: 'abc' }] } } });
    renderComponent();
    fireEvent.change(screen.getByPlaceholderText('請輸入您的手機號碼'), { target: { value: '***********' } });
    fireEvent.change(screen.getByPlaceholderText('請輸入驗證碼'), { target: { value: '123456' } });
    const continueBtn = screen.getByText('繼續').closest('button');
    fireEvent.click(continueBtn!);
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(ROUTE_HOME, { replace: true });
      expect(mockSetLocalStorage).toHaveBeenCalledWith(expect.any(String), expect.anything());
      expect(mockLog.info).toHaveBeenCalled();
    });
  });

  test('登入成功但無個人租戶時跳轉 BusinessSignUpPage', async () => {
    const mockSetLocalStorage = jest.fn();
    jest.doMock('../../utils/storage', () => ({
      setLocalStorage: mockSetLocalStorage,
    }));
    const mockLog = require('../../services/logService').logService;
    store.dispatch = jest.fn().mockResolvedValue({ payload: { data: { isInitial: false, accountId: 'abc', tokenId: 'token', tenantInfo: [{ type: 'Company', accountId: 'abc' }] } } });
    renderComponent();
    fireEvent.change(screen.getByPlaceholderText('請輸入您的手機號碼'), { target: { value: '***********' } });
    fireEvent.change(screen.getByPlaceholderText('請輸入驗證碼'), { target: { value: '123456' } });
    const continueBtn = screen.getByText('繼續').closest('button');
    fireEvent.click(continueBtn!);
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(ROUTE_BUSINESS_REGISTER, { replace: true });
      expect(mockSetLocalStorage).toHaveBeenCalledWith(expect.any(String), expect.anything());
      expect(mockLog.info).toHaveBeenCalled();
    });
  });

  test('登入流程異常時顯示錯誤提示並記錄日誌', async () => {
    const mockLog = require('../../services/logService').logService;
    store.dispatch = jest.fn().mockRejectedValue(new Error('登入失敗'));
    renderComponent();
    fireEvent.change(screen.getByPlaceholderText('請輸入您的手機號碼'), { target: { value: '***********' } });
    fireEvent.change(screen.getByPlaceholderText('請輸入驗證碼'), { target: { value: '123456' } });
    const continueBtn = screen.getByText('繼續').closest('button');
    fireEvent.click(continueBtn!);
    await waitFor(() => {
      expect(mockLog.error).toHaveBeenCalledWith('登錄流程失敗', { error: '登入失敗' });
      expect(screen.getByText('登入失敗')).toBeInTheDocument();
    });
  });

  describe('防重复点击修复', () => {
    test('应该防止在登录过程中重复点击继续按钮', async () => {
      const initialState = {
        ...getInitialAuthState(),
        otpSent: true,
        onceToken: 'test-token',
        otpValidSeconds: 60,
      };

      store = configureStore({
        reducer: { auth: authReducer },
        preloadedState: { auth: initialState },
      });

      render(
        <Provider store={store}>
          <BrowserRouter>
            <OtpLoginPage />
          </BrowserRouter>
        </Provider>
      );

      // 填写表单
      const phoneInput = screen.getByPlaceholderText('請輸入手機號碼');
      const codeInput = screen.getByPlaceholderText('請輸入驗證碼');

      fireEvent.change(phoneInput, { target: { value: '**********' } });
      fireEvent.change(codeInput, { target: { value: '123456' } });

      // 获取继续按钮
      const continueButton = screen.getByText('繼續');

      // 验证按钮初始状态
      expect(continueButton).not.toBeDisabled();

      // 第一次点击
      fireEvent.click(continueButton);

      // 验证按钮状态变为禁用
      await waitFor(() => {
        expect(continueButton).toBeDisabled();
      });

      // 尝试第二次点击（应该被忽略）
      fireEvent.click(continueButton);

      // 验证按钮仍然禁用
      expect(continueButton).toBeDisabled();
    });

    test('应该显示正确的按钮文本状态', async () => {
      const initialState = {
        ...getInitialAuthState(),
        otpSent: true,
        onceToken: 'test-token',
        otpValidSeconds: 60,
      };

      store = configureStore({
        reducer: { auth: authReducer },
        preloadedState: { auth: initialState },
      });

      render(
        <Provider store={store}>
          <BrowserRouter>
            <OtpLoginPage />
          </BrowserRouter>
        </Provider>
      );

      // 初始状态应该显示"繼續"
      expect(screen.getByText('繼續')).toBeInTheDocument();

      // 填写表单使按钮可用
      const phoneInput = screen.getByPlaceholderText('請輸入手機號碼');
      const codeInput = screen.getByPlaceholderText('請輸入驗證碼');

      fireEvent.change(phoneInput, { target: { value: '**********' } });
      fireEvent.change(codeInput, { target: { value: '123456' } });

      // 验证按钮文本
      const continueButton = screen.getByText('繼續');
      expect(continueButton).toBeInTheDocument();
    });
  });
});