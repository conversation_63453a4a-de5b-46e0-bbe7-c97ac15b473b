import { downloadAndCacheAvatarImage } from './avatarImageUtil';
import { getUserForage } from './userForage';
import { ConstantUtil } from './constantUtil';
import { attachmentService } from '@/services/core/attachment/attachmentService';
import stateService from '@/services/stateService';

jest.mock('./userForage');
jest.mock('@/services/core/attachment/attachmentService');
jest.mock('@/services/stateService');

const mockForage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
};
(getUserForage as jest.Mock).mockReturnValue(mockForage);

const avatarId = 'avatar1';
const size = 'S';
const key = ConstantUtil.AVATAR_IMAGE_CACHE_KEY(avatarId, size);

// 模擬 stateService.loginAccount
(stateService.loginAccount as jest.Mock).mockReturnValue({
  accountId: 'user1',
  name: 'Test User'
});

describe('downloadAndCacheAvatarImage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('快取命中直接返回', async () => {
    mockForage.getItem.mockResolvedValue('base64img');
    const result = await downloadAndCacheAvatarImage(avatarId, size);
    expect(result).toBe('base64img');
    expect(mockForage.getItem).toHaveBeenCalledWith(key);
  });

  it('下載成功並快取', async () => {
    mockForage.getItem.mockResolvedValue(null);
    const mockBlob = new Blob(['test'], { type: 'image/png' });
    (attachmentService.fetchAvatarAttachment as jest.Mock).mockReturnValue({
      subscribe: ({ next }: any) => {
        next({ status: 'done', data: mockBlob });
      },
    });
    (ConstantUtil.blobToBase64 as jest.Mock).mockResolvedValue('base64img');
    const result = await downloadAndCacheAvatarImage(avatarId, size);
    expect(result).toBe('base64img');
    expect(mockForage.setItem).toHaveBeenCalledWith(key, 'base64img');
  });

  it('下載失敗返回 null', async () => {
    mockForage.getItem.mockResolvedValue(null);
    (attachmentService.fetchAvatarAttachment as jest.Mock).mockReturnValue({
      subscribe: ({ next, error }: any) => {
        next({ status: 'error', message: 'fail' });
        error && error('fail');
      },
    });
    (ConstantUtil.blobToBase64 as jest.Mock).mockResolvedValue('base64img');
    const result = await downloadAndCacheAvatarImage(avatarId, size);
    expect(result).toBeNull();
  });
  
  it('未獲取到 accountId 時返回 null', async () => {
    (stateService.loginAccount as jest.Mock).mockReturnValueOnce(null);
    const result = await downloadAndCacheAvatarImage(avatarId, size);
    expect(result).toBeNull();
  });
}); 