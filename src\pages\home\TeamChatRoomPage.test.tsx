
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import TeamChatRoomPage from './TeamChatRoomPage';

jest.mock('./chat/ChatRoom', () => {
  return jest.fn(({ title, memberCount }) => (
    <div>
      <h1>{title}</h1>
      <span>{memberCount}</span>
    </div>
  ));
});

describe('TeamChatRoomPage', () => {
  it('renders the chat room with correct title and member count', () => {
    render(
      <MemoryRouter>
        <TeamChatRoomPage />
      </MemoryRouter>
    );

    expect(screen.getByText('團隊聊天室')).toBeInTheDocument();
    expect(screen.getByText('8')).toBeInTheDocument();
  });
}); 