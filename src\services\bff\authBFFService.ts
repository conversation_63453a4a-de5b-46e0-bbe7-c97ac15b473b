import { LoginWithOtpDocument, LoginWithOtpMutation, LoginWithOtpMutationVariables, RequestLoginOtpDocument, RequestLoginOtpMutation, RequestLoginOtpMutationVariables } from "@/gql/graphql";
import apolloClient from "@/services/system/apolloClient";
import { logService } from "@/services/system/logService";
import { LoginRequest, LoginResponse, OtpRequest, OtpResponse } from "@/services/core/auth/authService";


export class AuthBFFService {
  /**
 * 發送驗證碼 (GraphQL 版本)
 * @param request 驗證碼請求
 * @returns 驗證碼響應
 */
  public async sendOtpGraphql(request: OtpRequest): Promise<OtpResponse> {
    try {
      // 使用 Apollo Client 執行 GraphQL mutation
      const { data } = await apolloClient.mutate<RequestLoginOtpMutation, RequestLoginOtpMutationVariables>({
        mutation: RequestLoginOtpDocument,
        variables: {
          requestLoginOtpRequest2: {
            countryCode: request.countryCode,
            mobile: request.mobile,
          },
        },
      });

      // 根據 BFF 的回傳數據處理結果
      if (data?.requestLoginOtp.success) {
        logService.info('驗證碼發送成功，獲得 onceToken', {
          onceToken: data.requestLoginOtp.data?.onceToken,
          validSecond: data.requestLoginOtp.data?.validSecond
        });
        return {
          ...data.requestLoginOtp,
          code: data.requestLoginOtp.code || undefined,
          timeCost: data.requestLoginOtp.timeCost || undefined,
          data: data.requestLoginOtp.data || undefined
        };
      } else {
        throw new Error(data?.requestLoginOtp.msg || '發送驗證碼失敗');
      }
    } catch (err: any) {
      logService.error('GraphQL 發送驗證碼失敗', { error: err });
      throw new Error(err?.message || '發送驗證碼失敗');
    }
  };

  /**
   * GraphQL 登錄方法
   * @param request 登錄請求
   * @returns 登錄響應
   */
  public async loginWithOtp(request: LoginRequest): Promise<LoginResponse> {
    try {
      // 驗證必需參數
      if (!request.onceToken) {
        throw new Error('onceToken 是必需的');
      }
      if (!request.checkCode) {
        throw new Error('checkCode 是必需的');
      }

      // 使用 Apollo Client 執行 GraphQL mutation
      const { data } = await apolloClient.mutate<LoginWithOtpMutation, LoginWithOtpMutationVariables>({
        mutation: LoginWithOtpDocument,
        variables: {
          request: {
            onceToken: request.onceToken,
            checkCode: request.checkCode,
          }
        },
        errorPolicy: 'all' // 允許部分數據返回，即使有字段錯誤
      });

      // 根據 BFF 的回傳數據處理結果
      if (data?.loginWithOtp.success) {
        logService.info('GraphQL 登錄成功', {
          accountId: data.loginWithOtp.data?.accountId,
          loginType: data.loginWithOtp.data?.loginType,
          tokenId: data.loginWithOtp.data?.tokenId
        });

        // 轉換 GraphQL LoginData 為 API AccountResponse 格式
        const loginData = data.loginWithOtp.data;
        const accountResponse = loginData ? {
          accountId: loginData.accountId,
          name: loginData.name || undefined,
          countryCode: loginData.countryCode || undefined,
          mobile: loginData.mobile || undefined,
          type: loginData.loginType,
          onlineId: loginData.onlineId || undefined,
          tokenId: loginData.tokenId,
          systemRoomId: loginData.systemRoomId || undefined,
          personRoomId: loginData.personRoomId,
          systemAccountId: loginData.systemAccountId
        } : undefined;

        return {
          code: data.loginWithOtp.code || undefined,
          msg: data.loginWithOtp.msg,
          status: data.loginWithOtp.status,
          success: data.loginWithOtp.success,
          data: accountResponse
        };
      } else {
        throw new Error(data?.loginWithOtp.msg || '登錄失敗');
      }
    } catch (err: any) {
      // 特殊處理 GraphQL schema 錯誤
      if (err?.message?.includes('Cannot return null for non-nullable field')) {
        logService.warn('GraphQL schema 不匹配錯誤，可能是後端數據問題', { 
          error: err?.message,
          suggestion: '請檢查後端數據完整性，特別是 TenantRelation.bossServiceNumber 字段'
        });
        throw new Error('服務器數據錯誤，請聯繫技術支援。錯誤詳情：部分租戶關係數據不完整');
      }
      
      logService.error('GraphQL 登錄失敗', { error: err });
      throw new Error(err?.message || '登錄失敗');
    }
  }
}

export const authBFFService = new AuthBFFService();
export default authBFFService;