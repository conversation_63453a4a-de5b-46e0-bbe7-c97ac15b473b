import { getLastMessageSummary } from './messageUtil';

// 模擬 i18next 的 t 函數
jest.mock('i18next', () => ({
  t: (key: string) => {
    const translations: Record<string, string> = {
      'message.image': '[圖片]',
      'message.audio': '[語音]',
      'message.video': '[影片]',
      'message.file': '[檔案]',
      'message.unknown': '[未知消息]'
    };
    return translations[key] || key;
  }
}));

// 模擬 ConstantUtil
jest.mock('./constantUtil', () => ({
  ConstantUtil: {
    MESSAGE_TYPE_TEXT: 'text',
    MESSAGE_TYPE_IMAGE: 'image',
    MESSAGE_TYPE_AUDIO: 'audio',
    MESSAGE_TYPE_VIDEO: 'video',
    MESSAGE_TYPE_FILE: 'file'
  }
}));

describe('getLastMessageSummary', () => {
  test('應該處理空值和未定義值', () => {
    expect(getLastMessageSummary(null)).toBe('');
    expect(getLastMessageSummary(undefined)).toBe('');
    expect(getLastMessageSummary('')).toBe('');
  });

  test('應該處理純字符串（非JSON）', () => {
    expect(getLastMessageSummary('Hello World')).toBe('Hello World');
    expect(getLastMessageSummary('這是一條普通消息')).toBe('這是一條普通消息');
  });

  test('應該處理有效的JSON字符串', () => {
    const validJson = JSON.stringify({
      type: 'text',
      content: JSON.stringify({ text: '這是一條文本消息' })
    });
    expect(getLastMessageSummary(validJson)).toBe('這是一條文本消息');
  });

  test('應該處理來自MessageTab的錯誤處理對象', () => {
    const errorObject = { text: '這是錯誤處理的消息' };
    expect(getLastMessageSummary(errorObject)).toBe('這是錯誤處理的消息');
  });

  test('應該處理不同類型的消息', () => {
    const imageMessage = {
      type: 'image',
      content: JSON.stringify({ url: 'image.jpg' })
    };
    expect(getLastMessageSummary(imageMessage)).toBe('[圖片]');

    const audioMessage = {
      type: 'audio',
      content: JSON.stringify({ url: 'audio.mp3' })
    };
    expect(getLastMessageSummary(audioMessage)).toBe('[語音]');
  });

  test('應該處理無效的對象', () => {
    expect(getLastMessageSummary(123)).toBe('123');
    expect(getLastMessageSummary(true)).toBe('true');
    expect(getLastMessageSummary(false)).toBe('false');
  });

  test('應該處理複雜對象', () => {
    const complexObject = {
      type: 'unknown',
      content: 'some content',
      other: 'properties'
    };
    expect(getLastMessageSummary(complexObject)).toBe('[未知消息]');
  });

  test('應該確保始終返回字符串', () => {
    const testCases = [
      null,
      undefined,
      '',
      'string',
      123,
      true,
      false,
      {},
      { text: 'test' },
      { type: 'text', content: '{"text":"test"}' }
    ];

    testCases.forEach(testCase => {
      const result = getLastMessageSummary(testCase);
      expect(typeof result).toBe('string');
    });
  });
});
