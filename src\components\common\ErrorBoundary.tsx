import React, { Component, ReactNode } from 'react';
import { Button, ErrorBlock } from 'antd-mobile';
import { logService } from '../../services/system/logService';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * React 錯誤邊界組件
 * 捕獲子組件樹中的 JavaScript 錯誤，並渲染備用 UI
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新 state 使下一次渲染能夠顯示降級後的 UI
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 記錄錯誤資訊
    logService.error('React 錯誤邊界捕獲到錯誤', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: 'ErrorBoundary'
    });

    // 更新錯誤狀態
    this.setState({
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    // 重置錯誤狀態
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined
    });
    
    logService.info('用戶點擊重試，重置錯誤邊界狀態');
  };

  handleReload = () => {
    // 重新載入頁面
    logService.info('用戶點擊重新載入頁面');
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // 如果有自定義的錯誤 UI，使用自定義的
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 預設的錯誤 UI
      return (
        <div className="error-boundary-container" style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          padding: '20px',
          backgroundColor: '#f5f5f5'
        }}>
          <ErrorBlock
            status="default"
            title="應用程式發生錯誤"
            description="很抱歉，應用程式遇到了意外錯誤。請嘗試重新整理頁面或聯繫技術支援。"
          />
          
          <div style={{ 
            marginTop: '24px', 
            display: 'flex', 
            gap: '12px',
            flexDirection: 'column',
            width: '100%',
            maxWidth: '300px'
          }}>
            <Button 
              color="primary" 
              onClick={this.handleRetry}
              block
            >
              重試
            </Button>
            <Button 
              onClick={this.handleReload}
              block
            >
              重新整理頁面
            </Button>
          </div>

          {/* 開發環境下顯示詳細錯誤資訊 */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details style={{ 
              marginTop: '24px',
              padding: '16px',
              backgroundColor: '#fff',
              border: '1px solid #d9d9d9',
              borderRadius: '8px',
              width: '100%',
              maxWidth: '600px',
              fontSize: '12px',
              color: '#666'
            }}>
              <summary style={{ 
                cursor: 'pointer',
                fontWeight: 'bold',
                marginBottom: '8px',
                color: '#ff4d4f'
              }}>
                錯誤詳情 (僅開發環境顯示)
              </summary>
              <pre style={{ 
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                margin: 0
              }}>
                {this.state.error.toString()}
                {this.state.errorInfo?.componentStack}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
} 