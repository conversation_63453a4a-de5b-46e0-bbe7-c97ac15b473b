/**
 * 调试工具函数
 * 用于在开发环境中调试分页和消息加载问题
 */

import { store } from '../app/store';
import { messageService } from '../services';
import { ConstantUtil } from './constantUtil';

/**
 * 调试房间分页状态
 */
export const debugRoomPagination = async (roomId: string) => {
  if (!roomId) {
    console.error('❌ roomId 不能为空');
    return;
  }

  const state = store.getState();
  const roomState = state.message.rooms[roomId];

  console.group(`🔍 房间分页状态调试 - ${roomId}`);
  
  // 1. Redux 状态
  console.log('📊 Redux 状态:', {
    存在: !!roomState,
    currentPage: roomState?.currentPage,
    hasNextPage: roomState?.hasNextPage,
    isLoading: roomState?.isLoading,
    lastSequence: roomState?.lastSequence,
    消息组数量: roomState?.dateGroups?.length || 0,
    总消息数: roomState?.dateGroups?.reduce((total, group) => total + group.messages.length, 0) || 0
  });

  if (!roomState) {
    console.warn('⚠️ 房间状态不存在，可能需要先初始化');
    console.groupEnd();
    return;
  }

  // 2. 数据库中的消息总数
  try {
    console.log('🗃️ 正在查询数据库...');
    
    // 查询总消息数
    const allMessages = await messageService.getMessagesFromDB(roomId, 0, 1000, undefined, 'desc');
    console.log('📈 数据库统计:', {
      总消息数: allMessages.length,
      最早消息: allMessages.length > 0 ? new Date(Math.min(...allMessages.map(m => m.sendTime || 0))).toLocaleString() : '无',
      最晚消息: allMessages.length > 0 ? new Date(Math.max(...allMessages.map(m => m.sendTime || 0))).toLocaleString() : '无'
    });

    // 3. 当前页和下一页的消息
    const currentPageMessages = await messageService.getMessagesFromDB(
      roomId, 
      roomState.currentPage, 
      ConstantUtil.DEFAULT_PAGE_SIZE, 
      undefined, 
      'desc'
    );
    
    const nextPageMessages = await messageService.getMessagesFromDB(
      roomId, 
      roomState.currentPage + 1, 
      ConstantUtil.DEFAULT_PAGE_SIZE, 
      undefined, 
      'desc'
    );

    console.log('📄 分页详情:', {
      当前页: roomState.currentPage,
      当前页消息数: currentPageMessages.length,
      下一页消息数: nextPageMessages.length,
      页面大小: ConstantUtil.DEFAULT_PAGE_SIZE,
      理论上hasNextPage: nextPageMessages.length > 0,
      实际hasNextPage: roomState.hasNextPage,
      状态是否一致: (nextPageMessages.length > 0) === roomState.hasNextPage
    });

    // 4. 分页计算验证
    const totalPages = Math.ceil(allMessages.length / ConstantUtil.DEFAULT_PAGE_SIZE);
    console.log('🧮 分页计算:', {
      总消息数: allMessages.length,
      页面大小: ConstantUtil.DEFAULT_PAGE_SIZE,
      理论总页数: totalPages,
      当前页: roomState.currentPage,
      是否还有下一页: roomState.currentPage < totalPages - 1,
      已加载页数: roomState.currentPage + 1
    });

    // 5. 问题诊断
    const issues = [];
    if (nextPageMessages.length > 0 && !roomState.hasNextPage) {
      issues.push('❌ 数据库中还有下一页消息，但 hasNextPage=false');
    }
    if (nextPageMessages.length === 0 && roomState.hasNextPage) {
      issues.push('⚠️ 数据库中没有下一页消息，但 hasNextPage=true');
    }
    if (roomState.currentPage >= totalPages && roomState.hasNextPage) {
      issues.push('⚠️ 当前页已超过总页数，但 hasNextPage=true');
    }

    if (issues.length > 0) {
      console.warn('🚨 发现问题:', issues);
    } else {
      console.log('✅ 分页状态正常');
    }

  } catch (error) {
    console.error('❌ 查询数据库失败:', error);
  }



  console.groupEnd();
};

/**
 * 强制重置房间分页状态
 */
export const resetRoomPagination = (roomId: string) => {
  if (!roomId) {
    console.error('❌ roomId 不能为空');
    return;
  }

  console.log(`🔄 重置房间分页状态 - ${roomId}`);
  
  // 这里需要导入 action，但为了避免循环依赖，我们通过 store.dispatch 调用
  store.dispatch({
    type: 'message/updateRoomMessageStatus',
    payload: {
      roomId,
      currentPage: 0,
      hasNextPage: true,
      lastSequence: 0
    }
  });

  console.log('✅ 分页状态已重置');
};



/**
 * 测试API请求参数计算逻辑
 */
export const testApiParameterLogic = () => {
  console.group('🔢 API请求参数计算逻辑测试');

  const pageSize = ConstantUtil.DEFAULT_PAGE_SIZE;

  // 测试用例
  const testCases = [
    { currentSequence: 100, type: '获取最新消息', expected: 101 },
    { currentSequence: 100, type: '获取历史消息', expected: Math.max(1, 100 - pageSize + 1) },
    { currentSequence: 50, type: '获取历史消息', expected: Math.max(1, 50 - pageSize + 1) },
    { currentSequence: 10, type: '获取历史消息', expected: Math.max(1, 10 - pageSize + 1) },
    { currentSequence: 1, type: '获取历史消息', expected: Math.max(1, 1 - pageSize + 1) },
  ];

  console.log(`页面大小: ${pageSize}`);
  console.log('');

  testCases.forEach(({ currentSequence, type, expected }) => {
    if (type === '获取最新消息') {
      const result = currentSequence + 1;
      console.log(`${type}: ${currentSequence} + 1 = ${result} ${result === expected ? '✅' : '❌'}`);
    } else {
      const result = Math.max(1, currentSequence - pageSize + 1);
      console.log(`${type}: max(1, ${currentSequence} - ${pageSize} + 1) = ${result} ${result === expected ? '✅' : '❌'}`);
    }
  });

  console.log('');
  console.log('📝 规则说明:');
  console.log('- 获取最新消息: lastSequence + 1');
  console.log('- 获取历史消息: max(1, lastSequence - pageSize + 1)');

  console.groupEnd();
};

// 将调试函数添加到全局对象，方便在控制台使用
if (typeof window !== 'undefined') {
  (window as any).debugRoomPagination = debugRoomPagination;
  (window as any).resetRoomPagination = resetRoomPagination;
  (window as any).testApiParameterLogic = testApiParameterLogic;
}

/**
 * 手动触发加载更多消息
 */
export const manualLoadMore = (roomId: string) => {
  if (!roomId) {
    console.error('❌ roomId 不能为空');
    return;
  }

  console.log(`📥 手动触发加载更多消息 - ${roomId}`);
  
  store.dispatch({
    type: 'message/loadMoreMessages',
    payload: roomId
  } as any);
};

// 在开发环境中添加 manualLoadMore 到全局
if (typeof window !== 'undefined') {
  (window as any).manualLoadMore = manualLoadMore;
}
