/* Home页面骨架屏样式 */
.home-skeleton {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #E4F4FD;
  position: relative;
}

/* 顶部进度条样式 */
.skeleton-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  z-index: 1001;
  overflow: hidden;
}

.skeleton-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1677FF, #4096FF, #1677FF);
  background-size: 200% 100%;
  animation: progress-loading 2s ease-in-out infinite;
  width: 100%;
  transform: translateX(-100%);
  animation: progress-slide 3s ease-in-out infinite;
}

@keyframes progress-slide {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes progress-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Header 骨架样式 */
.home-skeleton-header {
  background-color: #E4F4FD;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.home-skeleton-header.user-info-hidden {
  padding-top: 48px;
}

/* User Info 骨架样式 */
.home-skeleton-user-info {
  display: flex;
  align-items: center;
  padding: 4px 16px;
  padding-top: 4px;
  gap: 12px;
  max-height: 40px;
  overflow: hidden;
}

.skeleton-avatar {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-user-details {
  flex: 1;
}

.skeleton-points {
  display: flex;
  align-items: center;
  gap: 4px;
}

.skeleton-points-icon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Nav Bar 骨架样式 */
.home-skeleton-nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 12px;
}

.skeleton-nav-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-dropdown-icon {
  width: 12px;
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-nav-right {
  display: flex;
  gap: 16px;
}

.skeleton-nav-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Content 骨架样式 */
.home-skeleton-content {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
}

/* Filter Tabs 骨架样式 */
.home-skeleton-filter-tabs {
  display: flex;
  padding: 16px;
  gap: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-filter-tab {
  padding: 8px 0;
  position: relative;
}

.skeleton-filter-tab-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1677FF 25%, #4096FF 50%, #1677FF 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* List 骨架样式 */
.home-skeleton-list {
  padding: 0 16px;
}

.home-skeleton-list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.skeleton-item-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  flex-shrink: 0;
}

.skeleton-item-content {
  flex: 1;
  min-width: 0;
}

.skeleton-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.skeleton-item-badge {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(90deg, #ff4d4f 25%, #ff7875 50%, #ff4d4f 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  flex-shrink: 0;
}

/* Tab Bar 骨架样式 */
.home-skeleton-tab-bar {
  display: flex;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  padding: 8px 0;
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
}

.skeleton-tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
}

.skeleton-tab-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 通用骨架线条样式 */
.skeleton-line {
  height: 14px;
  border-radius: 7px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 6px;
}

.skeleton-line:last-child {
  margin-bottom: 0;
}

.skeleton-line-mini {
  width: 30px;
}

.skeleton-line-short {
  width: 60px;
}

.skeleton-line-medium {
  width: 120px;
}

.skeleton-line-long {
  width: 80%;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式调整 */
@media (max-width: 375px) {
  .home-skeleton-header {
    padding: 12px;
  }
  
  .home-skeleton-list {
    padding: 0 12px;
  }
  
  .home-skeleton-filter-tabs {
    padding: 12px;
    gap: 16px;
  }
}
