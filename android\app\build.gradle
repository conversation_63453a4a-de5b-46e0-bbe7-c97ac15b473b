apply plugin: 'com.android.application'

android {
    namespace "cloud.aile.aile"
    compileSdk rootProject.ext.compileSdkVersion
    defaultConfig {
        applicationId "cloud.aile.aile"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 2
        versionName "5.0.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }

        def formattedDate = new Date().format('yyyyMMdd-HHmm')
        archivesBaseName = "aile-v${versionName}-${versionCode}-${formattedDate}"


        // 添加 OAuth 回調 scheme 配置
        manifestPlaceholders = [
            appAuthRedirectScheme: 'cloud.aile.aile'
        ]
    }
    // 定义签名配置
    signingConfigs {
        release {
            // keystore 文件路径
            storeFile file("new-aile-key.jks")
            // keystore 密码
            storePassword "77066188"
            // 密钥别名
            keyAlias "new-aile-key"
            // 密钥密码
            keyPassword "77066188"
        }
        // 你也可以为 debug 构建配置签名，但通常不建议使用发布密钥进行 debug 签名
        debug {
            // 对于 debug 构建，Android Studio 会自动生成一个 debug.keystore
            // 你可以通过以下方式引用它，或者在开发环境中不显式配置，让系统使用默认的 debug 签名
            // storeFile file("path/to/debug.keystore")
            // storePassword "android"
            // keyAlias "androiddebugkey"
            // keyPassword "android"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            // 使用 release 签名配置
            signingConfig signingConfigs.release
        }
        debug {
            // 使用 debug 签名配置
            signingConfig signingConfigs.debug
        }
    }

    // 支持 16KB 页面大小的配置
    packagingOptions {
        jniLibs {
            useLegacyPackaging = false
        }
        // 确保 native 库使用正确的对齐方式
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }

    // 添加对 16KB 页面大小的支持
    android.applicationVariants.all { variant ->
        variant.outputs.all { output ->
            // 确保 APK 对齐到 16KB
            output.outputFileName = output.outputFileName.replace('.apk', '-16kb-aligned.apk')
        }
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
}

apply from: 'capacitor.build.gradle'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.info("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}
