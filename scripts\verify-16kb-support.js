#!/usr/bin/env node

/**
 * 验证 APK 是否支持 16KB 页面大小
 * 
 * 这个脚本检查生成的 APK 是否符合 Android 15+ 的 16KB 页面大小要求
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 验证 APK 16KB 页面大小支持...');

function findApkFiles() {
  const apkDir = 'android/app/build/outputs/apk/release';
  
  if (!fs.existsSync(apkDir)) {
    console.error('❌ APK 目录不存在，请先构建 APK');
    process.exit(1);
  }
  
  const apkFiles = fs.readdirSync(apkDir).filter(file => file.endsWith('.apk'));
  
  if (apkFiles.length === 0) {
    console.error('❌ 未找到 APK 文件，请先构建 APK');
    process.exit(1);
  }
  
  return apkFiles.map(file => path.join(apkDir, file));
}

function verifyApk(apkPath) {
  console.log(`\n📱 验证 APK: ${path.basename(apkPath)}`);
  
  try {
    // 使用 aapt 检查 APK 信息
    const aaptOutput = execSync(`aapt dump badging "${apkPath}"`, { encoding: 'utf8' });
    
    // 检查关键信息
    const checks = [
      {
        name: '16KB 页面大小支持',
        pattern: /android\.supports_16kb_page_size/,
        required: true
      },
      {
        name: '目标 SDK 版本',
        pattern: /targetSdkVersion:'(\d+)'/,
        required: true,
        validator: (match) => {
          const version = parseInt(match[1]);
          return version >= 35 ? `✅ ${version} (支持 Android 15+)` : `⚠️  ${version} (建议升级到 35+)`;
        }
      },
      {
        name: '最小 SDK 版本',
        pattern: /minSdkVersion:'(\d+)'/,
        required: true,
        validator: (match) => {
          const version = parseInt(match[1]);
          return `ℹ️  ${version}`;
        }
      }
    ];
    
    let allPassed = true;
    
    checks.forEach(check => {
      const match = aaptOutput.match(check.pattern);
      
      if (match) {
        if (check.validator) {
          console.log(`  ${check.name}: ${check.validator(match)}`);
        } else {
          console.log(`  ✅ ${check.name}: 已配置`);
        }
      } else if (check.required) {
        console.log(`  ❌ ${check.name}: 未找到`);
        allPassed = false;
      } else {
        console.log(`  ⚠️  ${check.name}: 未配置`);
      }
    });
    
    // 检查权限
    const permissions = aaptOutput.match(/uses-permission: name='([^']+)'/g);
    if (permissions) {
      console.log(`  📋 权限数量: ${permissions.length}`);
    }
    
    // 检查 native 库
    try {
      const unzipOutput = execSync(`unzip -l "${apkPath}" | grep -E "lib/.*\\.so$"`, { encoding: 'utf8' });
      const nativeLibs = unzipOutput.split('\n').filter(line => line.includes('.so'));
      if (nativeLibs.length > 0) {
        console.log(`  📚 Native 库数量: ${nativeLibs.length}`);
        
        // 检查是否有常见的对齐问题库
        const problematicLibs = nativeLibs.filter(lib => 
          lib.includes('libc++_shared.so') || 
          lib.includes('libjsc.so')
        );
        
        if (problematicLibs.length > 0) {
          console.log(`  ⚠️  发现可能需要特殊处理的库: ${problematicLibs.length}`);
        }
      }
    } catch (error) {
      console.log(`  ℹ️  无法检查 native 库信息`);
    }
    
    return allPassed;
    
  } catch (error) {
    console.error(`  ❌ 验证失败: ${error.message}`);
    return false;
  }
}

function main() {
  try {
    const apkFiles = findApkFiles();
    let allApksValid = true;
    
    apkFiles.forEach(apkPath => {
      const isValid = verifyApk(apkPath);
      allApksValid = allApksValid && isValid;
    });
    
    console.log('\n' + '='.repeat(50));
    
    if (allApksValid) {
      console.log('🎉 所有 APK 都通过了 16KB 页面大小支持验证！');
      console.log('✅ 可以安全地提交到 Google Play Store');
    } else {
      console.log('❌ 部分 APK 未通过验证');
      console.log('⚠️  请检查配置并重新构建');
      process.exit(1);
    }
    
    console.log('\n📋 建议:');
    console.log('  1. 在真实的 Android 15+ 设备上测试应用');
    console.log('  2. 使用 Google Play Console 的预发布报告');
    console.log('  3. 监控应用在不同设备上的性能表现');
    
  } catch (error) {
    console.error('❌ 验证过程出错:', error.message);
    process.exit(1);
  }
}

main();
