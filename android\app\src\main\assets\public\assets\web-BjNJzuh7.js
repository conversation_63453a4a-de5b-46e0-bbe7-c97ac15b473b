import{W as n,L as i,C as r,E as s,B as o}from"./index-CzeWrtPj.js";class h extends n{constructor(){super(...arguments),this._isSupported="BarcodeDetector"in window,this.errorVideoElementMissing="videoElement must be provided.",this.eventBarcodesScanned="barcodesScanned"}async startScan(e){if(!this._isSupported)throw this.createUnavailableException();if(!e?.videoElement)throw new Error(this.errorVideoElementMissing);this.videoElement=e.videoElement,this.stream=await navigator.mediaDevices.getUserMedia({video:{facingMode:{ideal:e?.lensFacing===i.Front?"user":"environment"}},audio:!1}),e.videoElement.srcObject=this.stream,await e.videoElement.play();const a=new BarcodeDetector;this.intervalId=window.setInterval(async()=>{if(!e.videoElement)return;const t=await a.detect(e.videoElement);t.length!==0&&this.handleScannedBarcodes(t)},500)}async stopScan(){if(!this._isSupported)throw this.createUnavailableException();this.intervalId&&(clearInterval(this.intervalId),this.intervalId=void 0),this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=void 0),this.videoElement&&(this.videoElement.srcObject=null,this.videoElement=void 0)}async readBarcodesFromImage(e){throw this.createUnavailableException()}async scan(){throw this.createUnavailableException()}async isSupported(){return{supported:this._isSupported}}async enableTorch(){throw this.createUnavailableException()}async disableTorch(){throw this.createUnavailableException()}async toggleTorch(){throw this.createUnavailableException()}async isTorchEnabled(){throw this.createUnavailableException}async isTorchAvailable(){throw this.createUnavailableException()}async setZoomRatio(e){throw this.createUnavailableException()}async getZoomRatio(){throw this.createUnavailableException()}async getMinZoomRatio(){throw this.createUnavailableException()}async getMaxZoomRatio(){throw this.createUnavailableException()}async openSettings(){throw this.createUnavailableException()}async isGoogleBarcodeScannerModuleAvailable(){throw this.createUnavailableException()}async installGoogleBarcodeScannerModule(){throw this.createUnavailableException()}async checkPermissions(){try{return{camera:(await navigator.permissions.query({name:"camera"})).state}}catch{return{camera:"prompt"}}}async requestPermissions(){try{return(await navigator.mediaDevices.getUserMedia({video:!0})).getTracks().forEach(a=>a.stop()),{camera:"granted"}}catch{return{camera:"denied"}}}createUnavailableException(){return new r("This plugin method is not available on this platform.",s.Unavailable)}handleScannedBarcodes(e){const a={barcodes:e.map(t=>({cornerPoints:[[t.cornerPoints[0].x,t.cornerPoints[0].y],[t.cornerPoints[1].x,t.cornerPoints[1].y],[t.cornerPoints[2].x,t.cornerPoints[2].y],[t.cornerPoints[3].x,t.cornerPoints[3].y]],displayValue:t.rawValue,rawValue:t.rawValue,format:t.format.toUpperCase(),valueType:o.Unknown}))};this.notifyListeners(this.eventBarcodesScanned,a)}}export{h as BarcodeScannerWeb};
