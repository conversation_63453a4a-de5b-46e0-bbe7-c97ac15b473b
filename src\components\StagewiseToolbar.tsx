import React from 'react';
import { StagewiseToolbar as StagewiseToolbarComponent } from '@stagewise/toolbar-react';
import stagewiseConfig from '../config/stagewise.config';

/**
 * Stagewise工具栏组件
 * 仅在开发环境下显示
 */
const StagewiseToolbar: React.FC = () => {
  // 检查是否在开发环境中
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (!isDevelopment) {
    return null;
  }

  return <StagewiseToolbarComponent config={stagewiseConfig} />;
};

export default StagewiseToolbar; 